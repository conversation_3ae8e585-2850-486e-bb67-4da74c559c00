const logger = require("../../logger").logger;
const moment = require("moment");
const _ = require("underscore");
const dashboardData = require("./../dataLayer/pv2DashboardData")


/**
 * @description Fetching Request Aging Data
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getSkillRequestData = async(db,params,user,authorization)=>{
    try{

        const inlineFilter = params?.inlineFilterData?.length > 0 ? params.inlineFilterData[0] : null;
        const defaultFilterId = inlineFilter?.default_filter_id ?? null;
        let inline_filter_config = null;

        if (inlineFilter?.filter_data?.length > 0) {
            inline_filter_config = inlineFilter.filter_data.find(i => i.id === defaultFilterId) || null;
        }
        let series_data = [];
        // Ensure function exists before calling it dynamically
        if (inline_filter_config?.function && typeof dashboardData[inline_filter_config.function] === "function") {
            series_data = await dashboardData[inline_filter_config.function](db, user, authorization);
        } else {
            return Promise.resolve({
                messType:"E", 
                error: inline_filter_config ? inline_filter_config : 'Inline Filter Error', 
                message:"Error in Handling Inline Filter!"
            })
        }

        if(!series_data || series_data['messType'] == 'E' || series_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: series_data ? series_data : 'NO Priority Master', 
                message:"NO Priority Master!"
            })
        }
        series_data = series_data['data']
        
        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        let status_list = []

        let get_request_data = await dashboardData.getRequestDataWithSkills(db,start_date,end_date,status_list,params?.filterQuery);
        if(!get_request_data || get_request_data['messType'] == 'E' || get_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                requestData: [],
                seriesData: series_data,
                count: 0,
                error: get_request_data ? get_request_data : 'Error in Fetching Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }
        
        const requestData = get_request_data?.data;

        // Initialize skillData dynamically with priority keys
        const priorityKeys = series_data.map(priority => priority.key);
        let skillMap = new Map();

        requestData.forEach(request => {
            // Check if the skill_id already exists in the Map
            if (!skillMap.has(request.skill_id)) {
                // Initialize a new skill entry
                let rangeData = { 
                    label: request.skill_name, 
                    id: request.skill_id 
                };
                // Initialize priorityKeys counts to 0
                priorityKeys.forEach(key => {
                    rangeData[key] = 0;
                });
                // Add the new skill to the Map
                skillMap.set(request.skill_id, rangeData);
            }

            // Process the request and update counts
            const rangeData = skillMap.get(request.skill_id);
            const priority = series_data.find(p => p.id === request[inline_filter_config?.key]);
            if (priority != undefined) {
                rangeData[priority.key] = (rangeData[priority.key] || 0) + 1;
            }
        });

        // Convert the Map to an array
        let skillMasterData = Array.from(skillMap.values());
        
        // Return formatted response
        return Promise.resolve({
            messType: "S",
            requestData: skillMasterData,
            seriesData: series_data,
            count: skillMasterData?.length
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, message:"Error while fetching Aging Request Data!"})
    }
}

/**
 * @description Fetching Aging Request Data
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getAgingRequestData = async(db,params,user,authorization)=>{
    try{

        const inlineFilter = params?.inlineFilterData?.length > 0 ? params.inlineFilterData[0] : null;
        const defaultFilterId = inlineFilter?.default_filter_id ?? null;
        let inline_filter_config = null;

        if (inlineFilter?.filter_data?.length > 0) {
            inline_filter_config = inlineFilter.filter_data.find(i => i.id === defaultFilterId) || null;
        }
        let series_data = [];
        // Ensure function exists before calling it dynamically
        if (inline_filter_config?.function && typeof dashboardData[inline_filter_config.function] === "function") {
            series_data = await dashboardData[inline_filter_config.function](db, user, authorization);
        } else {
            return Promise.resolve({
                messType:"E", 
                error: inline_filter_config ? inline_filter_config : 'Inline Filter Error', 
                message:"Error in Handling Inline Filter!"
            })
        }

        if(!series_data || series_data['messType'] == 'E' || series_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: series_data ? series_data : 'No Priority Master', 
                message:"No Priority Master!"
            })
        }
        series_data = series_data['data']
        if(inline_filter_config?.function == 'getStatusMasterData'){
            series_data = series_data.filter(item => ![2, 4, 5,7].includes(item.id));
        }
        
        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        let status_list = [1,3,6]

        let get_request_data = await dashboardData.getRequestData(db,start_date,end_date,status_list,params?.filterQuery);
        if(!get_request_data  || get_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S",
                requestData: [],
                seriesData: series_data,
                count: 0,
                error: get_request_data ? get_request_data : 'Error in Fetching Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }
        
        const requestData = get_request_data?.data;

        // Calculate maximum age
        const currentDate = new Date();
        const maxAge = Math.max(
            ...requestData.map(req => Math.floor((currentDate - new Date(req.created_on)) / (1000 * 60 * 60 * 24)))
        );

        // Generate dynamic age ranges
        const maxRange = 90;
        const rangeStep = 10;
        const ageRanges = [];

        // Generate age ranges up to 90
        for (let i = 0; i <= maxRange; i += rangeStep) {
            ageRanges.push({
                label: i + rangeStep > maxRange ? `>${maxRange}` : `${i}-${i + rangeStep}`,
                min: i,
                max: i + rangeStep > maxRange ? Infinity : i + rangeStep
            });
        }

        // Initialize agingData dynamically with priority keys
        const priorityKeys = series_data.map(priority => priority.key);
        let agingData = ageRanges.map(range => {
            let rangeData = { label: range.label, min: range.min, max: range.max };
            priorityKeys.forEach(key => {
                rangeData[key] = 0; // Initialize count to 0
            });
            return rangeData;
        });

        // Process each request
        requestData.forEach(request => {
            const creationDate = new Date(request.created_on);
            const ageInDays = Math.floor((currentDate - creationDate) / (1000 * 60 * 60 * 24));

            const range = agingData.find(r => ageInDays >= r.min && ageInDays < r.max);
           
            if (range != undefined) {
                const data = series_data.find(p => p.id === request[inline_filter_config?.key]);
                if (data) {
                    range[data.key] = (range[data.key] || 0) + 1;
                }
            }
        });

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            requestData: agingData,
            seriesData: series_data,
            count: requestData?.length
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, message:"Error while fetching Aging Request Data!"})
    }
}

module.exports.getSummaryOfRequest = async(db,params,user,authorization)=>{
    try{

        let request_status_data = await dashboardData.getRequestStatusData(db);
        if(!request_status_data || request_status_data['messType'] == 'E' || request_status_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: request_status_data ? request_status_data : 'Error in Fetching the Request Status Master', 
                message:"Error in Fetching the Request Status Master!"
            })
        }
        request_status_data = request_status_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;

        let get_all_request_data = await dashboardData.getAllRequestData(db,start_date,end_date,params?.filterQuery);
        if(!get_all_request_data || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S",
                data:[],
                error: get_all_request_data ? get_all_request_data : 'No Request Data', 
                message:"No Request Data!"
            })
        }

        const requestData = get_all_request_data?.data;
        
        let result = request_status_data.map(status => ({
            id: status?.id,
            label: status?.name, // Use label from master data
            count: 0,           // Initialize count to 0
            color: status?.color // Use color from master data
        }));

        let statusMap = {};
        request_status_data.forEach(status => {
            statusMap[status.id] = status.id; // Create a mapping of id to name
        });
        
        // Count occurrences of each status
        requestData.forEach(request => {
            const statusId = statusMap[request.status]; // Find the status name for the request
            if (statusId != undefined) {
                const statusObj = result.find(r => r.id === statusId); // Find the result object
                if (statusObj) {
                    statusObj.count += 1; // Increment the count
                }
            }
        });

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while fetching Summary Request Data!"})
    }
}

/**
 * @description Fetching Request Data Based on Client
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.requestBasedCustomer = async(db,params,user,authorization)=>{
    try{

        let customer_data = await dashboardData.getCustomerData(db);
        
        if(!customer_data || customer_data['messType'] == 'E' || customer_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: customer_data ? customer_data : 'No Customer Data', 
                message:"No Customer Data!"
            })
        }
        customer_data = customer_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        const inlineFilter = params?.inlineFilterData?.length > 0 ? params.inlineFilterData[0] : null;
        const defaultFilterId = inlineFilter?.default_filter_id ?? null;
        let inline_filter_config = null;
        let status = [];

        if (inlineFilter?.filter_data?.length > 0) {
            inline_filter_config = inlineFilter.filter_data.find(i => i.id === defaultFilterId) || null;
        }
        if (inline_filter_config && inline_filter_config?.value != 0) {
            status = [inline_filter_config.value];
        }
        
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);
        
        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                data: [],
                error: get_all_request_data ? get_all_request_data : 'No Request Data', 
                message:"No Request Data!"
            })
        }

        const requestData = get_all_request_data?.data;

        let customerMap = new Map(customer_data.map(data => [data.id, { id: data.id, label: data.name, value: 0 }]));

        // Count occurrences of each request
        requestData.forEach(request => {
            if (customerMap.has(request.customer)) {
                customerMap.get(request.customer).value += 1;
            }
        });

        // Filter customers with at least one request
        let result = Array.from(customerMap.values()).filter(customer => customer.value > 0);

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while fetching Request Based on Customer !"})
    }
}

/**
 * @description Fetching Request Data based on Project Type
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.requestBasedOnProjectType = async(db,params,user,authorization)=>{
    try{

        let project_type_data = await dashboardData.getProjectTypeData(db);
        
        if(!project_type_data || project_type_data['messType'] == 'E' || project_type_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: project_type_data ? project_type_data : 'Error in Fetching the Project Type Data', 
                message:"No Project Type Data!"
            })
        }
        project_type_data = project_type_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        const inlineFilter = params?.inlineFilterData?.length > 0 ? params.inlineFilterData[0] : null;
        const defaultFilterId = inlineFilter?.default_filter_id ?? null;
        let inline_filter_config = null;
        let status = [];

        if (inlineFilter?.filter_data?.length > 0) {
            inline_filter_config = inlineFilter.filter_data.find(i => i.id === defaultFilterId) || null;
        }
        if (inline_filter_config && inline_filter_config?.value != 0) {
            status = [inline_filter_config.value];
        }

        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);
        
        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"No Request Data!"
            })
        }

        const requestData = get_all_request_data?.data;
        
        let result = project_type_data.map(data => ({
            id: data?.id,
            label: data?.name, // Use label from master data
            value: 0,           // Initialize count to 0
        }));

        let projectMap = {};
        project_type_data.forEach(data => {
            projectMap[data.id] = data.id; // Create a mapping of id to name
        });
        
        // Count occurrences of each status
        requestData.forEach(request => {
            const statusId = projectMap[request.service_type_id]; // Find the status name for the request
            
            if (statusId != undefined) {
                const statusObj = result.find(r => r.id === statusId); // Find the result object
                if (statusObj) {
                    statusObj.value += 1; // Increment the count
                }
            }
        });

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while fetching Request Based on Project Type !"})
    }
}

/**
 * @description Fetching Request Data based on Project Role
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.requestBasedOnProjectRole = async(db,params,user,authorization)=>{
    try{

        let project_role_data = await dashboardData.getProjectRoleData(db);
        
        if(!project_role_data || project_role_data['messType'] == 'E' || project_role_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: project_role_data ? project_role_data : 'Error in Fetching the Project Role Data', 
                message:"No Project Role Data!"
            })
        }
        project_role_data = project_role_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        const inlineFilter = params?.inlineFilterData?.length > 0 ? params.inlineFilterData[0] : null;
        const defaultFilterId = inlineFilter?.default_filter_id ?? null;
        let inline_filter_config = null;
        let status = [];

        if (inlineFilter?.filter_data?.length > 0) {
            inline_filter_config = inlineFilter.filter_data.find(i => i.id === defaultFilterId) || null;
        }
        if (inline_filter_config && inline_filter_config?.value != 0) {
            status = [inline_filter_config.value];
        }
 
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);
        
        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }


        const requestData = get_all_request_data?.data;

        // Create a Map for role data
        let roleMap = new Map(project_role_data.map(data => [data.id, { id: data.id, label: data.name, value: 0 }]));

        // Count occurrences of each role in requestData
        requestData.forEach(request => {
            if (roleMap.has(request.project_role)) {
                roleMap.get(request.project_role).value += 1;
            }
        });

        // Filter roles with at least one request
        let result = Array.from(roleMap.values()).filter(role => role.value > 0);


        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while fetching Request Based on Project Role !"})
    }
}

/**
 * @description Open Request Summary Card Data
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.openRequestSummaryCard = async(db,params,user,authorization)=>{
    try{

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        let status = [1,2,3,4]
        let get_total_request_data = await dashboardData.getRequestData(db,start_date,end_date,[],params?.filterQuery);
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);

        if(!get_total_request_data || get_total_request_data['messType'] == 'E'){
            return Promise.resolve({
                messType:"E", 
                data:[],
                error: get_total_request_data ? get_total_request_data : 'Error in Fetching the Total Request Data', 
                message:"Error in Fetching the Total Request Data!"
            })
        }
        get_total_request_data = get_total_request_data['data'] ? get_total_request_data['data'] : [];
        if(!get_all_request_data || get_all_request_data['messType'] == 'E'){
            return Promise.resolve({
                messType:"E", 
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }
        get_all_request_data = get_all_request_data['data'] ? get_all_request_data['data'] : [];

        let percentage = 0;
        if(get_total_request_data?.length > 0){
            percentage = ((get_all_request_data?.length || 0) / (get_total_request_data?.length || 0))*100
        }
        let color;
        if(percentage >= 80)
            color = "#52C41A"
        else if(percentage < 80 && percentage > 40)
            color = "#FA8C16"
        else
            color = "#FF3A46"


        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:{
                total:`${get_all_request_data?.length || 0}/${get_total_request_data?.length || 0}`,
                percentage: (percentage).toFixed(2),
                total_count: get_total_request_data?.length || 0,
                color:color
            }
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description fetching the Onsite Offshore Summary Card Data
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.onsiteOffshoreSummaryCard = async(db,params,user,authorization)=>{
    try{

        let operational_model_data = await dashboardData.getOperationalModelData(db);

        if(!operational_model_data || operational_model_data['messType'] == 'E' || operational_model_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: operational_model_data ? operational_model_data : 'Error in Fetching the Operational Model Data', 
                message:"No Operational Model Data!"
            })
        }
        operational_model_data = operational_model_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        let status = []
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);

        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                total:0,
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }
        get_all_request_data = get_all_request_data['data'] ? get_all_request_data['data'] : [];

        let result = operational_model_data.map(data => ({
            id: data?.id,
            label: data?.name, // Use label from master data
            value: 0,           // Initialize count to 0
        }));

        let dataMap = {};
        operational_model_data.forEach(data => {
            dataMap[data.id] = data.id; // Create a mapping of id to name
        });
        
        // Count occurrences of each status
        get_all_request_data.forEach(request => {
            const statusId = dataMap[request.operation_model]; // Find the status name for the request
            
            if (statusId != undefined) {
                const statusObj = result.find(r => r.id === statusId); // Find the result object
                if (statusObj) {
                    statusObj.value += 1; // Increment the count
                }
            }
        });

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
            total:get_all_request_data?.length || 0
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description fetching the Client Interview Summary Card Data
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.clientInterviewSummaryCard = async(db,params,user,authorization)=>{
    try{

       let client_data = [
        {
            id: 1,
            name: 'Yes'
        },
        {
            id: 0,
            name: 'No'
        }
       ]

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        let status = []
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);

        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                total:0,
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }
        get_all_request_data = get_all_request_data['data'] ? get_all_request_data['data'] : [];

        let result = client_data.map(data => ({
            id: data?.id,
            label: data?.name, // Use label from master data
            value: 0,           // Initialize count to 0
        }));

        let dataMap = {};
        client_data.forEach(data => {
            dataMap[data.id] = data.id; // Create a mapping of id to name
        });
        
        // Count occurrences of each status
        get_all_request_data.forEach(request => {
            const statusId = dataMap[request.need_client_interview]; // Find the status name for the request

            if (statusId != undefined) {
                const statusObj = result.find(r => r.id === statusId); // Find the result object
                if (statusObj) {
                    statusObj.value += 1; // Increment the count
                }
            }
        });

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
            total:get_all_request_data?.length || 0
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.forwardRequestSummaryCard = async(db,params,user,authorization)=>{
    try{

        let operational_model_data = await dashboardData.getOperationalModelData(db);

        if(!operational_model_data || operational_model_data['messType'] == 'E' || operational_model_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: operational_model_data ? operational_model_data : 'Error in Fetching the Operational Model Data', 
                message:"No Operational Model Data!"
            })
        }
        operational_model_data = operational_model_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        let status = [2]
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);

        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                total:0,
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }
        get_all_request_data = get_all_request_data['data'] ? get_all_request_data['data'] : [];
        
        let result = operational_model_data.map(data => ({
            id: data?.id,
            label: data?.name, // Use label from master data
            value: 0,           // Initialize count to 0
        }));

        let dataMap = {};
        operational_model_data.forEach(data => {
            dataMap[data.id] = data.id; // Create a mapping of id to name
        });
        
        // Count occurrences of each status
        get_all_request_data.forEach(request => {
            const statusId = dataMap[request.operation_model]; // Find the status name for the request
            
            if (statusId != undefined) {
                const statusObj = result.find(r => r.id === statusId); // Find the result object
                if (statusObj) {
                    statusObj.value += 1; // Increment the count
                }
            }
        });

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
            total:get_all_request_data?.length || 0
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getDeployedHC = async(db,params,user,authorization)=>{
    try{

        let result = {
            percentage: 0,
            color: "#52C41A",
            total: 0
        }

        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getDeployedHC(db, filter_query, start_date, end_date);
        let project_employees = await dashboardData.getProjectEmployeesCount(db)

        result.total = data['messType'] === "S" ? data['data'] : 0;
        result.employee_count = project_employees['messType'] === "S" ? project_employees['data'] : 0;

        result.percentage = ((result.total/result.employee_count)*100).toFixed(2);;

        logger.info

        if(result.percentage >= 80)
            result.color = "#52C41A"
        else if(result.percentage < 80 && result.percentage > 40)
            result.color = "#FA8C16"
        else
            result.color = "#FF3A46"

        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}


/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getBillability = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getBillability(db, filter_query, start_date, end_date);

        if(data['messType'] === "S" && data['data']){
            data = data['data']
            result = {
                total: data['head_count'] ? data['head_count'] : 0,
                data:[
                {
                    label: "Billable",
                    value: data['billable'],
                    color:"#FFBD3D"
                },
                {
                    label: "Shadow",
                    value: data['shadow'],
                    color:"#1890FF"
                },
                {
                    label: "Non-Billable",
                    value: data['non_billable'],
                    color:"#13C2C2"
                },
                ]
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}


/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getBillabilityBasedOnRegion = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getBillabilityBasedOnRegion(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            logger.info("chart_data", chart_data)
            result = {
                total: chart_data.reduce((sum, item) => sum + item.value, 0),
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}


module.exports.getBillabilityBasedOnLocation = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getBillabilityBasedOnLocation(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            logger.info("chart_data", chart_data)
            result = {
                total: chart_data.reduce((sum, item) => sum + item.value, 0),
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getLocationOfDeployment = async(db,params,user,authorization)=>{
    try{

        let result = {};
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getBillabilityBasedOnRegion(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){

            let chart_data = data['data']
            const countryGroups = {
                NAM: ["United States", "Canada", "Mexico"],
                EMEA: [
                    "Albania", "Andorra", "Armenia", "Austria", "Azerbaijan", "Belarus", "Belgium", "Bosnia and Herzegovina", 
                    "Bulgaria", "Croatia", "Cyprus", "Czech Republic", "Denmark", "Estonia", "Finland", "France", "Georgia", 
                    "Germany", "Greece", "Hungary", "Iceland", "Ireland", "Italy", "Kazakhstan", "Kosovo", "Latvia", 
                    "Liechtenstein", "Lithuania", "Luxembourg", "Malta", "Moldova", "Monaco", "Montenegro", "Netherlands", 
                    "North Macedonia", "Norway", "Poland", "Portugal", "Romania", "Russia", "San Marino", "Serbia", "Slovakia", 
                    "Slovenia", "Spain", "Sweden", "Switzerland", "Ukraine", "United Kingdom", "Vatican", 
                    "Bahrain", "Cyprus", "Egypt", "Iran", "Iraq", "Israel", "Jordan", "Kuwait", "Lebanon", "Oman", "Palestine", 
                    "Qatar", "Saudi Arabia", "Syria", "Turkey", "United Arab Emirates", "Yemen",
                    "Algeria", "Angola", "Benin", "Botswana", "Burkina Faso", "Burundi", "Cape Verde", "Cameroon", 
                    "Central African Republic", "Chad", "Comoros", "Republic of the Congo", "Democratic Republic of the Congo", 
                    "Djibouti", "Equatorial Guinea", "Eritrea", "Eswatini", "Ethiopia", "Gabon", "Gambia", "Ghana", "Guinea", 
                    "Guinea-Bissau", "Ivory Coast", "Kenya", "Lesotho", "Liberia", "Libya", "Madagascar", "Malawi", "Mali", 
                    "Mauritania", "Mauritius", "Morocco", "Mozambique", "Namibia", "Niger", "Nigeria", "Rwanda", 
                    "São Tomé and Príncipe", "Senegal", "Seychelles", "Sierra Leone", "Somalia", "South Africa", 
                    "South Sudan", "Sudan", "Tanzania", "Togo", "Tunisia", "Uganda", "Zambia", "Zimbabwe"
                ]
              };
        
              // Define region colors
              const region_colors = {
                NAM: "#FDEAF4",
                EMEA: "#F1EAFA"
              };
        
              // Generate result object dynamically
              chart_data.forEach(({ label, value }) => {
                const countries = countryGroups[label] || [];
                const distributedValue = value;
        
                countries.forEach(country => {
                  result[country] = {
                    total: distributedValue,
                    region: label,
                    color: region_colors[label] || "#FFFFFF" // Default color
                  };
                });
              });
        }

        logger.info("result", result)

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getAllocatedEmployeeStatus = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getAllocatedEmployeeStatus(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            logger.info("chart_data", chart_data)
            result = {
                total: chart_data.reduce((sum, item) => sum + item.value, 0),
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.deployedHeadCountBasedOnEntity = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.deployedHeadCountBasedOnEntity(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            result = {
                data: data['data']
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getNonBillableStatus = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getNonBillableStatus(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            result = {
                data: data['data']
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}


/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getAllocatedEmployeePractice = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getAllocatedEmployeePractice(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getBillabilityBasedOnLocation = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getBillabilityBasedOnLocation(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}


/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getAllocatedEmployeeLevel = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getAllocatedEmployeeLevel(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}


/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getSkillCountBasedOnAllocation = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getSkillCountBasedOnAllocation(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data,
                countLabel: chart_data.length
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

/**
 * @description Non Billable Aging
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getNonBillableAging = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }


        
            let seriesData = [
              {
                "id": 1,
                "label": "<90 days",
                "color": "#4CAF50", 
                "key": "<90 days"
              },
              {
                "id": 2,
                "label": "90 - 120 days",
                "color": "#FFEB3B",
                "key": "90 - 120 days"
              },
              {
                "id": 3,
                "label": "120 - 180 days",
                "color": "#FF9800",
                "key": "120 - 180 days"
              },
              {
                "id": 4,
                "label": ">180 days",
                "color": "#F44336",
                "key": ">180 days"
              }
            ]

        let data = await dashboardData.getNonBillableAging(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data,
                countLabel: chart_data.length,
                seriesData: seriesData
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
            seriesData: seriesData
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getEmpoloyeeDiversity = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getEmpoloyeeDiversity(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            logger.info("chart_data", chart_data)
            result = {
                total: chart_data.reduce((sum, item) => sum + item.value, 0),
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getEmployeeBasedOnElm = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getEmployeeBasedOnElm(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            result = {
                data: data['data']
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getEmployeeBasedOnBench = async(db,params,user,authorization)=>{
    try{

        let result = {
            percentage: 0,
            color: "#52C41A",
            total: 0
        }

        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getEmployeeBasedOnBench(db, filter_query, start_date, end_date);
        let project_employees = await dashboardData.getProjectEmployeesCount(db)

        result.total = data['messType'] === "S" ? data['data'] : 0;
        result.employee_count = project_employees['messType'] === "S" ? project_employees['data'] : 0;

        result.percentage = ((result.total/result.employee_count)*100).toFixed(2);;

        logger.info

        if(result.percentage >= 80)
            result.color = "#52C41A"
        else if(result.percentage < 80 && result.percentage > 40)
            result.color = "#FA8C16"
        else
            result.color = "#FF3A46"

        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getEmployeeDeployableVsNonDeployable = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getEmployeeDeployableVsNonDeployable(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            logger.info("chart_data", chart_data)
            result = {
                total: chart_data.reduce((sum, item) => sum + item.value, 0),
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getEmployeeLevel = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getEmployeeLevel(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}
module.exports.getEmployeeDepartment = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getEmployeeDepartment(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getSkillsBasedOnEmployee = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getSkillCountBasedOnEmployee(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data,
                countLabel: chart_data.length
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}
module.exports.getLocationOfEmployee = async(db,params,user,authorization)=>{
    try{

        // let result = {};
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.endDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        console.log("getLocationOfEmployee - Date range:", { start_date, end_date });
        console.log("getLocationOfEmployee - Filter query:", filter_query);

        let data = await dashboardData.getEmployeeBasedOnRegion(db, filter_query, start_date, end_date);

        console.log("getEmployeeBasedOnRegion response:", data);

        if(data['messType'] === "S" && data['data']){

            let chart_data = data['data']
            console.log("Raw chart_data from DB:", chart_data);
            const countryGroups = {
                NAM: ["United States", "Canada", "Mexico"],
                EMEA: [
                    "Albania", "Andorra", "Armenia", "Austria", "Azerbaijan", "Belarus", "Belgium", "Bosnia and Herzegovina", 
                    "Bulgaria", "Croatia", "Cyprus", "Czech Republic", "Denmark", "Estonia", "Finland", "France", "Georgia", 
                    "Germany", "Greece", "Hungary", "Iceland", "Ireland", "Italy", "Kazakhstan", "Kosovo", "Latvia", 
                    "Liechtenstein", "Lithuania", "Luxembourg", "Malta", "Moldova", "Monaco", "Montenegro", "Netherlands", 
                    "North Macedonia", "Norway", "Poland", "Portugal", "Romania", "Russia", "San Marino", "Serbia", "Slovakia", 
                    "Slovenia", "Spain", "Sweden", "Switzerland", "Ukraine", "United Kingdom", "Vatican", 
                    "Bahrain", "Cyprus", "Egypt", "Iran", "Iraq", "Israel", "Jordan", "Kuwait", "Lebanon", "Oman", "Palestine", 
                    "Qatar", "Saudi Arabia", "Syria", "Turkey", "United Arab Emirates", "Yemen",
                    "Algeria", "Angola", "Benin", "Botswana", "Burkina Faso", "Burundi", "Cape Verde", "Cameroon", 
                    "Central African Republic", "Chad", "Comoros", "Republic of the Congo", "Democratic Republic of the Congo", 
                    "Djibouti", "Equatorial Guinea", "Eritrea", "Eswatini", "Ethiopia", "Gabon", "Gambia", "Ghana", "Guinea", 
                    "Guinea-Bissau", "Ivory Coast", "Kenya", "Lesotho", "Liberia", "Libya", "Madagascar", "Malawi", "Mali", 
                    "Mauritania", "Mauritius", "Morocco", "Mozambique", "Namibia", "Niger", "Nigeria", "Rwanda", 
                    "São Tomé and Príncipe", "Senegal", "Seychelles", "Sierra Leone", "Somalia", "South Africa", 
                    "South Sudan", "Sudan", "Tanzania", "Togo", "Tunisia", "Uganda", "Zambia", "Zimbabwe"
                ]
              };
            const regionTotals = {
                NAM: 0,
                EMEA: 0
            };
        
              // Define region colors
              const region_colors = {
                NAM: "#FDEAF4",
                EMEA: "#F1EAFA"
              };
        
            chart_data.forEach((item) => {
                const label = (item.label || item.country || "").trim().toLowerCase();
                const value = Number(item.value ?? item.count ?? 0);

                if (countryGroups.NAM.some(c => c.toLowerCase() === label)) {
                    regionTotals.NAM += value;
                } else if (countryGroups.EMEA.some(c => c.toLowerCase() === label)) {
                    regionTotals.EMEA += value;
                }
            });

            console.log("chart_data:", chart_data);
            // Prepare array for chart
            const result = Object.entries(regionTotals).map(([region, count]) => ({
                label: region,
                count
            }));
        

        logger.info("result", result)

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });
    }else {
      return Promise.resolve({
        messType: "S",
        data: [],
      });
    }

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}
/**
 * @description Non Billable Aging
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getEmployeeNonBillableAging = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }


        
            let seriesData = [
              {
                "id": 1,
                "label": "<90 days",
                "color": "#4CAF50", 
                "key": "<90 days"
              },
              {
                "id": 2,
                "label": "90 - 120 days",
                "color": "#FFEB3B",
                "key": "90 - 120 days"
              },
              {
                "id": 3,
                "label": "120 - 180 days",
                "color": "#FF9800",
                "key": "120 - 180 days"
              },
              {
                "id": 4,
                "label": ">180 days",
                "color": "#F44336",
                "key": ">180 days"
              }
            ]

        let data = await dashboardData.getNonEmployeeBillableAging(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data,
                countLabel: chart_data.length,
                seriesData: seriesData
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
            seriesData: seriesData
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.getAllocatedEmployeeElm = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getAllocatedEmployeeElm(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}
/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getAllocatedEmployeeCustomer = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getAllocatedEmployeeCustomer(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}
/**
 * @description Fetching Forward Request Summary Card Data
 * @param {*} db 
 * @param {*} params `
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getAllocatedEmployeeServiceType = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getAllocatedEmployeeServiceType(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}
module.exports.getDeployedDM = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getDeployedDM(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            logger.info("chart_data", chart_data)
            result = {
                total: chart_data.reduce((sum, item) => sum + item.value, 0),
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}
module.exports.getEmployeeReason = async(db,params,user,authorization)=>{
    try{

        let result = [];
        let filter_query = params?.filterQuery ? params?.filterQuery : null;
        let start_date = params?.startDate ? moment(params?.startDate).format("YYYY-MM-DD") : null;
        let end_date = params?.startDate ? moment(params?.endDate).format("YYYY-MM-DD") : null;

        if(!start_date || !end_date){
            return Promise.resolve({
                messType: "E",
                message:"Duration details not found",
            });
        }

        let data = await dashboardData.getEmployeeReason(db, filter_query, start_date, end_date);


        if(data['messType'] === "S" && data['data']){
            let chart_data = data['data']
            result = {
                data: chart_data
            }
        }

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result,
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[],total:0, message:"Error while Request data for Summary card !"})
    }
}

module.exports.openRequestSummaryCardForward = async(db,params,user,authorization)=>{
    try{

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        let status = [1,2,3,4]
        let get_total_request_data = await dashboardData.getRequestData(db,start_date,end_date,[],params?.filterQuery);
        let get_all_request_data = await dashboardData.getRequestDataForward(db,start_date,end_date,status,params?.filterQuery);

        if(!get_total_request_data || get_total_request_data['messType'] == 'E'){
            return Promise.resolve({
                messType:"E", 
                data:[],
                error: get_total_request_data ? get_total_request_data : 'Error in Fetching the Total Request Data', 
                message:"Error in Fetching the Total Request Data!"
            })
        }
        get_total_request_data = get_total_request_data['data'] ? get_total_request_data['data'] : [];
        if(!get_all_request_data || get_all_request_data['messType'] == 'E'){
            return Promise.resolve({
                messType:"E", 
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }
        get_all_request_data = get_all_request_data['data'] ? get_all_request_data['data'] : [];

        let percentage = 0;
        if(get_total_request_data?.length > 0){
            percentage = ((get_all_request_data?.length || 0) / (get_total_request_data?.length || 0))*100
        }
        let color;
        if(percentage >= 80)
            color = "#52C41A"
        else if(percentage < 80 && percentage > 40)
            color = "#FA8C16"
        else
            color = "#FF3A46"


        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:{
                total:`${get_all_request_data?.length || 0}/${get_total_request_data?.length || 0}`,
                percentage: (percentage).toFixed(2),
                total_count: get_total_request_data?.length || 0,
                color:color
            }
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while Request data for Summary card !"})
    }
}

module.exports.requestBasedProject = async(db,params,user,authorization)=>{
    try{

        let customer_data = await dashboardData.getProjectData(db);
        
        if(!customer_data || customer_data['messType'] == 'E' || customer_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: customer_data ? customer_data : 'No Customer Data', 
                message:"No Customer Data!"
            })
        }
        customer_data = customer_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        const inlineFilter = params?.inlineFilterData?.length > 0 ? params.inlineFilterData[0] : null;
        const defaultFilterId = inlineFilter?.default_filter_id ?? null;
        let inline_filter_config = null;
        let status = [];

        if (inlineFilter?.filter_data?.length > 0) {
            inline_filter_config = inlineFilter.filter_data.find(i => i.id === defaultFilterId) || null;
        }
        if (inline_filter_config && inline_filter_config?.value != 0) {
            status = [inline_filter_config.value];
        }
        
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);
        
        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                data: [],
                error: get_all_request_data ? get_all_request_data : 'No Request Data', 
                message:"No Request Data!"
            })
        }

        const requestData = get_all_request_data?.data;

        let customerMap = new Map(customer_data.map(data => [data.id, { id: data.id, label: data.name, value: 0 }]));

        // Count occurrences of each request
        requestData.forEach(request => {
            if (customerMap.has(request.project_id)) {
                customerMap.get(request.project_id).value += 1;
            }
        });

        // Filter customers with at least one request
        let result = Array.from(customerMap.values()).filter(customer => customer.value > 0);

        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while fetching Request Based on Customer !"})
    }
}
module.exports.requestBasedOnProjectCategory = async(db,params,user,authorization)=>{
    try{

        let project_role_data = await dashboardData.getProjectCategoryData(db);
        
        if(!project_role_data || project_role_data['messType'] == 'E' || project_role_data?.data?.length == 0){
            return Promise.resolve({
                messType:"E", 
                error: project_role_data ? project_role_data : 'Error in Fetching the Project Role Data', 
                message:"No Project Role Data!"
            })
        }
        project_role_data = project_role_data?.data

        let start_date = params?.startDate ? moment(params?.startDate).format('YYYY-MM-DD') : null;
        let end_date = params?.endDate ? moment(params?.endDate).format('YYYY-MM-DD') : null;
        const inlineFilter = params?.inlineFilterData?.length > 0 ? params.inlineFilterData[0] : null;
        const defaultFilterId = inlineFilter?.default_filter_id ?? null;
        let inline_filter_config = null;
        let status = [];

        if (inlineFilter?.filter_data?.length > 0) {
            inline_filter_config = inlineFilter.filter_data.find(i => i.id === defaultFilterId) || null;
        }
        if (inline_filter_config && inline_filter_config?.value != 0) {
            status = [inline_filter_config.value];
        }
 
        let get_all_request_data = await dashboardData.getRequestData(db,start_date,end_date,status,params?.filterQuery);
        
        if(!get_all_request_data || get_all_request_data['messType'] == 'E' || get_all_request_data?.data?.length == 0){
            return Promise.resolve({
                messType:"S", 
                data:[],
                error: get_all_request_data ? get_all_request_data : 'Error in Fetching the Request Data', 
                message:"Error in Fetching the Request Data!"
            })
        }


        const requestData = get_all_request_data?.data;

        // Create a Map for role data
        let roleMap = new Map(project_role_data.map(data => [data.id, { id: data.id, label: data.name, value: 0 }]));

        // Count occurrences of each role in requestData
        requestData.forEach(request => {
            if (roleMap.has(request.commercial_category)) {
                roleMap.get(request.commercial_category).value += 1;
            }
        });

        // Filter roles with at least one request
        let result = Array.from(roleMap.values()).filter(role => role.value > 0);


        // Return formatted response
        return Promise.resolve({
            messType: "S",
            data:result
        });

    }
    catch(err){
        logger.info(err)
        return Promise.reject({messType:"E", error: err, data:[], message:"Error while fetching Request Based on Project Role !"})
    }
}