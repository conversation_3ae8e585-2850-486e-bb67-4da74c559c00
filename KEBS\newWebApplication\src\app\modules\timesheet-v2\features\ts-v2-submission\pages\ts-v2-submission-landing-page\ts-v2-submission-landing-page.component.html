<div class="bg-container">
  <div class="submission">
    <div class="submission-spinner" [hidden]="!initialLoading || isTsBlocked">
      <mat-spinner class="green-spinner" diameter="40"></mat-spinner>
    </div>
    <div class="submission-header" [hidden]="initialLoading">
      <div
        class="submission-content-header-0"
        *ngIf="employeeConfig && basicTsConfig"
      >
        <div class="align-items-row col-11 p-0 first-shepherd-element">
          <div class="col-3 pl-0" style="display: flex; align-items: center">
            <div
              *ngIf="isAdmin"
              class="header-icon"
              style="margin-right: 30px"
              [ngStyle]="{
                'pointer-events':
                  isWalkthroughEnabled || isApiInProgress ? 'none' : 'auto'
              }"
            >
              <mat-icon
                (click)="resetMyTeams()"
                #myTeamsMenuTrigger="matMenuTrigger"
                [matMenuTriggerFor]="menu"
                class="supervisor"
                >group</mat-icon
              >
              <mat-menu #menu="matMenu">
                <ng-template [matMenuContent]>
                  <div
                    (click)="$event.stopPropagation()"
                    class="my-teams-popup"
                  >
                    <p class="my-team-text">My Team</p>
                    <mat-form-field
                      appearance="outline"
                      class="search-form pl-3"
                    >
                      <input
                        (keydown.Tab)="$event.stopPropagation()"
                        matInput
                        placeholder="Search"
                        [(ngModel)]="search_param_my_team"
                        (ngModelChange)="
                          onSearchChange($event)
                        "
                      />
                      <mat-icon matSuffix class="search-icon">search</mat-icon>
                    </mat-form-field>
                    <div
                      *ngIf="reportingEmployeeList.length === 0"
                      style="
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 256px;
                        width: 195px;
                        font-family: var(--fontFamily);
                        font-size: 12px;
                        font-weight: 600;
                        color: #45546e;
                      "
                    >
                      {{
                        search_param_my_team === "" &&
                        reportingEmployeeDetailsInProgress === false
                          ? "No team members found!"
                          : reportingEmployeeDetailsInProgress
                          ? "Please wait while employee details are being fetched!"
                          : "No employee details found!"
                      }}
                    </div>
                    <div
                      *ngIf="reportingEmployeeList.length > 0"
                      style="
                        display: flex;
                        flex-direction: column;
                        overflow-y: auto;
                        overflow-x: hidden;
                        max-height: 256px;
                      "
                      infinite-scroll
                      [infiniteScrollDistance]="0.1"
                      [scrollWindow]="false"
                      (scrolled)="onScrollGetEmployees()"
                    >
                      <div
                        *ngFor="
                          let items of reportingEmployeeList;
                          let i = index
                        "
                        style="
                          display: flex;
                          flex-direction: row;
                          align-items: center;
                        "
                        class="hover-color"
                        (click)="
                          employeeSwitchMyTeams(i); myTeamsTrigger.closeMenu()
                        "
                      >
                        <div style="margin-right: 8px">
                          <app-user-image
                            [oid]="items?.oid"
                            imgWidth="24px"
                            imgHeight="24px"
                          ></app-user-image>
                        </div>
                        <div>
                          <p
                            class="my-teams-name"
                            [matTooltip]="
                              items?.employee_name ? items.employee_name : ''
                            "
                          >
                            {{
                              items?.employee_name ? items.employee_name : "-"
                            }}
                          </p>
                          <p
                            class="my-teams-desc"
                            [matTooltip]="items?.status ? items.status : ''"
                          >
                            {{ items?.status ? items.status : "-" }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>
              </mat-menu>
            </div>
            <div style="margin-right: 15px">
              <app-user-image [id]="oid" imgWidth="29px" imgHeight="29px">
              </app-user-image>
            </div>
            <div
              style="
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              "
            >
              <p
                class="light-texts"
                [matTooltip]="employeeConfig[0]?.employee_name"
              >
                {{ employeeConfig[0]?.employee_name }},
              </p>
              <p
                class="bold-texts"
                [matTooltip]="employeeConfig[0]?.associate_id"
              >
                {{ employeeConfig[0]?.associate_id }}
              </p>
            </div>
          </div>
          <div
          class="col pl-0 overflow-div"
          *ngIf="tsUiConfig['UI-TAS-ADMN-002']?.is_visible"
        >
          <p
            class="header-name"
            [matTooltip]="tsUiConfig['UI-TAS-ADMN-002']?.config_text"
          >
            {{ tsUiConfig["UI-TAS-ADMN-002"]?.config_text }}
          </p>
          <p
            class="header-description"
            [matTooltip]="employeeConfig[0]?.department_name"
          >
            {{ employeeConfig[0]?.department_name }}
          </p>
        </div>
          <div
            class="col pl-0 overflow-div"
            *ngIf="tsUiConfig['UI-TAS-ADMN-007']?.is_visible"
          >
            <p
              class="header-name"
              [matTooltip]="tsUiConfig['UI-TAS-ADMN-007']?.config_text"
            >
              {{ tsUiConfig["UI-TAS-ADMN-007"]?.config_text }}
            </p>
            <p
              class="header-description"
              [matTooltip]="employeeConfig[0]?.division_name"
            >
              {{ employeeConfig[0]?.division_name }}
            </p>
          </div>
          <div
            class="col pl-0 overflow-div"
            *ngIf="tsUiConfig['UI-TAS-ADMN-008']?.is_visible"
          >
            <p
              class="header-name"
              [matTooltip]="tsUiConfig['UI-TAS-ADMN-008']?.config_text"
            >
              {{ tsUiConfig["UI-TAS-ADMN-008"]?.config_text }}
            </p>
            <p
              class="header-description"
              [matTooltip]="employeeConfig[0]?.sub_division_name"
            >
              {{ employeeConfig[0]?.sub_division_name }}
            </p>
          </div>
          <div
            class="col pl-0 overflow-div"
            *ngIf="tsUiConfig['UI-TAS-ADMN-009']?.is_visible"
          >
            <p
              class="header-name"
              [matTooltip]="tsUiConfig['UI-TAS-ADMN-009']?.config_text"
            >
              {{ tsUiConfig["UI-TAS-ADMN-009"]?.config_text }}
            </p>
            <p
              class="header-description"
              [matTooltip]="employeeConfig[0]?.role"
            >
              {{ employeeConfig[0]?.role }}
            </p>
          </div>
          <div
            class="col pl-0 overflow-div"
            *ngIf="tsUiConfig['UI-TAS-ADMN-004']?.is_visible"
          >
            <p
              class="header-name"
              [matTooltip]="tsUiConfig['UI-TAS-ADMN-004']?.config_text"
            >
              {{ tsUiConfig["UI-TAS-ADMN-004"]?.config_text }}
            </p>
            <p
              class="header-description"
              [matTooltip]="employeeConfig[0]?.work_schedule"
            >
              {{ employeeConfig[0]?.work_schedule }}
            </p>
          </div>
          <div
            class="col pl-0 overflow-div"
            *ngIf="tsUiConfig['UI-TAS-ADMN-003']?.is_visible"
          >
            <p
              class="header-name"
              [matTooltip]="tsUiConfig['UI-TAS-ADMN-003']?.config_text"
            >
              {{ tsUiConfig["UI-TAS-ADMN-003"]?.config_text }}
            </p>
            <p
              class="header-description"
              [matTooltip]="employeeConfig[0]?.employment_type"
            >
              {{ employeeConfig[0]?.employment_type }}
            </p>
          </div>
          <div
            class="col pl-0 overflow-div second-shepherd-element"
            *ngIf="tsUiConfig['UI-TAS-ADMN-005']?.is_visible"
          >
            <p
              class="header-name"
              [matTooltip]="tsUiConfig['UI-TAS-ADMN-005']?.config_text"
            >
              {{ tsUiConfig["UI-TAS-ADMN-005"]?.config_text }}
            </p>
            <p
              style="cursor: pointer"
              class="header-description"
              (click)="onClickHolidayCalendar()"
              [ngStyle]="{
                'pointer-events': isWalkthroughEnabled ? 'none' : 'auto'
              }"
            >
              <u style="padding-top: 1px">View</u>
            </p>
          </div>
        </div>
        <div
          class="align-items-row col-1 p-0"
          style="justify-content: flex-end"
        >
          <div
            style="margin-right: 10px"
            *ngIf="employeeConfig[0]?.auto_timesheet_submission"
          >
            <img
              matTooltip="Auto Timesheet Enabled"
              src="https://assets.kebs.app/Auto_Timesheet_Icon.png"
              width="27"
              height="27"
            />
          </div>
          <div class="third-shepherd-element">
            <mat-icon
              [matTooltip]="
                (basicTsConfig
                  | noOfFutureMonthsAllowed
                    : monthEndDate
                    : currentWeek?.startDate) ||
                ((basicTsConfig
                  | isTimesheetSubmissionLocked
                    : monthEndDate
                    : (currentWeek?.days | getCurrentWeekEndDate)
                    : overallTimesheetStatusBeforeEdit
                    : currentWeek?.tsOverAllStatus
                    : isPrevHistoryRejected
                    : employeeConfig[0]?.is_timesheet_edit_allowed
                    : openTsForLeaveWeekIntegrationEdit) &&
                  (basicTsConfig
                    | noOfPastMonthsAllowed
                      : monthStartDate
                      : employeeConfig[0]?.is_timesheet_edit_allowed))
                  ? 'Your Timesheet is Locked by HR!'
                  : (basicTsConfig
                    | isTimesheetSubmissionLocked
                      : monthEndDate
                      : (currentWeek?.days | getCurrentWeekEndDate)
                      : overallTimesheetStatusBeforeEdit
                      : currentWeek?.tsOverAllStatus
                      : isPrevHistoryRejected
                      : employeeConfig[0]?.is_timesheet_edit_allowed
                      : openTsForLeaveWeekIntegrationEdit)
                  ? 'Your Timesheet is Locked for Submission!'
                  : 'Your Timesheet is Open for Submission!'
              "
              [ngStyle]="{
                color:
                  ((basicTsConfig
                    | isTimesheetSubmissionLocked
                      : monthEndDate
                      : (currentWeek?.days | getCurrentWeekEndDate)
                      : overallTimesheetStatusBeforeEdit
                      : currentWeek?.tsOverAllStatus
                      : isPrevHistoryRejected
                      : employeeConfig[0]?.is_timesheet_edit_allowed
                      : openTsForLeaveWeekIntegrationEdit) &&
                    (basicTsConfig
                      | noOfPastMonthsAllowed
                        : monthStartDate
                        : employeeConfig[0]?.is_timesheet_edit_allowed)) ||
                  (basicTsConfig
                    | noOfFutureMonthsAllowed
                      : monthEndDate
                      : currentWeek?.startDate)
                    ? '#FF3A46'
                    : '#52C41A'
              }"
              class="header-timesheet-status"
              >{{
                ((basicTsConfig
                  | isTimesheetSubmissionLocked
                    : monthEndDate
                    : (currentWeek?.days | getCurrentWeekEndDate)
                    : overallTimesheetStatusBeforeEdit
                    : currentWeek?.tsOverAllStatus
                    : isPrevHistoryRejected
                    : employeeConfig[0]?.is_timesheet_edit_allowed
                    : openTsForLeaveWeekIntegrationEdit) &&
                  (basicTsConfig
                    | noOfPastMonthsAllowed
                      : monthStartDate
                      : employeeConfig[0]?.is_timesheet_edit_allowed)) ||
                (basicTsConfig
                  | noOfFutureMonthsAllowed
                    : monthEndDate
                    : currentWeek?.startDate)
                  ? "lock"
                  : "lock_open"
              }}</mat-icon
            >
          </div>
          <div
            [hidden]="aid === currentUser.aid"
            [ngStyle]="{
              'pointer-events': isApiInProgress ? 'none' : 'auto'
            }"
          >
            <mat-icon
              matTooltip="Close"
              *ngIf="tsUiConfig['UI-TAS-ADMN-006']?.is_visible && isAdmin"
              (click)="switchToCurrentEmployee()"
              class="header-close"
              >{{ tsUiConfig["UI-TAS-ADMN-006"]?.icon_name }}</mat-icon
            >
          </div>
        </div>
      </div>
      <mat-divider class="divider"></mat-divider>
    </div>
    <div
      class="submission-content"
      [hidden]="initialLoading"
      *ngIf="basicTsConfig"
    >
      <div class="submission-content-header">
        <div
          class="align-items-row"
          *ngIf="tsUiConfig['UI-TAS-HD-001']?.is_visible"
        >
          <div
            [ngStyle]="{
              'pointer-events':
                isApiInProgress || isWalkthroughEnabled ? 'none' : 'auto'
            }"
            class="fourth-shepherd-element"
          >
            <mat-icon
              matTooltip="Go to Previous Week"
              (click)="getPreviousWeek()"
              class="icon"
              >keyboard_arrow_left</mat-icon
            >
          </div>
          <div>
            <p class="week-text">
              {{ currentWeek?.startDate | dateFormat }} -
              {{ currentWeek?.endDate | dateFormat }}
            </p>
          </div>
          <div
            class="pb-2"
            [ngStyle]="{
              'pointer-events':
                isApiInProgress || isWalkthroughEnabled ? 'none' : 'auto'
            }"
          >
            <input
              matInput
              [formControl]="month"
              [matDatepicker]="dp"
              [min]="minValidation"
              [max]="maxValidation"
              style="visibility: hidden; width: 0px"
            />
            <mat-datepicker-toggle [for]="dp">
              <mat-icon matDatepickerToggleIcon class="calendar-icon"
                >calendar_today</mat-icon
              >
            </mat-datepicker-toggle>
            <mat-datepicker
              #dp
              startView="year"
              [startAt]="startDate"
              [selected]="selectedDate"
              [opened]="monthSelected"
              (monthSelected)="
                dp.close(); monthSelected = false; onSelect($event, dp)
              "
            >
            </mat-datepicker>
          </div>
          <div
            [ngStyle]="{
              'pointer-events':
                isApiInProgress || isWalkthroughEnabled ? 'none' : 'auto'
            }"
            class="fourteenth-shepherd-element"
          >
            <mat-icon
              matTooltip="Go to Next Week"
              (click)="getNextWeek()"
              class="icon"
              >keyboard_arrow_right</mat-icon
            >
          </div>
          <ng-container *ngIf="tsUiConfig['UI-TAS-HD-002']?.is_visible">
            <div
              *ngFor="let item of weeksOfMonth; let i = index"
              (click)="getWeekButton(i)"
              class="week-badge fifth-shepherd-element"
              matTooltip="{{
                item?.tsOverAllStatus
                  | tsOverallStatus
                    : showEditStatus
                    : i + 1
                    : currentWeek?.weekNumber
              }}"
              [matTooltipClass]="
                item?.tsOverAllStatus
                  | statusTooltipClass
                    : showEditStatus
                    : i + 1
                    : currentWeek?.weekNumber
              "
              [matTooltipDisabled]="
                (item?.endDate
                  | dateOfJoiningCheck : startDateOfEmployee : false) ||
                (endDateOfEmployee && item?.startDate
                  | dateOfExitCheck : endDateOfEmployee : false)
              "
              [ngStyle]="{
                'box-shadow':
                  item?.weekNumber === currentWeek?.weekNumber
                    ? '2px 2px 2px 0px #00000040'
                    : '',
                'background-color':
                  item?.weekNumber === currentWeek?.weekNumber &&
                  showEditStatus === true
                    ? tsStatusConfig[6]?.bckg_color_code
                    : item?.tsOverAllStatus === 1
                    ? tsStatusConfig[1]?.bckg_color_code
                    : item?.tsOverAllStatus === 2
                    ? tsStatusConfig[2]?.bckg_color_code
                    : item?.tsOverAllStatus === 3
                    ? tsStatusConfig[3]?.bckg_color_code
                    : item?.tsOverAllStatus === 4
                    ? tsStatusConfig[4]?.bckg_color_code
                    : item?.tsOverAllStatus === 7
                    ? tsStatusConfig[4]?.bckg_color_code
                    : tsStatusConfig[6]?.bckg_color_code,
                'border-color':
                  item?.weekNumber === currentWeek?.weekNumber &&
                  showEditStatus === true
                    ? tsStatusConfig[6]?.border_color
                    : item?.tsOverAllStatus === 1
                    ? tsStatusConfig[1]?.border_color
                    : item?.tsOverAllStatus === 2
                    ? tsStatusConfig[2]?.border_color
                    : item?.tsOverAllStatus === 3
                    ? tsStatusConfig[3]?.border_color
                    : item?.tsOverAllStatus === 4
                    ? tsStatusConfig[4]?.border_color
                    : item?.tsOverAllStatus === 7
                    ? tsStatusConfig[4]?.border_color
                    : tsStatusConfig[6]?.border_color,
                'pointer-events':
                  isApiInProgress || isWalkthroughEnabled ? 'none' : 'auto',
                'border-width':
                  item?.weekNumber === currentWeek?.weekNumber ? '2px' : '1px'
              }"
            >
              <p
                class="week-badge-text"
                [ngStyle]="{
                  color:
                    item?.weekNumber === currentWeek?.weekNumber &&
                    showEditStatus === true
                      ? tsStatusConfig[6]?.border_color
                      : item?.tsOverAllStatus === 1
                      ? tsStatusConfig[1]?.border_color
                      : item?.tsOverAllStatus === 2
                      ? tsStatusConfig[2]?.border_color
                      : item?.tsOverAllStatus === 3
                      ? tsStatusConfig[3]?.border_color
                      : item?.tsOverAllStatus === 4
                      ? tsStatusConfig[4]?.border_color
                      : item?.tsOverAllStatus === 7
                      ? tsStatusConfig[4]?.border_color
                      : tsStatusConfig[6]?.border_color
                }"
              >
                W{{ item?.weekNumber }}
              </p>
            </div>
          </ng-container>
        </div>
        <div
          class="align-items-row timer-stop-resume"
          *ngIf="
            basicTsConfig?.display_timer == '1' &&
            !(currentWeek | isTimerRunning : 'Stop' : triggerTimerPipe)
          "
        >
          <div
            class="stop-button"
            (click)="storeClockInOutDetails(4)"
            [ngStyle]="{
              'pointer-events': isTimerApiRunning ? 'none' : ''
            }"
          >
            <div class="stop-icon"></div>
            <div class="stop-text">Stop</div>
          </div>
          <div
            (click)="storeClockInOutDetails(2)"
            class="pause-button"
            *ngIf="!(currentWeek | isTimerRunning : 'Pause' : triggerTimerPipe)"
            [ngStyle]="{
              'pointer-events': isTimerApiRunning ? 'none' : ''
            }"
          >
            <div style="height: 17px">
              <mat-icon class="pause-icon">pause</mat-icon>
            </div>
            <div class="pause-text">Pause</div>
          </div>
          <div
            (click)="storeClockInOutDetails(3)"
            class="resume-button"
            *ngIf="currentWeek | isTimerRunning : 'Pause' : triggerTimerPipe"
            [ngStyle]="{
              'pointer-events': isTimerApiRunning ? 'none' : ''
            }"
          >
            <div style="height: 16px">
              <mat-icon class="resume-icon">play_arrow</mat-icon>
            </div>
            <div class="resume-text">Resume</div>
          </div>
          <div class="elapsed-time">
            {{
              currentWeek
                | getTimerIndex
                | timerDurationCalc : currentWeek : triggerTimerPipe
            }}
          </div>
        </div>
        <div class="align-items-row">
          <div
            *ngIf="tsUiConfig['UI-TAS-HD-003']?.is_visible"
            class="sixth-shepherd-element"
          >
            <p class="light-text">
              {{ tsUiConfig["UI-TAS-HD-003"]?.config_text }}
            </p>
            <p
              class="bold-text"
              [ngStyle]="{
                color:
                  currentWeek
                  | weeklyHoursColorStamp
                    : basicTsConfig?.overtime_allowed_in_ts
                    : employee_quota_min_hours
                    : startDateOfEmployee
                    : triggerPipes
              }"
            >
              {{
                currentWeek?.days
                  | totalHoursWeekly
                    : basicTsConfig?.overtime_allowed_in_ts
                    : triggerPipes
                    : basicTsConfig
              }}
            </p>
          </div>
          <div class="mr24" *ngIf="tsUiConfig['UI-TAS-HD-003']?.is_visible">
            <button
              *ngIf="basicTsConfig?.display_total_hours_splitup == '1'"
              style="border: none; background-color: white; padding: 0"
              [matMenuTriggerFor]="hoursPopUp"
            >
              <mat-icon class="drop-down-icon">arrow_drop_down</mat-icon>
            </button>
          </div>
          <div
            *ngIf="tsUiConfig['UI-TAS-HD-004']?.is_visible"
            class="seventh-shepherd-element"
          >
            <p class="light-text">
              {{ tsUiConfig["UI-TAS-HD-004"]?.config_text }}
            </p>
            <p
              class="bold-text"
              [ngStyle]="{
                color:
                  currentWeek
                  | totalLeavesWeekly : triggerPipes
                  | weeklyLeaveColorStamp
              }"
            >
              {{ currentWeek | totalLeavesWeekly : triggerPipes }}
            </p>
          </div>
          <div class="mr24" *ngIf="tsUiConfig['UI-TAS-HD-004']?.is_visible">
            <button
              *ngIf="basicTsConfig?.display_leave_splitup == '1'"
              style="border: none; background-color: white; padding: 0"
              [matMenuTriggerFor]="leavePopUp"
            >
              <mat-icon
                (click)="getLeaveDetailsForPopUp()"
                class="drop-down-icon"
                >arrow_drop_down</mat-icon
              >
            </button>
          </div>
          <div
            class="mr30 eighth-shepherd-element"
            *ngIf="tsUiConfig['UI-TAS-HD-005']?.is_visible"
          >
            <p class="light-text">
              {{ tsUiConfig["UI-TAS-HD-005"]?.config_text }}
            </p>
            <p
              class="bold-text"
              [ngStyle]="{
                color:
                  currentWeek?.days
                  | totalOvertimeHoursWeekly : triggerPipes
                  | weeklyOvertimeColorStamp
              }"
            >
              {{ currentWeek?.days | totalOvertimeHoursWeekly : triggerPipes }}
            </p>
          </div>
          <div
            [hidden]="
              currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6
            "
            class="mr16"
            *ngIf="tsUiConfig['UI-TAS-HD-008']?.is_visible"
          >
            <button
              mat-button
              class="edit-btn"
              (click)="editAssocTSData()"
              [disabled]="
                editInProgress ||
                retrieveInProgress ||
                isApiInProgress ||
                ((basicTsConfig
                  | isTimesheetSubmissionLocked
                    : monthEndDate
                    : (currentWeek?.days | getCurrentWeekEndDate)
                    : overallTimesheetStatusBeforeEdit
                    : currentWeek?.tsOverAllStatus
                    : isPrevHistoryRejected
                    : employeeConfig[0]?.is_timesheet_edit_allowed
                    : openTsForLeaveWeekIntegrationEdit) &&
                  (basicTsConfig
                    | noOfPastMonthsAllowed
                      : monthStartDate
                      : employeeConfig[0]?.is_timesheet_edit_allowed)) ||
                (basicTsConfig
                  | noOfFutureMonthsAllowed
                    : monthEndDate
                    : currentWeek?.startDate) ||
                (basicTsConfig?.disable_edit_to_admin == 1 && this.aid != this.currentUser.aid) ||
                (basicTsConfig?.disable_approved_timesheet == 1 && currentWeek?.tsOverAllStatus == 4)
              "
            >
              {{ tsUiConfig["UI-TAS-HD-008"]?.config_text }}
            </button>
          </div>
          <div
            [hidden]="
              currentWeek?.tsOverAllStatus !== 1 &&
              currentWeek?.tsOverAllStatus !== 6
            "
            class="mr16 tenth-shepherd-element"
            *ngIf="tsUiConfig['UI-TAS-HD-006']?.is_visible"
            [ngStyle]="{
              'pointer-events': isWalkthroughEnabled ? 'none' : 'auto'
            }"
          >
            <button
              mat-button
              class="save-btn"
              (click)="onSaveAssociateTs()"
              [disabled]="
                saveSubmitBtnDisabled ||
                editBtnClicked ||
                ((basicTsConfig
                  | isTimesheetSubmissionLocked
                    : monthEndDate
                    : (currentWeek?.days | getCurrentWeekEndDate)
                    : overallTimesheetStatusBeforeEdit
                    : currentWeek?.tsOverAllStatus
                    : isPrevHistoryRejected
                    : employeeConfig[0]?.is_timesheet_edit_allowed
                    : openTsForLeaveWeekIntegrationEdit) &&
                  (basicTsConfig
                    | noOfPastMonthsAllowed
                      : monthStartDate
                      : employeeConfig[0]?.is_timesheet_edit_allowed)) ||
                (basicTsConfig
                  | noOfFutureMonthsAllowed
                    : monthEndDate
                    : currentWeek?.startDate)
              "
            >
              {{
                saveInProgress ? "" : tsUiConfig["UI-TAS-HD-006"]?.config_text
              }}
              <mat-icon *ngIf="saveInProgress"
                ><mat-spinner class="green-spinner" diameter="20"></mat-spinner
              ></mat-icon>
            </button>
          </div>
          <div
            class="mr16"
            *ngIf="tsUiConfig['UI-TAS-HD-007']?.is_visible"
            [ngStyle]="{
              'pointer-events': isWalkthroughEnabled ? 'none' : 'auto'
            }"
          >
            <button
              mat-button
              class="submit-btn eleventh-shepherd-element"
              (click)="onSubmitAssociateTs()"
              [disabled]="
                saveSubmitBtnDisabled ||
                ((basicTsConfig
                  | isTimesheetSubmissionLocked
                    : monthEndDate
                    : (currentWeek?.days | getCurrentWeekEndDate)
                    : overallTimesheetStatusBeforeEdit
                    : currentWeek?.tsOverAllStatus
                    : isPrevHistoryRejected
                    : employeeConfig[0]?.is_timesheet_edit_allowed
                    : openTsForLeaveWeekIntegrationEdit) &&
                  (basicTsConfig
                    | noOfPastMonthsAllowed
                      : monthStartDate
                      : employeeConfig[0]?.is_timesheet_edit_allowed)) ||
                (basicTsConfig
                  | noOfFutureMonthsAllowed
                    : monthEndDate
                    : currentWeek?.startDate)
              "
            >
              {{
                submitInProgress ? "" : tsUiConfig["UI-TAS-HD-007"]?.config_text
              }}
              <mat-icon *ngIf="submitInProgress"
                ><mat-spinner class="green-spinner" diameter="20"></mat-spinner
              ></mat-icon>
            </button>
          </div>
          <div
            class="mr16 twelveth-shepherd-element"
            *ngIf="tsUiConfig['UI-TAS-HD-009']?.is_visible"
            [ngStyle]="{
              'pointer-events':
                isApiInProgress || isWalkthroughEnabled ? 'none' : 'auto'
            }"
          >
            <button class="more-options-btn" [matMenuTriggerFor]="belowMenu">
              <mat-icon class="more-options-icon">more_vert</mat-icon>
            </button>
          </div>
          <mat-menu #belowMenu="matMenu" yPosition="below">
            <div class="more-options-menu">
              <p
                class="more-options-item"
                style="margin-bottom: 8px"
                (click)="onClearTs()"
                [ngStyle]="{
                  'pointer-events':
                    (currentWeek?.tsOverAllStatus === 1 ||
                      currentWeek?.tsOverAllStatus === 6) &&
                    currentWeek?.days[startDayIndex]?.costcenter &&
                    currentWeek?.days[startDayIndex]?.costcenter.length > 0 &&
                    !(
                      (basicTsConfig
                        | isTimesheetSubmissionLocked
                          : monthEndDate
                          : (currentWeek?.days | getCurrentWeekEndDate)
                          : overallTimesheetStatusBeforeEdit
                          : currentWeek?.tsOverAllStatus
                          : isPrevHistoryRejected
                          : employeeConfig[0]?.is_timesheet_edit_allowed
                          : openTsForLeaveWeekIntegrationEdit) &&
                      (basicTsConfig
                        | noOfPastMonthsAllowed
                          : monthStartDate
                          : employeeConfig[0]?.is_timesheet_edit_allowed)
                    ) &&
                    !(
                      basicTsConfig
                      | noOfFutureMonthsAllowed
                        : monthEndDate
                        : currentWeek?.startDate
                    )
                      ? 'auto'
                      : 'none',
                  opacity:
                    (currentWeek?.tsOverAllStatus === 1 ||
                      currentWeek?.tsOverAllStatus === 6) &&
                    currentWeek?.days[startDayIndex]?.costcenter &&
                    currentWeek?.days[startDayIndex]?.costcenter.length > 0 &&
                    !(
                      (basicTsConfig
                        | isTimesheetSubmissionLocked
                          : monthEndDate
                          : (currentWeek?.days | getCurrentWeekEndDate)
                          : overallTimesheetStatusBeforeEdit
                          : currentWeek?.tsOverAllStatus
                          : isPrevHistoryRejected
                          : employeeConfig[0]?.is_timesheet_edit_allowed
                          : openTsForLeaveWeekIntegrationEdit) &&
                      (basicTsConfig
                        | noOfPastMonthsAllowed
                          : monthStartDate
                          : employeeConfig[0]?.is_timesheet_edit_allowed)
                    ) &&
                    !(
                      basicTsConfig
                      | noOfFutureMonthsAllowed
                        : monthEndDate
                        : currentWeek?.startDate
                    )
                      ? '1'
                      : '0.3'
                }"
              >
                Clear Timesheet
              </p>
              <p
                class="more-options-item"
                style="margin-bottom: 8px"
                (click)="onClickTsPolicies()"
              >
                Policies
              </p>
              <p
                class="more-options-item"
                style="margin-bottom: 8px"
                (click)="onClickEvents()"
                *ngIf="basicTsConfig?.display_timesheet_events == '1'"
              >
                Timesheet Events
              </p>
              <p
                class="more-options-item"
                style="margin-bottom: 8px"
                (click)="openWelcomeScreen()"
              >
                Timesheet Updates
              </p>
              <p
                class="more-options-item"
                style="margin-bottom: 8px"
                (click)="openTimesheetHelpDocument()"
              >
                Timesheet Help
              </p>
              <p
                class="more-options-item"
                (click)="openTimesheetTaskDocument()"
                *ngIf="basicTsConfig?.show_task_link == '1'"
              >
                {{ tsUiConfig["UI-TAS-HLF-001"]?.config_text }}
              </p>
              <p
              class="more-options-item"
              (click)="downloadEmployeeTimesheet()"
              *ngIf="basicTsConfig?.display_download_report == '1'"
              >
                Download Timesheet
              </p>
            </div>
          </mat-menu>
          <div
            class="thirteenth-shepherd-element"
            *ngIf="tsUiConfig['UI-TAS-HD-010']?.is_visible"
            [ngStyle]="{
              'pointer-events': isWalkthroughEnabled ? 'none' : 'auto'
            }"
          >
            <mat-icon (click)="openHelpDialog()" class="ques-icon"
              >help</mat-icon
            >
          </div>
        </div>
      </div>
      <mat-divider class="divider"></mat-divider>
      <div
        class="submission-content-display-spinner"
        [hidden]="!retrieveInProgress"
      >
        <mat-spinner
          matTooltip="Please wait while your Timesheet is being retrieved."
          class="green-spinner"
          diameter="40"
        ></mat-spinner>
      </div>
      <div
        cdk-virtual-scroll-viewport
        itemSize="1000"
        class="submission-content-display"
        #scrollContainer
        (scroll)="
          isFillHoursPopupVisible = false; this.selectedToggle = 'Regular'
        "
        [hidden]="retrieveInProgress"
      >
        <div class="col-3 ts-cost-center">
          <div style="margin-bottom: 35px">
            <p
              class="cc-title-text"
              *ngIf="tsUiConfig['UI-TAS-SN-004']?.is_visible"
            >
              {{ tsUiConfig["UI-TAS-SN-004"]?.config_text }}
            </p>
            <p
              class="approval-status-text"
              [ngStyle]="{
                color:
                  !showIcons || showEditStatus
                    ? tsStatusConfig[6]?.border_color
                    : currentWeek?.tsOverAllStatus === 1
                    ? tsStatusConfig[1]?.border_color
                    : currentWeek?.tsOverAllStatus === 2
                    ? tsStatusConfig[2]?.border_color
                    : currentWeek?.tsOverAllStatus === 3
                    ? tsStatusConfig[3]?.border_color
                    : currentWeek?.tsOverAllStatus === 4
                    ? tsStatusConfig[4]?.border_color
                    : tsStatusConfig[6]?.border_color
              }"
            >
              {{
                showIcons
                  ? (currentWeek?.tsOverAllStatus
                    | tsOverallStatus : showEditStatus : 0 : 0)
                  : "Editing"
              }}
            </p>
          </div>
          <ng-container *ngIf="currentUi === 'InitialCC'">
            <div
              style="display: flex; flex-direction: column; align-items: center"
            >
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                "
              >
                <img
                  style="padding-bottom: 16px"
                  [src]="tsUiConfig['UI-TAS-SN-009']?.image_url"
                />
                <p
                  class="large-text"
                  *ngIf="tsUiConfig['UI-TAS-SN-001']?.is_visible"
                >
                  {{ tsUiConfig["UI-TAS-SN-001"]?.config_text }}
                </p>
                <p
                  class="medium-light-text"
                  *ngIf="tsUiConfig['UI-TAS-SN-002']?.is_visible"
                >
                  {{ tsUiConfig["UI-TAS-SN-002"]?.config_text }}
                </p>
              </div>
              <div class="ninth-shepherd-element">
                <button
                  class="add-cost-center-btn"
                  *ngIf="tsUiConfig['UI-TAS-SN-007']?.is_visible"
                  (click)="goToInitialAddCcUi()"
                  [ngStyle]="{
                    'pointer-events':
                      isApiInProgress || isWalkthroughEnabled ? 'none' : 'auto'
                  }"
                >
                  {{ tsUiConfig["UI-TAS-SN-007"]?.config_text }}
                </button>
              </div>
              <div
                class="align-items-row"
                style="margin-top: 10px; cursor: pointer; align-items: normal"
                [ngStyle]="{
                  'pointer-events': isApiInProgress ? 'none' : 'auto'
                }"
                (click)="retrievePreviousWeekData(2)"
              >
                <div>
                  <mat-icon class="previous-week-icon">history</mat-icon>
                </div>
                <div>
                  <p
                    class="prefill-previous-week-text"
                    *ngIf="tsUiConfig['UI-TAS-SN-008']?.is_visible"
                  >
                    {{ tsUiConfig["UI-TAS-SN-008"]?.config_text }}
                  </p>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="currentUi === 'AddInitialCC'">
            <div class="add-initial-cc-border">
              <div>
                <p
                  class="large-bold-text"
                  *ngIf="tsUiConfig['UI-TAS-SN-005']?.is_visible"
                >
                  {{ tsUiConfig["UI-TAS-SN-005"]?.config_text }}
                </p>
              </div>
              <div>
                <form [formGroup]="ccForm">
                  <mat-form-field
                    class="cc-form-field"
                    appearance="outline"
                    [matTooltip]="
                      ccForm.value.costcenter
                        ? ccForm.value.costcenter?.name
                        : ''
                    "
                  >
                    <input
                      type="text"
                      matInput
                      #autoCompleteInput
                      [(ngModel)]="search_param"
                      placeholder="Select"
                      formControlName="costcenter"
                      (ngModelChange)="
                        getCostCenterListForSearch(); currentCcApprovers = []
                      "
                      [matAutocomplete]="auto"
                    />
                    <mat-autocomplete
                      class="custom-autocomplete-panel"
                      #auto="matAutocomplete"
                      [displayWith]="displayCcFn"
                      (optionSelected)="
                        onCcOptionSelected(); getCurrentCcApprovers($event)
                      "
                    >
                      <mat-option
                        [matTooltip]="option.name"
                        *ngFor="let option of ccSearchList"
                        [value]="option"
                        [disabled]="option.id | isCostCenterBlocked : ccList"
                      >
                        {{ option.name }}
                      </mat-option>
                    </mat-autocomplete>
                  </mat-form-field>
                </form>
              </div>
              <div
                style="padding-top: 8px"
                *ngIf="
                  subProjectList.length > 0 &&
                  (currentCcApprovers.length > 0 ||
                    manualApprovers.length > 0) &&
                  basicTsConfig?.display_subproject_in_ts == '1'
                "
              >
                <p
                  class="medium-text"
                  *ngIf="tsUiConfig['UI-TAS-SN-RT-003']?.is_visible"
                >
                  {{ tsUiConfig["UI-TAS-SN-RT-003"]?.config_text }}
                </p>
              </div>
              <div
                *ngIf="
                  subProjectList.length > 0 &&
                  (currentCcApprovers.length > 0 ||
                    manualApprovers.length > 0) &&
                  basicTsConfig?.display_subproject_in_ts == '1'
                "
              >
                <form [formGroup]="ccForm">
                  <mat-form-field class="cc-form-field" appearance="outline">
                    <mat-select
                      formControlName="subProject"
                      placeholder="Select"
                      class="cc-form-field"
                      (selectionChange)="onSubCcSelected()"
                      (opened)="showCursor()"
                      (closed)="clearSearchInput()"
                    >
                      <input matInput #searchSubProject (keyup)="filterOptions($event.target.value)" placeholder="Search Workstream" style="height: 35px; padding: 15px">
                      <mat-option
                        [matTooltip]="option.name"
                        *ngFor="let option of subProjectSearchList"
                        [value]="option.id"
                      >
                        <div class="manual-approver-name">
                          {{ option.name }}
                        </div>
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </form>
              </div>
              <div
                style="padding-top: 8px"
                *ngIf="
                  basicTsConfig?.manual_approver_selection == '1' &&
                  manualApprovers.length > 0
                "
              >
                <p class="medium-text">Select Approver</p>
              </div>
              <div
                *ngIf="
                  basicTsConfig?.manual_approver_selection == '1' &&
                  manualApprovers.length > 0
                "
              >
                <mat-form-field class="cc-form-field" appearance="outline">
                  <mat-select
                    [formControl]="approver"
                    placeholder="Select Approver"
                    (selectionChange)="onApproverSelected(selectApprover.value)"
                    #selectApprover
                    class="cc-form-field"
                  >
                    <mat-option
                      [matTooltip]="option?.name"
                      *ngFor="let option of manualApprovers; let i = index"
                      [value]="i"
                    >
                      <div class="manual-approver-name">
                        {{ option?.name }}
                      </div>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div
                *ngIf="
                  currentCcApprovers.length > 0 &&
                  basicTsConfig?.manual_approver_selection == '0'
                "
                style="margin-bottom: 8px"
              >
                <p class="medium-text">Approvers</p>
                <ng-container>
                  <ng-container
                    *ngFor="
                      let icons of currentCcApprovers;
                      let iconIndex = index
                    "
                  >
                    <app-user-image
                      [matTooltip]="icons?.name"
                      [id]="icons?.oid"
                      imgWidth="26px"
                      imgHeight="26px"
                      borderWidth="2px"
                      borderStyle="solid"
                      borderColor="#FFFFFF"
                      [ngStyle]="{
                        'margin-left': iconIndex >= 1 ? '-8px' : ''
                      }"
                    >
                    </app-user-image>
                  </ng-container>
                </ng-container>
              </div>
              <div
                class="align-items-center"
                *ngIf="
                  (ccForm.value | isDeptCc : ccList) &&
                  (currentWeek?.days
                    | isQuickFillActive
                      : basicTsConfig
                      : -1
                      : startDayIndex
                      : ccList
                      : ccForm.value)
                "
              >
                <div class="mr10">
                  <mat-slide-toggle
                    [checked]="isQuickFill"
                    (change)="toggleQuickFillInCc()"
                  ></mat-slide-toggle>
                </div>
                <div>
                  <p class="light-text">Quick fill hours</p>
                </div>
              </div>
              <div
                *ngIf="
                  isDecimalTs === '1' && isQuickFill && ccForm.value
                    | isDeptCc : ccList
                "
                [formGroup]="ccForm"
              >
                <mat-form-field
                  appearance="outline"
                  class="quick-fill-hours-form-field"
                >
                  <input
                    (keydown.Tab)="$event.stopPropagation()"
                    type="text"
                    placeholder="hh:mm"
                    maxlength="5"
                    (keydown)="onKeyDownPreventDecimal($event)"
                    (paste)="preventPaste($event)"
                    (keyup)="onInputChangeFormatTimeInCcQuickFill($event)"
                    matInput
                    formControlName="quickFillHours"
                  />
                </mat-form-field>
              </div>
              <div
                *ngIf="
                  isDecimalTs === '0' && isQuickFill && ccForm.value
                    | isDeptCc : ccList
                "
                [formGroup]="ccForm"
              >
                <mat-form-field
                  appearance="outline"
                  class="quick-fill-hours-form-field"
                >
                  <mat-label>Hours</mat-label>
                  <input
                    (keydown.Tab)="$event.stopPropagation()"
                    matInput
                    formControlName="quickFillHours"
                    type="number"
                    min="0"
                    max="24"
                    step="1"
                    (keydown)="preventMinusSignAndMaxTwoDigit($event)"
                    (input)="restrictOutOfRangeValues($event, 'quickFillCc')"
                    (paste)="preventDecimalOnPaste($event)"
                  />
                </mat-form-field>
              </div>
              <div style="display: flex; flex-direction: row">
                <div>
                  <button
                    (click)="goToInitialScreen()"
                    class="cancel-btn"
                    [ngStyle]="{
                      'pointer-events': isTaskLoadingForSubProject
                        ? 'none'
                        : 'auto'
                    }"
                  >
                    Cancel
                  </button>
                </div>
                <div>
                  <button
                    [disabled]="!ccFormValidations()"
                    (click)="isCostCenterSelected('AddCC')"
                    class="add-btn"
                    [ngStyle]="{
                      'pointer-events': isTaskLoadingForSubProject
                        ? 'none'
                        : 'auto'
                    }"
                  >
                    {{
                      isTaskLoadingForSubProject
                        ? "Adding" + loadingDots
                        : "Add"
                    }}
                  </button>
                </div>
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="currentUi === 'AddCC'">
            <div>
              <mat-accordion multi>
                <div
                  *ngFor="
                    let cc of currentWeek?.days[startDayIndex]?.costcenter;
                    let i = index
                  "
                  [ngStyle]="{
                    'pointer-events': isApiInProgress ? 'none' : 'auto'
                  }"
                >
                  <mat-expansion-panel
                    class="panel"
                    [hideToggle]="true"
                    [expanded]="cc?.expanded === true ? true : false"
                    [disabled]="true"
                    [ngStyle]="{
                      'margin-bottom':
                        cc?.tasks || cc?.subProjectId ? '12px' : '12px'
                    }"
                    [ngClass]="{
                      'panel-content':
                        cc?.tasks || cc?.subProjectId ? false : true,
                      'panel-height':
                        cc?.tasks || cc?.subProjectId ? true : false
                    }"
                  >
                    <mat-expansion-panel-header>
                      <mat-panel-title
                        class="col-9 p-0"
                        style="
                          margin-right: 0px !important;
                          display: flex !important;
                        "
                      >
                        <mat-icon
                          style="pointer-events: auto"
                          (click)="expandPanel(i)"
                          *ngIf="
                            cc?.expanded === false &&
                            (cc?.tasks || cc?.subProjectId)
                          "
                          class="expand-icon"
                          [ngStyle]="{
                            visibility: cc?.tasks
                              ? cc?.tasks.length <= 0
                                ? 'hidden'
                                : ''
                              : cc?.subProjectId
                              ? 'hidden'
                              : ''
                          }"
                          >expand_more</mat-icon
                        >
                        <mat-icon
                          style="pointer-events: auto"
                          (click)="expandPanel(i)"
                          *ngIf="
                            cc?.expanded === true &&
                            (cc?.tasks || cc?.subProjectId)
                          "
                          class="contract-icon"
                          [ngStyle]="{
                            visibility: cc?.tasks
                              ? cc?.tasks.length <= 0
                                ? 'hidden'
                                : ''
                              : cc?.subProjectId
                              ? 'hidden'
                              : ''
                          }"
                          >expand_less</mat-icon
                        >
                        <div
                          class="col-9 p-0"
                          style="
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                          "
                          [ngStyle]="{
                            'margin-left':
                              cc?.tasks || cc?.subProjectId ? '0px' : '24px'
                          }"
                        >
                          <div
                            class="expansion-panel-cc-name"
                            matTooltip="{{
                              cc?.costcenter + ' - ' + cc?.costcentername
                            }}"
                          >
                            {{ cc?.costcenter }} -
                            {{ cc?.costcentername }}
                          </div>
                          <div
                            matTooltip="{{ cc?.subProjectName }}"
                            class="expansion-panel-subcc-name"
                            *ngIf="cc?.subProjectName"
                          >
                            {{ cc?.subProjectName }}
                          </div>
                        </div>
                        <div>
                          <mat-icon
                            style="font-size: 14px; padding-left: 10px"
                            *ngIf="cc?.statusId != 1 && showIcons"
                            [ngStyle]="{
                              color:
                                cc?.statusId === 2
                                  ? '#fa8c16'
                                  : cc?.statusId === 3
                                  ? '#ff3a46'
                                  : cc?.statusId === 4
                                  ? '#52c41a'
                                  : '',
                              'padding-top': cc?.subProjectId ? '14px' : '7px'
                            }"
                            >{{
                              cc?.statusId === 2
                                ? "how_to_vote"
                                : cc?.statusId === 3
                                ? "dangerous"
                                : cc?.statusId === 4
                                ? "check_circle"
                                : ""
                            }}</mat-icon
                          >
                        </div>
                      </mat-panel-title>
                      <mat-panel-description>
                        <ng-container
                          *ngFor="
                            let icons of cc?.approvers;
                            let iconIndex = index
                          "
                        >
                          <ng-container *ngIf="cc?.approvers.length <= 2">
                            <app-user-image
                              style="pointer-events: auto; cursor: pointer"
                              (click)="onClickApprovers(i)"
                              [id]="icons?.oid"
                              imgWidth="26px"
                              imgHeight="26px"
                              borderWidth="2px"
                              borderStyle="solid"
                              borderColor="#FFFFFF"
                              [ngStyle]="{
                                'margin-left': iconIndex === 1 ? '-8px' : ''
                              }"
                            >
                            </app-user-image>
                          </ng-container>
                          <ng-container *ngIf="cc?.approvers.length > 2">
                            <app-user-image
                              style="pointer-events: auto; cursor: pointer"
                              (click)="onClickApprovers(i)"
                              [id]="icons?.oid"
                              imgWidth="26px"
                              imgHeight="26px"
                              *ngIf="iconIndex < 1"
                              borderWidth="2px"
                              borderStyle="solid"
                              borderColor="#FFFFFF"
                            >
                            </app-user-image>
                            <div
                              style="pointer-events: auto; cursor: pointer"
                              (click)="onClickApprovers(i)"
                              class="approvers-extra-display"
                              *ngIf="iconIndex === cc?.approvers.length - 1"
                            >
                              +{{ cc?.approvers.length - 1 }}
                            </div>
                          </ng-container>
                        </ng-container>
                        <mat-icon
                          [id]="'quicksettings' + i + '-1'"
                          (click)="
                            setIndexQuickFill(i, -1, cc?.tasks ? false : true);
                            isQuickSettingsPopupVisible = true;
                            isFillHoursPopupVisible = false
                          "
                          class="more-vert-icon"
                          [ngStyle]="{
                            'pointer-events': isApiInProgress ? 'none' : 'auto'
                          }"
                          >more_vert</mat-icon
                        >
                      </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div
                      style="
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 10px;
                      "
                      *ngFor="let task of cc?.tasks; let taskIndex = index"
                    >
                      <div class="col-9 p-0">
                        <p
                          class="task-name-text"
                          matTooltip="{{ task?.taskName }}"
                        >
                          {{ task?.taskName }}
                        </p>
                        <p
                          #taskUrlMenuTrigger="matMenuTrigger"
                          [matMenuTriggerFor]="taskUrlPopUp"
                          (click)="
                            setTaskUrlPopUpDetails(
                              task?.taskName,
                              task?.taskURL
                            )
                          "
                          class="task-url-text"
                          matTooltip="{{ task?.taskURL }}"
                        >
                          {{ task?.taskURL }}
                        </p>
                      </div>
                      <div>
                        <mat-icon
                          [id]="'quicksettings' + i + taskIndex"
                          (click)="
                            setIndexQuickFill(
                              i,
                              taskIndex,
                              cc?.tasks ? false : true
                            );
                            isQuickSettingsPopupVisible = true;
                            isFillHoursPopupVisible = false
                          "
                          class="more-vert-icon-tasks"
                          [ngStyle]="{
                            'pointer-events': isApiInProgress ? 'none' : 'auto'
                          }"
                          >more_vert</mat-icon
                        >
                      </div>
                    </div>
                    <div *ngIf="cc?.tasks || cc?.subProjectId ? true : false">
                      <p
                        *ngIf="
                          currentWeek?.tsOverAllStatus === 1 ||
                          currentWeek?.tsOverAllStatus === 6
                        "
                        (click)="addTaskManually(i)"
                        class="add-task-manually-text"
                        [ngStyle]="{
                          'margin-top': cc?.tasks
                            ? cc?.tasks.length > 0
                              ? '16px'
                              : '12px'
                            : '12px'
                        }"
                      >
                        {{ tsUiConfig["UI-TAS-SN-011"]?.config_text }}
                      </p>
                    </div>
                    <div
                      *ngIf="
                        (cc?.tasks || cc?.subProjectId ? true : false) &&
                        (currentWeek?.tsOverAllStatus !== 1 ||
                          currentWeek?.tsOverAllStatus !== 6)
                      "
                      style="margin-bottom: 8px"
                    ></div>
                  </mat-expansion-panel>
                </div>
              </mat-accordion>
            </div>
            <ng-container
              *ngIf="
                currentSubUi === 'AddBtn' &&
                (currentWeek?.tsOverAllStatus === 1 ||
                  currentWeek?.tsOverAllStatus === 6)
              "
            >
              <button
                class="add-cc-btn"
                (click)="openAddCc()"
                *ngIf="tsUiConfig['UI-TAS-SN-007']?.is_visible"
                [ngStyle]="{
                  'pointer-events': isApiInProgress ? 'none' : 'auto'
                }"
              >
                {{ tsUiConfig["UI-TAS-SN-007"]?.config_text }}
              </button>
            </ng-container>
            <ng-container
              *ngIf="
                currentSubUi === 'AddCcUi' &&
                (currentWeek?.tsOverAllStatus === 1 ||
                  currentWeek?.tsOverAllStatus === 6)
              "
            >
              <div class="add-secondary-cc-border">
                <div>
                  <p
                    class="large-bold-text"
                    *ngIf="tsUiConfig['UI-TAS-SN-005']?.is_visible"
                  >
                    {{ tsUiConfig["UI-TAS-SN-005"]?.config_text }}
                  </p>
                </div>
                <div>
                  <form [formGroup]="ccForm">
                    <mat-form-field
                      class="cc-form-field"
                      appearance="outline"
                      [matTooltip]="
                        ccForm.value.costcenter
                          ? ccForm.value.costcenter?.name
                          : ''
                      "
                    >
                      <input
                        type="text"
                        matInput
                        #autoCompleteInput
                        [(ngModel)]="search_param"
                        placeholder="Select"
                        formControlName="costcenter"
                        (ngModelChange)="
                          getCostCenterListForSearch(); currentCcApprovers = []
                        "
                        [matAutocomplete]="auto"
                      />
                      <mat-autocomplete
                        class="custom-autocomplete-panel"
                        #auto="matAutocomplete"
                        (optionSelected)="
                          onCcOptionSelected(); getCurrentCcApprovers($event)
                        "
                        [displayWith]="displayCcFn"
                      >
                        <mat-option
                          [matTooltip]="option.name"
                          *ngFor="let option of ccSearchList"
                          [value]="option"
                          [disabled]="option.id | isCostCenterBlocked : ccList"
                        >
                          {{ option.name }}
                        </mat-option>
                      </mat-autocomplete>
                    </mat-form-field>
                  </form>
                </div>
                <div
                  style="padding-top: 8px"
                  *ngIf="
                    subProjectList.length > 0 &&
                    (currentCcApprovers.length > 0 ||
                      manualApprovers.length > 0) &&
                    basicTsConfig?.display_subproject_in_ts == '1'
                  "
                >
                  <p
                    class="medium-text"
                    *ngIf="tsUiConfig['UI-TAS-SN-RT-003']?.is_visible"
                  >
                    {{ tsUiConfig["UI-TAS-SN-RT-003"]?.config_text }}
                  </p>
                </div>
                <div
                  *ngIf="
                    subProjectList.length > 0 &&
                    (currentCcApprovers.length > 0 ||
                      manualApprovers.length > 0) &&
                    basicTsConfig?.display_subproject_in_ts == '1'
                  "
                >
                  <form [formGroup]="ccForm">
                    <mat-form-field class="cc-form-field" appearance="outline">
                      <mat-select
                        formControlName="subProject"
                        placeholder="Select"
                        class="cc-form-field"
                        (selectionChange)="onSubCcSelected()"
                        (opened)="showCursor()"
                        (closed)="clearSearchInput()"
                      >
                        <input matInput #searchSubProject2 (keyup)="filterOptions($event.target.value)" placeholder="Search Workstream" style="height: 35px; padding: 15px">
                        <mat-option
                          [matTooltip]="option.name"
                          *ngFor="let option of subProjectSearchList"
                          [value]="option.id"
                        >
                          <div class="manual-approver-name">
                            {{ option.name }}
                          </div>
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </form>
                </div>
                <div
                  style="padding-top: 8px"
                  *ngIf="
                    basicTsConfig?.manual_approver_selection == '1' &&
                    manualApprovers.length > 0
                  "
                >
                  <p class="medium-text">Select Approver</p>
                </div>
                <div
                  *ngIf="
                    basicTsConfig?.manual_approver_selection == '1' &&
                    manualApprovers.length > 0
                  "
                >
                  <mat-form-field class="cc-form-field" appearance="outline">
                    <mat-select
                      [formControl]="approver"
                      placeholder="Select Approver"
                      (selectionChange)="
                        onApproverSelected(selectApprover.value)
                      "
                      #selectApprover
                      class="cc-form-field"
                    >
                      <mat-option
                        [matTooltip]="option?.name"
                        *ngFor="let option of manualApprovers; let i = index"
                        [value]="i"
                      >
                        <div class="manual-approver-name">
                          {{ option?.name }}
                        </div>
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div
                  *ngIf="
                    currentCcApprovers.length > 0 &&
                    basicTsConfig?.manual_approver_selection == '0'
                  "
                  style="margin-bottom: 8px"
                >
                  <p class="medium-text">Approvers</p>
                  <ng-container>
                    <ng-container
                      *ngFor="
                        let icons of currentCcApprovers;
                        let iconIndex = index
                      "
                    >
                      <app-user-image
                        [matTooltip]="icons?.name"
                        [id]="icons?.oid"
                        imgWidth="26px"
                        imgHeight="26px"
                        borderWidth="2px"
                        borderStyle="solid"
                        borderColor="#FFFFFF"
                        [ngStyle]="{
                          'margin-left': iconIndex >= 1 ? '-8px' : ''
                        }"
                      >
                      </app-user-image>
                    </ng-container>
                  </ng-container>
                </div>
                <div
                  class="align-items-center"
                  *ngIf="
                    (ccForm.value | isDeptCc : ccList) &&
                    (currentWeek?.days
                      | isQuickFillActive
                        : basicTsConfig
                        : -1
                        : startDayIndex
                        : ccList
                        : ccForm.value)
                  "
                >
                  <div class="mr10">
                    <mat-slide-toggle
                      [checked]="isQuickFill"
                      (change)="toggleQuickFillInCc()"
                    ></mat-slide-toggle>
                  </div>
                  <div>
                    <p class="light-text">Quick fill hours</p>
                  </div>
                </div>
                <div
                  *ngIf="
                    isDecimalTs === '1' && isQuickFill && ccForm.value
                      | isDeptCc : ccList
                  "
                  [formGroup]="ccForm"
                >
                  <mat-form-field
                    appearance="outline"
                    class="quick-fill-hours-form-field"
                  >
                    <input
                      (keydown.Tab)="$event.stopPropagation()"
                      type="text"
                      placeholder="hh:mm"
                      maxlength="5"
                      (keydown)="onKeyDownPreventDecimal($event)"
                      (paste)="preventPaste($event)"
                      (keyup)="onInputChangeFormatTimeInCcQuickFill($event)"
                      matInput
                      formControlName="quickFillHours"
                    />
                  </mat-form-field>
                </div>
                <div
                  *ngIf="
                    isDecimalTs === '0' && isQuickFill && ccForm.value
                      | isDeptCc : ccList
                  "
                  [formGroup]="ccForm"
                >
                  <mat-form-field
                    appearance="outline"
                    class="quick-fill-hours-form-field"
                  >
                    <mat-label>Hours</mat-label>
                    <input
                      (keydown.Tab)="$event.stopPropagation()"
                      matInput
                      formControlName="quickFillHours"
                      type="number"
                      min="0"
                      max="24"
                      step="1"
                      (keydown)="preventMinusSignAndMaxTwoDigit($event)"
                      (input)="restrictOutOfRangeValues($event, 'quickFillCc')"
                      (paste)="preventDecimalOnPaste($event)"
                    />
                  </mat-form-field>
                </div>
                <div style="display: flex; flex-direction: row">
                  <div>
                    <button
                      (click)="goToAddBtnUiOnCancel()"
                      class="cancel-btn"
                      [ngStyle]="{
                        'pointer-events': isTaskLoadingForSubProject
                          ? 'none'
                          : 'auto'
                      }"
                    >
                      Cancel
                    </button>
                  </div>
                  <div>
                    <button
                      [disabled]="!ccFormValidations()"
                      (click)="isCostCenterSelected('AddBtn')"
                      class="add-btn"
                      [ngStyle]="{
                        'pointer-events': isTaskLoadingForSubProject
                          ? 'none'
                          : 'auto'
                      }"
                    >
                      {{
                        isTaskLoadingForSubProject
                          ? "Adding" + loadingDots
                          : "Add"
                      }}
                    </button>
                  </div>
                </div>
              </div>
            </ng-container>
            <!--Prefill during Holiday cc-->
            <ng-container  *ngIf="(currentWeek?.days | totalHoursWeekly) == '00h 00m' && currentWeek?.days[startDayIndex]?.costcenter.length == 1 && currentWeek?.days[startDayIndex]?.costcenter[0]?.costCenterId == basicTsConfig?.ts_leave_booking_cc_id && currentWeek?.days[startDayIndex]?.costcenter[0]?.costcentertype != 'I'">
              <div
                class="align-items-row"
                style="margin-top: 10px; cursor: pointer; align-items: normal"
                [ngStyle]="{
                  'pointer-events': isApiInProgress ? 'none' : 'auto'
                }"
                (click)="retrievePreviousWeekData(1)"
              >
                <div>
                  <mat-icon class="previous-week-icon">history</mat-icon>
                </div>
                <div>
                  <p
                    class="prefill-previous-week-text"
                    *ngIf="tsUiConfig['UI-TAS-SN-008']?.is_visible"
                  >
                    {{ tsUiConfig["UI-TAS-SN-008"]?.config_text }}
                  </p>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
        <div class="col-9 ts-week" style="padding-top: 16px;">
          <ng-container *ngFor="let item of currentWeek?.days; let i = index">
            <div
              class="col"
              [ngClass]="getBackgroundColor(item)"
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0px !important;
              "
            >
              <div
                class="date-border"
                *ngIf="item?.dayIndex == i + 1 && 
                item?.date != endDateOfEmployee"
                [ngStyle]="{
                  'border-color': (currentWeek | dailyHoursColorStamp:i:basicTsConfig?.overtime_allowed_in_ts:employee_quota_min_hours:employee_quota_max_hours:triggerPipes),
                  'border-width': (item | currentDateBorderColor:basicTsConfig?.show_current_day_bckg_color)
                }"
              >
                <p class="date-text">
                  {{ item?.date | dateFormat2 }}
                </p>
              </div>

              <div class="last-working-day-border" *ngIf="item?.dayIndex == i + 1 && 
                            item?.date == endDateOfEmployee" [ngStyle]="{
                              'border-color':
                                currentWeek
                                | dailyHoursColorStamp
                                  : i
                                  : basicTsConfig?.overtime_allowed_in_ts
                                  : employee_quota_min_hours
                                  : employee_quota_max_hours
                                  : triggerPipes
                            }">
                <div class="last-working-day-border-overlap">
                  <p style="padding-left: 20px" class="last-working-day-text">
                    LWD
                  </p>
                </div>
                <div style="
                          display: flex;
                          width: inherit;
                          justify-content: center;
                        ">
                  <p class="hours-worked-text-on-last-working-day">
                    {{ item?.date | dateFormat2 }}
                  </p>
                </div>
              </div>

              <div>
                <p
                  class="small-light-text"
                  style="margin-bottom: 0px"
                  *ngIf="item?.dayIndex == i + 1"
                  [ngStyle]="{
                    color:
                      currentWeek
                      | dailyHoursColorStamp
                        : i
                        : basicTsConfig?.overtime_allowed_in_ts
                        : employee_quota_min_hours
                        : employee_quota_max_hours
                        : triggerPipes
                        : basicTsConfig
                  }"
                >
                  {{
                    currentWeek
                      | totalHoursDaily
                        : i
                        : -1
                        : basicTsConfig?.overtime_allowed_in_ts
                        : triggerPipes
                        : basicTsConfig?.show_leave_at_cc_header
                        : leaveHeaderData
                        : basicTsConfig
                  }}
                </p>
              </div>
              <ng-container *ngFor="let cc of item?.costcenter; let i2 = index">
                <div
                  *ngIf="
                    cc?.tasks || cc?.subProjectId;
                    then hasTask;
                    else noTask
                  "
                ></div>
                <ng-template #noTask>
                  <div
                    [id]="'hoursFillPopup' + i + i2 + '-1'"
                    [matTooltip]="item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId"
                    [matTooltipDisabled]="
                      !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                    "
                    matTooltipClass="holiday-tooltip"
                    class="hours-worked-border restrict-host-listener-hours-fill"
                    (click)="setIndexMatMenu($event, i, i2, -1)"
                    *ngIf="
                      [1, 7, 8].includes(cc?.attendanceTypeId) &&
                      (currentWeek?.tsOverAllStatus === 1 ||
                        currentWeek?.tsOverAllStatus === 6)
                    "
                    [ngStyle]="{
                      'pointer-events':
                        ((currentWeek | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                          cc?.attendanceTypeId != 2) ||
                        ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                          cc?.attendanceTypeId != 5) ||
                        (currentWeek?.tsOverAllStatus !== 1 &&
                          currentWeek?.tsOverAllStatus !== 6) ||
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false) ||
                        ((basicTsConfig
                          | isTimesheetSubmissionLocked
                            : monthEndDate
                            : (currentWeek?.days | getCurrentWeekEndDate)
                            : overallTimesheetStatusBeforeEdit
                            : currentWeek?.tsOverAllStatus
                            : isPrevHistoryRejected
                            : employeeConfig[0]?.is_timesheet_edit_allowed
                            : openTsForLeaveWeekIntegrationEdit) &&
                          (basicTsConfig
                            | noOfPastMonthsAllowed
                              : monthStartDate
                              : employeeConfig[0]
                                  ?.is_timesheet_edit_allowed)) ||
                        (basicTsConfig
                          | noOfFutureMonthsAllowed
                            : monthEndDate
                            : currentWeek?.startDate) ||
                        (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                          ? 'none'
                          : 'auto',
                      'background-color':
                        ((currentWeek | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                          cc?.attendanceTypeId != 2) ||
                        ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                          cc?.attendanceTypeId != 5) ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false)
                          ? '#F7F9FB'
                          : '#FFFFFF',
                      border:
                        basicTsConfig?.display_timer == '1' &&
                        cc?.timerFlag != 4 &&
                        cc?.timerFlag
                          ? '1px solid red'
                          : ((currentWeek
                              | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                              cc?.attendanceTypeId != 2) ||
                            ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                              cc?.attendanceTypeId != 5) ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false)
                          ? '1px solid #DADCE2'
                          : (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                          ? '1px solid #6ab04c'
                          : '1px solid #5F6C81'
                    }"
                  >
                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>
                    <p
                      class="hours-worked-text restrict-host-listener-hours-fill"
                      [ngStyle]="{
                        color:
                          ((currentWeek
                            | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                            cc?.attendanceTypeId != 2) ||
                          ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                            cc?.attendanceTypeId != 5) ||
                          (item?.date
                            | dateOfJoiningCheck
                              : startDateOfEmployee
                              : false) ||
                          (endDateOfEmployee && item?.date
                            | dateOfExitCheck : endDateOfEmployee : false)
                            ? '#DADCE2'
                            : '#111434'
                      }"
                    >
                      {{
                        basicTsConfig?.overtime_allowed_in_ts == "1"
                          ? (cc?.hoursworked
                            | getRegularAndOvertimeHours : cc?.overtimeHours
                            | hoursWorkedSplit)
                          : (cc?.hoursworked | hoursWorkedSplit)
                      }}
                    </p>
                  </div>

                  <div
                    [id]="'detailsTooltipCc' + i + i2"
                    class="hours-worked-border"
                    (mouseenter)="setIndexTooltip(i, i2, -1)"
                    (mouseleave)="isTooltipVisible = false"
                    *ngIf="
                      [1, 7, 8].includes(cc?.attendanceTypeId) &&
                      currentWeek?.tsOverAllStatus !== 1 &&
                      currentWeek?.tsOverAllStatus !== 6
                    "
                    [ngStyle]="{
                      'pointer-events':
                        ((currentWeek | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                          cc?.attendanceTypeId != 2) ||
                        ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                          cc?.attendanceTypeId != 5) ||
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false) ||
                        (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                          ? 'none'
                          : 'auto',
                      'background-color':
                        ((currentWeek | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                          cc?.attendanceTypeId != 2) ||
                        ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                          cc?.attendanceTypeId != 5) ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false)
                          ? '#F7F9FB'
                          : '#FFFFFF',
                      border:
                        ((currentWeek | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                          cc?.attendanceTypeId != 2) ||
                        ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                          cc?.attendanceTypeId != 5) ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false)
                          ? '1px solid #DADCE2'
                          : (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                          ? '1px solid #6ab04c'
                          : '1px solid #5F6C81'
                    }"
                  >

                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>
                    <p
                      class="hours-worked-text"
                      [ngStyle]="{
                        color:
                          ((currentWeek
                            | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) &&
                            cc?.attendanceTypeId != 2) ||
                          ((currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) &&
                            cc?.attendanceTypeId != 5) ||
                          (item?.date
                            | dateOfJoiningCheck
                              : startDateOfEmployee
                              : false) ||
                          (endDateOfEmployee && item?.date
                            | dateOfExitCheck : endDateOfEmployee : false)
                            ? '#DADCE2'
                            : '#111434'
                      }"
                    >
                      {{
                        basicTsConfig?.overtime_allowed_in_ts == "1"
                          ? (cc?.hoursworked
                            | getRegularAndOvertimeHours : cc?.overtimeHours
                            | hoursWorkedSplit)
                          : (cc?.hoursworked | hoursWorkedSplit)
                      }}
                    </p>
                  </div>

                  <div
                    [id]="'hoursFillPopup' + i + i2 + '-1'"
                    [matTooltip]="item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId"
                    [matTooltipDisabled]="
                      !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                    "
                    matTooltipClass="holiday-tooltip"
                    class="hours-worked-border restrict-host-listener-hours-fill"
                    (click)="setIndexMatMenu($event, i, i2, -1)"
                    *ngIf="
                      cc?.attendanceTypeId === 5 &&
                      (currentWeek?.tsOverAllStatus === 1 ||
                        currentWeek?.tsOverAllStatus === 6)
                    "
                    [ngStyle]="{
                      'pointer-events':
                        (currentWeek?.tsOverAllStatus !== 1 &&
                          currentWeek?.tsOverAllStatus !== 6) ||
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false) ||
                        ((basicTsConfig
                          | isTimesheetSubmissionLocked
                            : monthEndDate
                            : (currentWeek?.days | getCurrentWeekEndDate)
                            : overallTimesheetStatusBeforeEdit
                            : currentWeek?.tsOverAllStatus
                            : isPrevHistoryRejected
                            : employeeConfig[0]?.is_timesheet_edit_allowed
                            : openTsForLeaveWeekIntegrationEdit) &&
                          (basicTsConfig
                            | noOfPastMonthsAllowed
                              : monthStartDate
                              : employeeConfig[0]
                                  ?.is_timesheet_edit_allowed)) ||
                        (basicTsConfig
                          | noOfFutureMonthsAllowed
                            : monthEndDate
                            : currentWeek?.startDate) ||
                        (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                          ? 'none'
                          : 'auto',
                      border:
                        (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                          ? '1px solid #6ab04c'
                          : ''
                    }"
                    [ngClass]="(basicTsConfig?.week_off_should_be_in_disable_view == 1) ? 'wo-disabled': 'wo-not-disabled'"
                  >

                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>

                    <p *ngIf="basicTsConfig?.week_off_should_be_in_disable_view == 0"
                      class="hours-worked-text restrict-host-listener-hours-fill"
                    >
                      WO
                    </p>
                  </div>

                  <div
                    [id]="'detailsTooltipCc' + i + i2"
                    class="hours-worked-border"
                    (mouseenter)="setIndexTooltip(i, i2, -1)"
                    (mouseleave)="isTooltipVisible = false"
                    *ngIf="
                      cc?.attendanceTypeId === 5 &&
                      currentWeek?.tsOverAllStatus !== 1 &&
                      currentWeek?.tsOverAllStatus !== 6
                    "
                    [ngStyle]="{
                      'pointer-events':
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false)
                          ? 'none'
                          : 'auto',
                      border:
                        (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                          ? '1px solid #6ab04c'
                          : ''
                    }"
                    [ngClass]="(basicTsConfig?.week_off_should_be_in_disable_view == 1) ? 'wo-disabled': 'wo-not-disabled'"
                  >
                  
                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>
                    <p *ngIf="basicTsConfig?.week_off_should_be_in_disable_view == 0" class="hours-worked-text">WO</p>
                  </div>

                  <div
                    [id]="'hoursFillPopup' + i + i2 + '-1'"
                    [matTooltip]="item?.date | holidayTooltip : holidayConfig"
                    [matTooltipDisabled]="
                      !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                    "
                    matTooltipClass="holiday-tooltip"
                    class="full-day-leave-border restrict-host-listener-hours-fill"
                    (click)="setIndexMatMenu($event, i, i2, -1)"
                    *ngIf="
                      cc?.attendanceTypeId === 2 &&
                      cc?.insertionTypeId === 1 &&
                      (currentWeek?.tsOverAllStatus === 1 ||
                        currentWeek?.tsOverAllStatus === 6)
                    "
                    [style.border-color]="cc?.leaveColourCode"
                    [ngStyle]="{
                      'pointer-events':
                        (currentWeek?.tsOverAllStatus !== 1 &&
                          currentWeek?.tsOverAllStatus !== 6) ||
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false) ||
                        ((basicTsConfig
                          | isTimesheetSubmissionLocked
                            : monthEndDate
                            : (currentWeek?.days | getCurrentWeekEndDate)
                            : overallTimesheetStatusBeforeEdit
                            : currentWeek?.tsOverAllStatus
                            : isPrevHistoryRejected
                            : employeeConfig[0]?.is_timesheet_edit_allowed
                            : openTsForLeaveWeekIntegrationEdit) &&
                          (basicTsConfig
                            | noOfPastMonthsAllowed
                              : monthStartDate
                              : employeeConfig[0]
                                  ?.is_timesheet_edit_allowed)) ||
                        (basicTsConfig
                          | noOfFutureMonthsAllowed
                            : monthEndDate
                            : currentWeek?.startDate) ||
                        (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                          ? 'none'
                          : 'auto'
                    }"
                  >

                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>
                    <p
                      [style.color]="cc?.leaveColourCode"
                      class="leave-text restrict-host-listener-hours-fill"
                    >
                      {{ cc?.leaveCode }}
                    </p>
                  </div>

                  <div
                    [id]="'detailsTooltipCc' + i + i2"
                    class="full-day-leave-border"
                    (mouseenter)="setIndexTooltip(i, i2, -1)"
                    (mouseleave)="isTooltipVisible = false"
                    *ngIf="
                      (cc?.attendanceTypeId === 2 &&
                        currentWeek?.tsOverAllStatus !== 1 &&
                        currentWeek?.tsOverAllStatus !== 6) ||
                      (cc?.attendanceTypeId === 2 && cc?.insertionTypeId === 2)
                    "
                    [style.border-color]="cc?.leaveColourCode"
                    [ngStyle]="{
                      'pointer-events':
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false)
                          ? 'none'
                          : 'auto'
                    }"
                  >
                  
                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>
                    <p [style.color]="cc?.leaveColourCode" class="leave-text">
                      {{ cc?.leaveCode }}
                    </p>
                  </div>

                  <div
                    [id]="'hoursFillPopup' + i + i2 + '-1'"
                    [matTooltip]="item?.date | holidayTooltip : holidayConfig"
                    [matTooltipDisabled]="
                      !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                    "
                    matTooltipClass="holiday-tooltip"
                    (click)="setIndexMatMenu($event, i, i2, -1)"
                    *ngIf="
                      cc?.attendanceTypeId === 4 &&
                      (currentWeek?.tsOverAllStatus === 1 ||
                        currentWeek?.tsOverAllStatus === 6)
                    "
                    [style.border-color]="cc?.leaveColourCode"
                    [ngStyle]="{
                      'pointer-events':
                        (currentWeek?.tsOverAllStatus !== 1 &&
                          currentWeek?.tsOverAllStatus !== 6) ||
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false) ||
                        ((basicTsConfig
                          | isTimesheetSubmissionLocked
                            : monthEndDate
                            : (currentWeek?.days | getCurrentWeekEndDate)
                            : overallTimesheetStatusBeforeEdit
                            : currentWeek?.tsOverAllStatus
                            : isPrevHistoryRejected
                            : employeeConfig[0]?.is_timesheet_edit_allowed
                            : openTsForLeaveWeekIntegrationEdit) &&
                          (basicTsConfig
                            | noOfPastMonthsAllowed
                              : monthStartDate
                              : employeeConfig[0]
                                  ?.is_timesheet_edit_allowed)) ||
                        (basicTsConfig
                          | noOfFutureMonthsAllowed
                            : monthEndDate
                            : currentWeek?.startDate) ||
                        (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                          ? 'none'
                          : 'auto'
                    }"
                    class="half-day-leave-border restrict-host-listener-hours-fill"
                  >
                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>

                    <div
                      class="leave-border-overlap restrict-host-listener-hours-fill"
                      [style.background]="cc?.leaveColourCode"
                    >
                      <p
                        style="padding-left: 7px"
                        class="half-day-text restrict-host-listener-hours-fill"
                      >
                        Half Day
                      </p>
                      <mat-icon
                        class="restrict-host-listener-hours-fill"
                        style="font-size: 10px; color: white; padding-left: 2px"
                        >history</mat-icon
                      >
                    </div>
                    <div
                      style="
                        display: flex;
                        width: inherit;
                        justify-content: center;
                      "
                      class="restrict-host-listener-hours-fill"
                    >
                      <p
                        [style.color]="cc?.leaveColourCode"
                        class="cl-text restrict-host-listener-hours-fill"
                      >
                        {{ cc?.leaveCode }}
                      </p>
                      <p
                        *ngIf="cc?.hoursworked != '00:00'"
                        class="hours-worked-text-on-leave restrict-host-listener-hours-fill"
                      >
                        {{ cc?.hoursworked | hoursWorkedSplit }}
                      </p>
                    </div>
                  </div>

                  <div
                    [id]="'detailsTooltipCc' + i + i2"
                    (mouseenter)="setIndexTooltip(i, i2, -1)"
                    (mouseleave)="isTooltipVisible = false"
                    *ngIf="
                      cc?.attendanceTypeId === 4 &&
                      currentWeek?.tsOverAllStatus !== 1 &&
                      currentWeek?.tsOverAllStatus !== 6
                    "
                    [style.border-color]="cc?.leaveColourCode"
                    [ngStyle]="{
                      'pointer-events':
                        isApiInProgress ||
                        (item?.date
                          | dateOfJoiningCheck : startDateOfEmployee : false) ||
                        (endDateOfEmployee && item?.date
                          | dateOfExitCheck : endDateOfEmployee : false)
                          ? 'none'
                          : 'auto'
                    }"
                    class="half-day-leave-border"
                  >
                  
                  <div
                  class="comments-overlap"
                  *ngIf="
                    (cc?.approver_rejection_coment != '' &&
                    cc?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(cc?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>
                    <div
                      class="leave-border-overlap"
                      [style.background]="cc?.leaveColourCode"
                    >
                      <p style="padding-left: 7px" class="half-day-text">
                        Half Day
                      </p>
                      <mat-icon
                        style="font-size: 10px; color: white; padding-left: 2px"
                        >history</mat-icon
                      >
                    </div>
                    <div
                      style="
                        display: flex;
                        width: inherit;
                        justify-content: center;
                      "
                    >
                      <p [style.color]="cc?.leaveColourCode" class="cl-text">
                        {{ cc?.leaveCode }}
                      </p>
                      <p
                        *ngIf="cc?.hoursworked != '00:00'"
                        class="hours-worked-text-on-leave"
                      >
                        {{ cc?.hoursworked | hoursWorkedSplit }}
                      </p>
                    </div>
                  </div>
                </ng-template>
                <ng-template #hasTask>
                  <p
                    class="small-light-text"
                    [ngStyle]="{
                      'padding-top':
                        i2 == 0
                          ? '22px'
                          : item.costcenter[i2 - 1]?.tasks &&
                            item.costcenter[i2 - 1]?.tasks.length > 0 &&
                            item.costcenter[i2 - 1]?.expanded == false
                          ? '26px'
                          : '22px',
                      'margin-bottom':
                        cc?.expanded === false
                          ? '6px'
                          : cc?.tasks
                          ? cc?.tasks.length === 0
                            ? '54px'
                            : '4px'
                          : cc?.subProjectId && !cc?.tasks
                          ? '54px'
                          : '4px'
                    }"
                  >
                    {{
                      currentWeek
                        | totalHoursDaily
                          : i
                          : i2
                          : basicTsConfig?.overtime_allowed_in_ts
                          : triggerPipes
                          : basicTsConfig?.show_leave_at_cc_header
                          : leaveHeaderData
                          : basicTsConfig
                    }}
                  </p>
                  <ng-container *ngFor="let task of cc?.tasks; let i3 = index">
                    <ng-container>
                      <div
                        [id]="'hoursFillPopup' + i + i2 + i3"
                        [matTooltip]="
                          item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId
                        "
                        [matTooltipDisabled]="
                          !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                        "
                        matTooltipClass="holiday-tooltip"
                        class="hours-worked-border restrict-host-listener-hours-fill"
                        (click)="setIndexMatMenu($event, i, i2, i3)"
                        *ngIf="
                          [1, 7, 8].includes(task?.attendanceTypeId) &&
                          cc?.expanded === true &&
                          (currentWeek
                            | checkIfTaskPresent
                              : i
                              : i2
                              : i3
                              : item?.date
                              : triggerPipes) &&
                          (currentWeek?.tsOverAllStatus === 1 ||
                            currentWeek?.tsOverAllStatus === 6) &&
                          !(
                            basicTsConfig?.leave_activity_config == 1 &&
                            task?.taskName ==
                              basicTsConfig?.activity_name_for_leave &&
                              !(leaveAdminAccountId.includes(currentUser.aid) &&
                              basicTsConfig?.allow_leave_for_admin_account_in_ts ==1)
                          )
                        "
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            (currentWeek
                              | checkIfTaskPresent
                                : i
                                : i2
                                : i3
                                : item?.date
                                : triggerPipes) === false ||
                            (currentWeek
                              | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                            (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                            (currentWeek?.tsOverAllStatus !== 1 &&
                              currentWeek?.tsOverAllStatus !== 6) ||
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false) ||
                            ((basicTsConfig
                              | isTimesheetSubmissionLocked
                                : monthEndDate
                                : (currentWeek?.days | getCurrentWeekEndDate)
                                : overallTimesheetStatusBeforeEdit
                                : currentWeek?.tsOverAllStatus
                                : isPrevHistoryRejected
                                : employeeConfig[0]?.is_timesheet_edit_allowed
                                : openTsForLeaveWeekIntegrationEdit) &&
                              (basicTsConfig
                                | noOfPastMonthsAllowed
                                  : monthStartDate
                                  : employeeConfig[0]
                                      ?.is_timesheet_edit_allowed)) ||
                            (basicTsConfig
                              | noOfFutureMonthsAllowed
                                : monthEndDate
                                : currentWeek?.startDate) ||
                            (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                              ? 'none'
                              : 'auto',
                          'background-color':
                            (currentWeek
                              | checkIfTaskPresent
                                : i
                                : i2
                                : i3
                                : item?.date
                                : triggerPipes) === false ||
                            (currentWeek
                              | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                            (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                            (item?.date
                              | dateOfJoiningCheck : startDateOfEmployee) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false)
                              ? '#F7F9FB'
                              : '#FFFFFF',
                          border:
                            basicTsConfig?.display_timer == '1' &&
                            task?.timerFlag != 4 &&
                            task?.timerFlag
                              ? '1px solid red'
                              : (currentWeek
                                  | checkIfTaskPresent
                                    : i
                                    : i2
                                    : i3
                                    : item?.date
                                    : triggerPipes) === false ||
                                (currentWeek
                                  | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                                (currentWeek
                                  | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                                (item?.date
                                  | dateOfJoiningCheck
                                    : startDateOfEmployee
                                    : false) ||
                                (endDateOfEmployee && item?.date
                                  | dateOfExitCheck : endDateOfEmployee : false)
                              ? '1px solid #DADCE2'
                              : (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                              ? '1px solid #6ab04c'
                              : '1px solid #5F6C81'
                        }"
                      >
                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <p
                          class="hours-worked-text restrict-host-listener-hours-fill"
                          [ngStyle]="{
                            color:
                              (currentWeek
                                | checkIfTaskPresent
                                  : i
                                  : i2
                                  : i3
                                  : item?.date
                                  : triggerPipes) === false ||
                              (currentWeek
                                | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                              (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                              (item?.date
                                | dateOfJoiningCheck
                                  : startDateOfEmployee
                                  : false) ||
                              (endDateOfEmployee && item?.date
                                | dateOfExitCheck : endDateOfEmployee : false)
                                ? '#DADCE2'
                                : '#111434'
                          }"
                        >
                          {{
                            basicTsConfig?.overtime_allowed_in_ts == "1"
                              ? (task?.hoursworked
                                | getRegularAndOvertimeHours
                                  : task?.overtimeHours
                                | hoursWorkedSplit)
                              : (task?.hoursworked | hoursWorkedSplit)
                          }}
                        </p>
                      </div>

                      <div
                        [id]="'detailsTooltipTask' + i + i2 + i3"
                        class="hours-worked-border"
                        (mouseenter)="setIndexTooltip(i, i2, i3)"
                        (mouseleave)="isTooltipVisible = false"
                        *ngIf="
                          [1, 7, 8].includes(task?.attendanceTypeId) &&
                          cc?.expanded === true &&
                          (currentWeek
                            | checkIfTaskPresent
                              : i
                              : i2
                              : i3
                              : item?.date
                              : triggerPipes) &&
                          currentWeek?.tsOverAllStatus !== 1 &&
                          currentWeek?.tsOverAllStatus !== 6 &&
                          !(
                            basicTsConfig?.leave_activity_config == 1 &&
                            task?.taskName ==
                              basicTsConfig?.activity_name_for_leave &&
                              !(leaveAdminAccountId.includes(currentUser.aid) &&
                              basicTsConfig?.allow_leave_for_admin_account_in_ts ==1)
                          )
                        "
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            (currentWeek
                              | checkIfTaskPresent
                                : i
                                : i2
                                : i3
                                : item?.date
                                : triggerPipes) === false ||
                            (currentWeek
                              | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                            (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false)
                              ? 'none'
                              : 'auto',
                          'background-color':
                            (currentWeek
                              | checkIfTaskPresent
                                : i
                                : i2
                                : i3
                                : item?.date
                                : triggerPipes) === false ||
                            (currentWeek
                              | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                            (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                            (item?.date
                              | dateOfJoiningCheck : startDateOfEmployee) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false)
                              ? '#F7F9FB'
                              : '#FFFFFF',
                          border:
                            (currentWeek
                              | checkIfTaskPresent
                                : i
                                : i2
                                : i3
                                : item?.date
                                : triggerPipes) === false ||
                            (currentWeek
                              | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                            (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false)
                              ? '1px solid #DADCE2'
                              : (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                              ? '1px solid #6ab04c'
                              : '1px solid #5F6C81'
                        }"
                      >
                      
                  <div
                  class="comments-overlap"
                  *ngIf="
                    (task?.approver_rejection_coment != '' &&
                    task?.approver_rejection_coment != null) &&
                    basicTsConfig?.ts_approval_overview_detail_config == '1'
                  "
                  >
                    <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                    (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                      >mark_chat_unread
                    </mat-icon>
                
                  </div>
                        <p
                          class="hours-worked-text"
                          [ngStyle]="{
                            color:
                              (currentWeek
                                | checkIfTaskPresent
                                  : i
                                  : i2
                                  : i3
                                  : item?.date
                                  : triggerPipes) === false ||
                              (currentWeek
                                | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                              (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                              (item?.date
                                | dateOfJoiningCheck
                                  : startDateOfEmployee
                                  : false) ||
                              (endDateOfEmployee && item?.date
                                | dateOfExitCheck : endDateOfEmployee : false)
                                ? '#DADCE2'
                                : '#111434'
                          }"
                        >
                          {{
                            basicTsConfig?.overtime_allowed_in_ts == "1"
                              ? (task?.hoursworked
                                | getRegularAndOvertimeHours
                                  : task?.overtimeHours
                                | hoursWorkedSplit)
                              : (task?.hoursworked | hoursWorkedSplit)
                          }}
                        </p>
                      </div>

                      <div
                        (click)="noTaskMsg(task?.taskName)"
                        class="hours-worked-border"
                        *ngIf="
                          ([1, 7, 8].includes(task?.attendanceTypeId) &&
                            cc?.expanded === true &&
                            !(
                              currentWeek
                              | checkIfTaskPresent
                                : i
                                : i2
                                : i3
                                : item?.date
                                : triggerPipes
                            )) ||
                          ([1, 7, 8].includes(task?.attendanceTypeId) &&
                            cc?.expanded === true &&
                            basicTsConfig?.leave_activity_config == 1 &&
                            task?.taskName ==
                              basicTsConfig?.activity_name_for_leave) &&
                              !(leaveAdminAccountId.includes(currentUser.aid) &&
                              basicTsConfig?.allow_leave_for_admin_account_in_ts ==1)
                        "
                        style="
                          background-color: #f7f9fb;
                          border: 1px solid #dadce2;
                        "
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            (currentWeek
                              | checkFullDayLeave : i : triggerPipes : basicTsConfig?.show_leave_at_cc_header : leaveHeaderData : basicTsConfig?.allow_hours_in_holidays : basicTsConfig?.allow_hours_in_leave_id) ||
                            (currentWeek | checkWeekOff : i : triggerPipes : basicTsConfig?.week_off_should_be_in_disable_view : basicTsConfig?.disable_approved_timesheet : cc?.statusId) ||
                            (currentWeek?.tsOverAllStatus !== 1 &&
                              currentWeek?.tsOverAllStatus !== 6) ||
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false) ||
                            ((basicTsConfig
                              | isTimesheetSubmissionLocked
                                : monthEndDate
                                : (currentWeek?.days | getCurrentWeekEndDate)
                                : overallTimesheetStatusBeforeEdit
                                : currentWeek?.tsOverAllStatus
                                : isPrevHistoryRejected
                                : employeeConfig[0]?.is_timesheet_edit_allowed
                                : openTsForLeaveWeekIntegrationEdit) &&
                              (basicTsConfig
                                | noOfPastMonthsAllowed
                                  : monthStartDate
                                  : employeeConfig[0]
                                      ?.is_timesheet_edit_allowed)) ||
                            (basicTsConfig
                              | noOfFutureMonthsAllowed
                                : monthEndDate
                                : currentWeek?.startDate) ||
                            (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                              ? 'none'
                              : 'auto'
                        }"
                      >

                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <p class="hours-worked-text" style="color: #dadce2">
                          {{
                            basicTsConfig?.overtime_allowed_in_ts == "1"
                              ? (task?.hoursworked
                                | getRegularAndOvertimeHours
                                  : task?.overtimeHours
                                | hoursWorkedSplit)
                              : (task?.hoursworked | hoursWorkedSplit)
                          }}
                        </p>
                      </div>

                      <div
                        [id]="'hoursFillPopup' + i + i2 + i3"
                        [matTooltip]="
                          item?.date | holidayTooltip : holidayConfig
                        "
                        [matTooltipDisabled]="
                          !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                        "
                        matTooltipClass="holiday-tooltip"
                        class="hours-worked-border restrict-host-listener-hours-fill"
                        (click)="setIndexMatMenu($event, i, i2, i3)"
                        *ngIf="
                          task?.attendanceTypeId === 5 &&
                          cc?.expanded === true &&
                          (currentWeek?.tsOverAllStatus === 1 ||
                            currentWeek?.tsOverAllStatus === 6)
                        "
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            (currentWeek?.tsOverAllStatus !== 1 &&
                              currentWeek?.tsOverAllStatus !== 6) ||
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false) ||
                            ((basicTsConfig
                              | isTimesheetSubmissionLocked
                                : monthEndDate
                                : (currentWeek?.days | getCurrentWeekEndDate)
                                : overallTimesheetStatusBeforeEdit
                                : currentWeek?.tsOverAllStatus
                                : isPrevHistoryRejected
                                : employeeConfig[0]?.is_timesheet_edit_allowed
                                : openTsForLeaveWeekIntegrationEdit) &&
                              (basicTsConfig
                                | noOfPastMonthsAllowed
                                  : monthStartDate
                                  : employeeConfig[0]
                                      ?.is_timesheet_edit_allowed)) ||
                            (basicTsConfig
                              | noOfFutureMonthsAllowed
                                : monthEndDate
                                : currentWeek?.startDate) ||
                            (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                              ? 'none'
                              : 'auto',
                          border:
                            (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                              ? '1px solid #6ab04c'
                              : ''
                        }"
                        [ngClass]="(basicTsConfig?.week_off_should_be_in_disable_view == 1) ? 'wo-disabled': 'wo-not-disabled'"
                      >
                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <p *ngIf="basicTsConfig?.week_off_should_be_in_disable_view == 0"
                          class="hours-worked-text restrict-host-listener-hours-fill"
                        >
                          WO
                        </p>
                      </div>

                      <div
                        [id]="'detailsTooltipTask' + i + i2 + i3"
                        class="hours-worked-border"
                        (mouseenter)="setIndexTooltip(i, i2, i3)"
                        (mouseleave)="isTooltipVisible = false"
                        *ngIf="
                          task?.attendanceTypeId === 5 &&
                          cc?.expanded === true &&
                          currentWeek?.tsOverAllStatus !== 1 &&
                          currentWeek?.tsOverAllStatus !== 6
                        "
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false)
                              ? 'none'
                              : 'auto',
                          border:
                            (item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                              ? '1px solid #6ab04c'
                              : ''
                        }"
                        [ngClass]="(basicTsConfig?.week_off_should_be_in_disable_view == 1) ? 'wo-disabled': 'wo-not-disabled'"
                      >
                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <p *ngIf="basicTsConfig?.week_off_should_be_in_disable_view == 0" class="hours-worked-text">WO</p>
                      </div>

                      <div
                        [id]="'hoursFillPopup' + i + i2 + i3"
                        [matTooltip]="
                          item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId
                        "
                        [matTooltipDisabled]="
                          !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                        "
                        matTooltipClass="holiday-tooltip"
                        class="full-day-leave-border restrict-host-listener-hours-fill"
                        (click)="setIndexMatMenu($event, i, i2, i3)"
                        *ngIf="
                          task?.attendanceTypeId === 2 &&
                          task?.insertionTypeId === 1 &&
                          cc?.expanded === true &&
                          (currentWeek?.tsOverAllStatus === 1 ||
                            currentWeek?.tsOverAllStatus === 6 ) &&
                            (basicTsConfig?.leave_activity_config == 1 ?
                            (task?.taskName == basicTsConfig?.activity_name_for_leave && 
                            leaveAdminAccountId.includes(currentUser.aid) && 
                            basicTsConfig?.allow_leave_for_admin_account_in_ts == 1) 
                            : true )
                        "
                        [style.border-color]="task?.leaveColourCode"
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            (currentWeek?.tsOverAllStatus !== 1 &&
                              currentWeek?.tsOverAllStatus !== 6) ||
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false) ||
                            ((basicTsConfig
                              | isTimesheetSubmissionLocked
                                : monthEndDate
                                : (currentWeek?.days | getCurrentWeekEndDate)
                                : overallTimesheetStatusBeforeEdit
                                : currentWeek?.tsOverAllStatus
                                : isPrevHistoryRejected
                                : employeeConfig[0]?.is_timesheet_edit_allowed
                                : openTsForLeaveWeekIntegrationEdit) &&
                              (basicTsConfig
                                | noOfPastMonthsAllowed
                                  : monthStartDate
                                  : employeeConfig[0]
                                      ?.is_timesheet_edit_allowed)) ||
                            (basicTsConfig
                              | noOfFutureMonthsAllowed
                                : monthEndDate
                                : currentWeek?.startDate) ||
                            (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                              ? 'none'
                              : 'auto'
                        }"
                      >
                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <p
                          [style.color]="task?.leaveColourCode"
                          class="leave-text restrict-host-listener-hours-fill"
                        >
                          {{ task?.leaveCode }}
                        </p>
                      </div>

                      <div
                        [id]="'detailsTooltipTask' + i + i2 + i3"
                        class="full-day-leave-border"
                        (mouseenter)="setIndexTooltip(i, i2, i3)"
                        (mouseleave)="isTooltipVisible = false"
                        *ngIf="
                          cc?.expanded === true &&
                          ((task?.attendanceTypeId === 2 &&
                            currentWeek?.tsOverAllStatus !== 1 &&
                            currentWeek?.tsOverAllStatus !== 6) ||
                            (task?.attendanceTypeId === 2 &&
                              task?.insertionTypeId === 2) || 
                              (basicTsConfig?.leave_activity_config == 1 ?
                              (task?.attendanceTypeId === 2 &&
                              task?.taskName == basicTsConfig?.activity_name_for_leave && 
                              !(leaveAdminAccountId.includes(currentUser.aid) && basicTsConfig?.allow_leave_for_admin_account_in_ts == 1)
                              ) : false
                              )
                              )
                        "
                        [style.border-color]="task?.leaveColourCode"
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false) ||
                            ((basicTsConfig
                              | isTimesheetSubmissionLocked
                                : monthEndDate
                                : (currentWeek?.days | getCurrentWeekEndDate)
                                : overallTimesheetStatusBeforeEdit
                                : currentWeek?.tsOverAllStatus
                                : isPrevHistoryRejected
                                : employeeConfig[0]?.is_timesheet_edit_allowed
                                : openTsForLeaveWeekIntegrationEdit) &&
                              (basicTsConfig
                                | noOfPastMonthsAllowed
                                  : monthStartDate
                                  : employeeConfig[0]
                                      ?.is_timesheet_edit_allowed)) ||
                            (basicTsConfig
                              | noOfFutureMonthsAllowed
                                : monthEndDate
                                : currentWeek?.startDate) ||
                            (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                              ? 'none'
                              : 'auto'
                        }"
                      >
                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <p
                          [style.color]="task?.leaveColourCode"
                          class="leave-text"
                        >
                          {{ task?.leaveCode }}
                        </p>
                      </div>

                      <div
                        [id]="'hoursFillPopup' + i + i2 + i3"
                        [matTooltip]="
                          item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId
                        "
                        [matTooltipDisabled]="
                          !(item?.date | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : cc?.costCenterId)
                        "
                        matTooltipClass="holiday-tooltip"
                        (click)="setIndexMatMenu($event, i, i2, i3)"
                        *ngIf="
                          task?.attendanceTypeId === 4 &&
                          cc?.expanded === true &&
                          (currentWeek?.tsOverAllStatus === 1 ||
                            currentWeek?.tsOverAllStatus === 6) &&
                          !(
                            basicTsConfig?.leave_activity_config == 1 &&
                            task?.taskName ==
                              basicTsConfig?.activity_name_for_leave ) &&
                              !(leaveAdminAccountId.includes(currentUser.aid) &&
                              basicTsConfig?.allow_leave_for_admin_account_in_ts ==1)
                        "
                        [style.border-color]="cc?.leaveColourCode"
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            (currentWeek?.tsOverAllStatus !== 1 &&
                              currentWeek?.tsOverAllStatus !== 6) ||
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false) ||
                            ((basicTsConfig
                              | isTimesheetSubmissionLocked
                                : monthEndDate
                                : (currentWeek?.days | getCurrentWeekEndDate)
                                : overallTimesheetStatusBeforeEdit
                                : currentWeek?.tsOverAllStatus
                                : isPrevHistoryRejected
                                : employeeConfig[0]?.is_timesheet_edit_allowed
                                : openTsForLeaveWeekIntegrationEdit) &&
                              (basicTsConfig
                                | noOfPastMonthsAllowed
                                  : monthStartDate
                                  : employeeConfig[0]
                                      ?.is_timesheet_edit_allowed)) ||
                            (basicTsConfig
                              | noOfFutureMonthsAllowed
                                : monthEndDate
                                : currentWeek?.startDate) ||
                            (basicTsConfig?.disable_approved_timesheet == 1 && cc?.statusId == 4)
                              ? 'none'
                              : 'auto'
                        }"
                        class="half-day-leave-border restrict-host-listener-hours-fill"
                      >
                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <div
                          class="leave-border-overlap restrict-host-listener-hours-fill"
                          [style.background]="cc?.leaveColourCode"
                        >
                          <p
                            style="padding-left: 7px"
                            class="half-day-text restrict-host-listener-hours-fill"
                          >
                            Half Day
                          </p>
                          <mat-icon
                            style="
                              font-size: 10px;
                              color: white;
                              padding-left: 2px;
                            "
                            class="restrict-host-listener-hours-fill"
                            >history</mat-icon
                          >
                        </div>
                        <div
                          class="restrict-host-listener-hours-fill"
                          style="
                            display: flex;
                            width: inherit;
                            justify-content: center;
                          "
                        >
                          <p
                            [style.color]="task?.leaveColourCode"
                            class="restrict-host-listener-hours-fill cl-text"
                          >
                            {{ task?.leaveCode }}
                          </p>
                          <p
                            *ngIf="task?.hoursworked != '00:00'"
                            class="hours-worked-text-on-leave restrict-host-listener-hours-fill"
                          >
                            {{ task?.hoursworked | hoursWorkedSplit }}
                          </p>
                        </div>
                      </div>

                      <div
                        [id]="'detailsTooltipTask' + i + i2 + i3"
                        (mouseenter)="setIndexTooltip(i, i2, i3)"
                        (mouseleave)="isTooltipVisible = false"
                        *ngIf="
                          (task?.attendanceTypeId === 4 &&
                            cc?.expanded === true &&
                            currentWeek?.tsOverAllStatus !== 1 &&
                            currentWeek?.tsOverAllStatus !== 6) ||
                          (task?.attendanceTypeId === 4 &&
                            cc?.expanded === true &&
                            basicTsConfig?.leave_activity_config == 1 &&
                            task?.taskName ==
                              basicTsConfig?.activity_name_for_leave)  &&
                              !(leaveAdminAccountId.includes(currentUser.aid) &&
                              basicTsConfig?.allow_leave_for_admin_account_in_ts ==1)
                        "
                        [style.border-color]="cc?.leaveColourCode"
                        [ngStyle]="{
                          'margin-bottom': cc?.tasks
                            ? cc?.tasks.length - 1 === i3 &&
                              (currentWeek?.tsOverAllStatus === 1 ||
                                currentWeek?.tsOverAllStatus === 6)
                              ? '44px'
                              : cc?.tasks.length - 1 === i3 &&
                                (currentWeek?.tsOverAllStatus !== 1 ||
                                  currentWeek?.tsOverAllStatus !== 6)
                              ? '11px'
                              : '0px'
                            : '0px',
                          'pointer-events':
                            isApiInProgress ||
                            (item?.date
                              | dateOfJoiningCheck
                                : startDateOfEmployee
                                : false) ||
                            (endDateOfEmployee && item?.date
                              | dateOfExitCheck : endDateOfEmployee : false)
                              ? 'none'
                              : 'auto'
                        }"
                        class="half-day-leave-border"
                      >
                      <div
                      class="comments-overlap"
                      *ngIf="
                        (task?.approver_rejection_coment != '' &&
                        task?.approver_rejection_coment != null) &&
                        basicTsConfig?.ts_approval_overview_detail_config == '1'
                      "
                      >
                        <mat-icon style="font-size: 18px; color: #45546e; cursor: pointer"
                        (click)="openRejectedComments(task?.approver_rejection_coment, cc, item?.date)"
                          >mark_chat_unread
                        </mat-icon>
                    
                      </div>
                        <div
                          class="leave-border-overlap"
                          [style.background]="cc?.leaveColourCode"
                        >
                          <p style="padding-left: 7px" class="half-day-text">
                            Half Day
                          </p>
                          <mat-icon
                            style="
                              font-size: 10px;
                              color: white;
                              padding-left: 2px;
                            "
                            >history</mat-icon
                          >
                        </div>
                        <div
                          style="
                            display: flex;
                            width: inherit;
                            justify-content: center;
                          "
                        >
                          <p
                            [style.color]="task?.leaveColourCode"
                            class="cl-text"
                          >
                            {{ task?.leaveCode }}
                          </p>
                          <p
                            *ngIf="task?.hoursworked != '00:00'"
                            class="hours-worked-text-on-leave"
                          >
                            {{ task?.hoursworked | hoursWorkedSplit }}
                          </p>
                        </div>
                      </div>
                    </ng-container>
                  </ng-container>
                </ng-template>
              </ng-container>
            </div>
            <mat-divider
              [vertical]="true"
              class="vertical-divider"
            ></mat-divider>
          </ng-container>
          <div
            class="col"
            style="display: flex; flex-direction: column; align-items: center"
          >
            <div class="total-width">
              <p class="light-text">Total</p>
            </div>
            <div>
              <p
                class="small-light-text"
                style="margin-bottom: 0px; padding-top: 16px"
                [ngStyle]="{
                  color:
                    currentWeek
                    | weeklyHoursColorStamp
                      : basicTsConfig?.overtime_allowed_in_ts
                      : employee_quota_min_hours
                      : startDateOfEmployee
                      : triggerPipes
                      : basicTsConfig
                }"
              >
                {{
                  currentWeek?.days
                    | totalHoursWeekly
                      : basicTsConfig?.overtime_allowed_in_ts
                      : triggerPipes
                      : basicTsConfig
                }}
              </p>
            </div>
            <ng-container
              *ngFor="
                let time of currentWeek?.days[startDayIndex]?.costcenter;
                let i = index
              "
            >
              <div
                *ngIf="
                  time?.tasks || time?.subProjectId;
                  then hasTaskTotal;
                  else noTaskTotal
                "
              ></div>
              <ng-template #noTaskTotal>
                <div>
                  <p
                    class="small-light-text-m0"
                    style="margin-bottom: 0px"
                    [ngStyle]="{ 'margin-top': i === 0 ? '22px' : '28px' }"
                  >
                    {{
                      currentWeek
                        | totalHoursCcWeekly
                          : i
                          : -1
                          : basicTsConfig?.overtime_allowed_in_ts
                          : triggerPipes
                    }}
                  </p>
                </div>
              </ng-template>
              <ng-template #hasTaskTotal>
                <div>
                  <p
                    class="small-light-text-m0"
                    [ngStyle]="{
                      'margin-bottom':
                        time?.expanded === false
                          ? '5px'
                          : time?.tasks
                          ? time?.tasks.length === 0
                            ? '47px'
                            : '0px'
                          : time?.subProjectId && !time?.tasks
                          ? '47px'
                          : '0px',
                      'margin-top': i === 0 ? '22px' : '27px'
                    }"
                  >
                    {{
                      currentWeek
                        | totalHoursCcWeekly
                          : i
                          : -1
                          : basicTsConfig?.overtime_allowed_in_ts
                          : triggerPipes
                    }}
                  </p>
                </div>
                <ng-container
                  *ngFor="let taskTime of time?.tasks; let i2 = index"
                >
                  <div>
                    <p
                      *ngIf="time?.expanded === true"
                      class="small-light-text-m0"
                      [ngStyle]="{
                        'margin-top': i2 === 0 ? '26px' : '28px',
                        'margin-bottom':
                          i2 === time?.tasks.length - 1 &&
                          (currentWeek?.tsOverAllStatus === 1 ||
                            currentWeek?.tsOverAllStatus === 6)
                            ? '45px'
                            : i2 === time?.tasks.length - 1 &&
                              (currentWeek?.tsOverAllStatus !== 1 ||
                                currentWeek?.tsOverAllStatus !== 6)
                            ? '12px'
                            : '0px'
                      }"
                    >
                      {{
                        currentWeek
                          | totalHoursCcWeekly
                            : i
                            : i2
                            : basicTsConfig?.overtime_allowed_in_ts
                            : triggerPipes
                      }}
                    </p>
                  </div>
                </ng-container>
              </ng-template>
            </ng-container>
          </div>
          <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Hours pop up -->
<mat-menu yPosition="below" xPosition="after" #hoursPopUp="matMenu">
  <ng-template [matMenuContent]>
    <div (click)="$event.stopPropagation()" class="hours-popup">
      <div class="align-items-btw" style="margin-bottom: 8px">
        <div class="light-text-ba">Billable Hours</div>
        <div class="bold-text-ba">
          {{
            currentWeek?.days
              | totalBillingTypeHoursWeekly
                : basicTsConfig?.overtime_allowed_in_ts
                : true
          }}
        </div>
      </div>
      <div class="align-items-btw" style="margin-bottom: 8px">
        <div class="light-text-nba">Non Billable Hours</div>
        <div class="bold-text-nba">
          {{
            currentWeek?.days
              | totalBillingTypeHoursWeekly
                : basicTsConfig?.overtime_allowed_in_ts
                : false
          }}
        </div>
      </div>
      <mat-divider class="hours-divider"></mat-divider>
      <div class="align-items-btw" style="margin-top: 8px">
        <div class="light-text">Total Hours</div>
        <div class="bold-text">
          {{
            currentWeek?.days
              | totalHoursWeekly
                : basicTsConfig?.overtime_allowed_in_ts
                : triggerPipes
          }}
        </div>
      </div>
    </div>
  </ng-template>
</mat-menu>

<!-- Leave pop up -->
<mat-menu yPosition="below" xPosition="after" #leavePopUp="matMenu">
  <ng-template [matMenuContent]>
    <div (click)="$event.stopPropagation()" class="leave-popup">
      <ng-container *ngFor="let item of leaveDetailsForPopUp; let i = index">
        <div class="align-items-btw">
          <div>
            <p
              [style.color]="item?.leaveColourCode"
              class="bolder-text"
              [matTooltip]="
                item?.leaveName + (item?.id === 4 ? ' - Half Day' : '')
              "
            >
              {{ item?.leaveName }}{{ item?.id === 4 ? " - Half Day" : "" }}
            </p>
            <p
              [matTooltip]="item?.costcenter"
              class="lighter-text"
              [matTooltip]="item?.costcenter"
            >
              {{ item?.costcenter }}
            </p>
          </div>
          <div>
            <p class="bold-text" style="text-align: right">
              {{ item?.date | dateFormat3 }},
            </p>
            <p class="bold-text">{{ item?.date | dateFormat }}</p>
          </div>
        </div>
        <mat-divider class="leave-divider"></mat-divider>
      </ng-container>
      <div class="align-items-btw">
        <div class="light-text">Total Paid Leaves</div>
        <div class="bold-text">
          {{ paidLeaveCount | leaveCountZeroPatch }}
          Days
        </div>
      </div>
      <div class="align-items-btw">
        <div class="light-text">Total Unpaid Leaves</div>
        <div class="bold-text">
          {{ unpaidLeaveCount | leaveCountZeroPatch }}
          Days
        </div>
      </div>
      <div class="align-items-btw">
        <div class="light-text">Total Leaves</div>
        <div class="bold-text">
          {{ currentWeek | totalLeavesWeekly : triggerPipes }} Days
        </div>
      </div>
    </div>
  </ng-template>
</mat-menu>

<!-- Task URL pop up -->
<mat-menu yPosition="below" xPosition="after" #taskUrlPopUp="matMenu">
  <ng-template [matMenuContent]>
    <div (click)="$event.stopPropagation()" class="task-url-popup">
      <div class="align-items-btw" style="height: 28px">
        <div>
          <p class="bold-text" matTooltip="{{ selectedTaskName }}">
            {{ selectedTaskName }}
          </p>
        </div>
        <div>
          <mat-icon class="clear-icon" (click)="taskUrlTrigger.closeMenu()"
            >clear</mat-icon
          >
        </div>
      </div>
      <div class="url-text">
        <span *ngFor="let taskUrl of taskUrlArray; let last = last">
          {{ taskUrl }}
          <mat-icon *ngIf="!last" class="right-arrow"
            >keyboard_arrow_right</mat-icon
          >
        </span>
      </div>
    </div>
  </ng-template>
</mat-menu>

<!-- Tooltip to display data on hover after timesheet submission in cost center level-->
<dx-popover
  [visible]="isTooltipVisible"
  [target]="'#detailsTooltipCc' + dayIndex + ccIndex"
  position="bottom"
  [minWidth]="200"
  [maxWidth]="400"
>
  <div *dxTemplate="let data = data; of: 'content'">
    <div class="tooltip-details-text">
      Regular Hours:
      {{ hoursWorkedTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="basicTsConfig?.display_billing_type_checkbox == '1'"
    >
      Billable:
      {{ billableTooltip }}
    </div>
    <div class="tooltip-details-text">
      Location:
      {{ locationTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="basicTsConfig?.overtime_allowed_in_ts == '1'"
    >
      Overtime Hours:
      {{ overtimeHoursWorkedTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="
        basicTsConfig?.overtime_allowed_in_ts == '1' &&
        basicTsConfig?.display_billing_type_checkbox == '1'
      "
    >
      Overtime Billable:
      {{ overtimeBillableTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="basicTsConfig?.overtime_allowed_in_ts == '1'"
    >
      Overtime Location:
      {{ overtimeLocationTooltip }}
    </div>
    <div class="tooltip-details-text" *ngIf="leaveNameTooltip != null">
      Leave Type:
      {{ leaveNameTooltip }}
    </div>
    <div class="tooltip-details-text" *ngIf="holidayTooltip != ''">
      Holiday:
      {{ holidayTooltip }}
    </div>
    <div class="tooltip-details-text">
      Comments:
      {{ commentsTooltip != "" ? commentsTooltip : "No Comments Entered" }}
    </div>
  </div>
</dx-popover>

<!-- Tooltip to display data on hover after timesheet submission in task level-->
<dx-popover
  [visible]="isTooltipVisible"
  [target]="'#detailsTooltipTask' + dayIndex + ccIndex + taskIndex"
  position="bottom"
  [minWidth]="200"
  [maxWidth]="400"
>
  <div *dxTemplate="let data = data; of: 'content'">
    <div class="tooltip-details-text">
      Regular Hours:
      {{ hoursWorkedTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="basicTsConfig?.display_billing_type_checkbox == '1'"
    >
      Billable:
      {{ billableTooltip }}
    </div>
    <div class="tooltip-details-text">
      Location:
      {{ locationTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="basicTsConfig?.overtime_allowed_in_ts == '1'"
    >
      Overtime Hours:
      {{ overtimeHoursWorkedTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="
        basicTsConfig?.overtime_allowed_in_ts == '1' &&
        basicTsConfig?.display_billing_type_checkbox == '1'
      "
    >
      Overtime Billable:
      {{ overtimeBillableTooltip }}
    </div>
    <div
      class="tooltip-details-text"
      *ngIf="basicTsConfig?.overtime_allowed_in_ts == '1'"
    >
      Overtime Location:
      {{ overtimeLocationTooltip }}
    </div>
    <div class="tooltip-details-text" *ngIf="leaveNameTooltip != null">
      Leave Type:
      {{ leaveNameTooltip }}
    </div>
    <div class="tooltip-details-text" *ngIf="holidayTooltip != ''">
      Holiday:
      {{ holidayTooltip }}
    </div>
    <div class="tooltip-details-text">
      Comments:
      {{ commentsTooltip != "" ? commentsTooltip : "No Comments Entered" }}
    </div>
  </div>
</dx-popover>

<!-- Quick settings pop up wth arrow -->
<dx-popover
  [target]="'#quicksettings' + popoverCcIndex + popoverTaskIndex"
  [visible]="isQuickSettingsPopupVisible"
  showEvent="click"
  position="right"
>
  <div *dxTemplate="let data of 'content'">
    <div class="quick-settings-popup">
      <p
        (click)="enableQuickFill()"
        class="quick-settings-text"
        [ngStyle]="{
          'pointer-events':
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            ((!currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks &&
              !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                ?.subProjectId &&
              taskIndex === -1) ||
              ((currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks ||
                !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                  ?.subProjectId) &&
                taskIndex !== -1)) &&
            (currentWeek?.days
              | isQuickFillActive
                : basicTsConfig
                : popoverCcIndex
                : startDayIndex
                : null
                : null) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            ) &&
            !disableQuickFill
              ? 'auto'
              : 'none',
          opacity:
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            ((!currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks &&
              !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                ?.subProjectId &&
              taskIndex === -1) ||
              ((currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks ||
                !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                  ?.subProjectId) &&
                taskIndex !== -1)) &&
            (currentWeek?.days
              | isQuickFillActive
                : basicTsConfig
                : popoverCcIndex
                : startDayIndex
                : null
                : null) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            ) &&
            !disableQuickFill
              ? '1'
              : '0.3'
        }"
      >
        Quick Fill Whole Week..
      </p>
      <div
        style="margin-bottom: 8px; margin-top: 8px"
        class="align-items-column"
        *ngIf="settingsUi === 'quickFillUi'"
        [formGroup]="quickFillForm"
      >
        <div class="no-close-quick-settings" *ngIf="isDecimalTs === '1'">
          <mat-form-field
            appearance="outline"
            class="quick-fill-hours-form-field"
          >
            <input
              (keydown.Tab)="$event.stopPropagation()"
              type="text"
              placeholder="hh:mm"
              maxlength="5"
              (keydown)="onKeyDownPreventDecimal($event)"
              (paste)="preventPaste($event)"
              (keyup)="onInputChangeFormatTimeInQuickFill($event)"
              matInput
              formControlName="quickFillHours"
              #hoursworkedInput3
            />
          </mat-form-field>
        </div>
        <div class="no-close-quick-settings" *ngIf="isDecimalTs === '0'">
          <mat-form-field
            appearance="outline"
            class="quick-fill-hours-form-field"
          >
            <mat-label>Hours</mat-label>
            <input
              (keydown.Tab)="$event.stopPropagation()"
              matInput
              formControlName="quickFillHours"
              type="number"
              min="0"
              max="24"
              step="1"
              (keydown)="preventMinusSignAndMaxTwoDigit($event)"
              (input)="restrictOutOfRangeValues($event, 'quickFill')"
              (paste)="preventDecimalOnPaste($event)"
              #hoursworkedInput4
            />
          </mat-form-field>
        </div>
        <div class="no-close-quick-settings">
          <button class="clear-btn" (click)="clearQuickFillPopUp()">
            Clear
          </button>
          <button
            class="add-btn close-quick-settings"
            (click)="addQuickFillPopUp(); isQuickSettingsPopupVisible = false"
            [disabled]="!quickFillFormValidations('Quick Fill Hours')"
          >
            Add Hours
          </button>
        </div>
      </div>
      <p
        (click)="enablePlannedHours()"
        class="quick-settings-text mt8"
        *ngIf="
          basicTsConfig?.allow_planned_hours_booking_in_ts_in_project_cc ==
            '1' &&
          taskIndex !== -1 &&
          !isDeptCc &&
          isEtcAllowed
        "
        [ngStyle]="{
          'pointer-events':
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            )
              ? 'auto'
              : 'none',
          opacity:
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            )
              ? '1'
              : '0.3'
        }"
      >
        ETC
      </p>
      <div
        style="margin-bottom: 8px; margin-top: 8px"
        class="align-items-column"
        *ngIf="settingsUi === 'plannedHoursUi'"
        [formGroup]="quickFillForm"
      >
        <div
          class="no-close-quick-settings"
          style="display: flex; flex-direction: row"
        >
          <div style="margin-right: 10px">
            <mat-form-field
              appearance="outline"
              class="planned-hours-form-field"
            >
              <input
                (keydown.Tab)="$event.stopPropagation()"
                placeholder="Hours"
                type="number"
                step="1"
                min="0"
                max="2147483647"
                matInput
                formControlName="plannedHours"
                (keydown)="preventMinusSign($event)"
                (paste)="preventDecimalOnPaste($event)"
                (input)="restrictOutOfRangeValues($event, 'plannedHours')"
              />
            </mat-form-field>
          </div>
          <div>
            <mat-form-field
              appearance="outline"
              class="planned-hours-form-field"
            >
              <input
                (keydown.Tab)="$event.stopPropagation()"
                placeholder="Mins"
                type="number"
                step="1"
                min="0"
                max="59"
                matInput
                formControlName="plannedMins"
                (keydown)="preventMinusSignAndMaxTwoDigit($event)"
                (input)="restrictOutOfRangeValues($event, 'plannedMins')"
                (paste)="preventDecimalOnPaste($event)"
              />
            </mat-form-field>
          </div>
        </div>
        <div class="no-close-quick-settings">
          <button class="clear-btn" (click)="clearPlannedHoursPopUp()">
            Clear
          </button>
          <button
            class="add-btn close-quick-settings"
            (click)="
              addPlannedHoursPopUp(); isQuickSettingsPopupVisible = false
            "
            [disabled]="!quickFillFormValidations('Planned Hours')"
          >
            Add Hours
          </button>
        </div>
      </div>
      <p
        class="quick-settings-text mt8 close-quick-settings"
        (click)="
          removeCcOrTaskFromTimesheet(); isQuickSettingsPopupVisible = false
        "
        [ngStyle]="{
          'pointer-events':
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            )
              ? 'auto'
              : 'none',
          opacity:
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            )
              ? '1'
              : '0.3'
        }"
      >
        Remove From Timesheet
      </p>
      <p
        class="quick-settings-text mt8 close-quick-settings"
        (click)="clearTimeForWholeWeek(); isQuickSettingsPopupVisible = false"
        [ngStyle]="{
          'pointer-events':
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            ((!currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks &&
              !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                ?.subProjectId &&
              taskIndex === -1) ||
              ((currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks ||
                !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                  ?.subProjectId) &&
                taskIndex !== -1)) &&
            (currentWeek?.days
              | isQuickFillActive
                : basicTsConfig
                : popoverCcIndex
                : startDayIndex
                : null
                : null) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            ) &&
            !disableQuickFill
              ? 'auto'
              : 'none',
          opacity:
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            ((!currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks &&
              !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                ?.subProjectId &&
              taskIndex === -1) ||
              ((currentWeek?.days[startDayIndex]?.costcenter[ccIndex]?.tasks ||
                !currentWeek?.days[startDayIndex]?.costcenter[ccIndex]
                  ?.subProjectId) &&
                taskIndex !== -1)) &&
            (currentWeek?.days
              | isQuickFillActive
                : basicTsConfig
                : popoverCcIndex
                : startDayIndex
                : null
                : null) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            ) &&
            !disableQuickFill
              ? '1'
              : '0.3'
        }"
      >
        Clear Time Entries
      </p>
      <p
        *ngIf="taskIndex === -1"
        class="quick-settings-text mt8 close-quick-settings"
        [ngStyle]="{
          'pointer-events':
            (currentWeek?.tsOverAllStatus !== 1 ||
              currentWeek?.tsOverAllStatus !== 6) &&
            taskIndex === -1
              ? 'auto'
              : 'none',
          opacity:
            (currentWeek?.tsOverAllStatus !== 1 ||
              currentWeek?.tsOverAllStatus !== 6) &&
            taskIndex === -1
              ? '1'
              : '0.3'
        }"
        (click)="onClickHistory(); isQuickSettingsPopupVisible = false"
      >
        History
      </p>
      <p
        (click)="
          onClickPlannedHoursHistory(); isQuickSettingsPopupVisible = false
        "
        class="quick-settings-text mt8 close-quick-settings"
        *ngIf="
          basicTsConfig?.allow_planned_hours_booking_in_ts_in_project_cc ==
            '1' &&
          taskIndex !== -1 &&
          !isDeptCc &&
          displayEtcLog
        "
      >
        ETC Logs
      </p>
      <p
        *ngIf="
          taskIndex === -1 && basicTsConfig?.manual_approver_selection == '1'
        "
        class="quick-settings-text mt8 close-quick-settings"
        [ngStyle]="{
          'pointer-events':
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            )
              ? 'auto'
              : 'none',
          opacity:
            (currentWeek?.tsOverAllStatus === 1 ||
              currentWeek?.tsOverAllStatus === 6) &&
            !(
              (basicTsConfig
                | isTimesheetSubmissionLocked
                  : monthEndDate
                  : (currentWeek?.days | getCurrentWeekEndDate)
                  : overallTimesheetStatusBeforeEdit
                  : currentWeek?.tsOverAllStatus
                  : isPrevHistoryRejected
                  : employeeConfig[0]?.is_timesheet_edit_allowed
                  : openTsForLeaveWeekIntegrationEdit) &&
              (basicTsConfig
                | noOfPastMonthsAllowed
                  : monthStartDate
                  : employeeConfig[0]?.is_timesheet_edit_allowed)
            ) &&
            !(
              basicTsConfig
              | noOfFutureMonthsAllowed : monthEndDate : currentWeek?.startDate
            )
              ? '1'
              : '0.3'
        }"
        (click)="onClickEditApprovers(); isQuickSettingsPopupVisible = false"
      >
        Edit Approvers
      </p>
    </div>
  </div>
</dx-popover>

<!-- Time fill popup with arrow -->
<dx-popover
  [visible]="isFillHoursPopupVisible"
  [width]="446"
  [minHeight]="150"
  [maxHeight]="370"
  [target]="'#hoursFillPopup' + dayIndex + ccIndex + taskIndex"
  position="bottom"
  [closeOnOutsideClick]="false"
  *ngIf="basicTsConfig"
>
  <form
    [formGroup]="timeFillForm"
    (keydown.enter)="onSubmitUsingEnterKey($event)"
  >
    <div
      class="time-fill-popup"
      (click)="$event.stopPropagation()"
      style="overflow: hidden"
    >
      <div class="align-items-btw">
        <div>
          <p class="fill-card-title-text">
            {{ timeFillHeaderDate | dateFormat }}
          </p>
        </div>
        <div class="align-items-center">
          <div>
            <p class="fill-card-title-text" matTooltip="{{ timeFillHeaderCc }}">
              {{ timeFillHeaderCc | ccDisplayFormat : 10 }}
            </p>
          </div>
          <div class="icon-margin">
            <mat-icon
              class="clear-icon"
              (click)="closeTimeFill(); isFillHoursPopupVisible = false"
              >clear</mat-icon
            >
          </div>
        </div>
      </div>
      <mat-divider class="divider-fill-popup"></mat-divider>
      <mat-button-toggle-group
        style="
          display: flex;
          justify-content: center;
          margin-bottom: 8px;
          margin-top: 6px;
        "
        *ngIf="togglePopup.length > 1 && istogglePopupVisible"
      >
        <mat-button-toggle
          *ngFor="let item of togglePopup"
          (click)="onSelectToggle(item)"
          [value]="item"
          class="toggle-btn"
          [ngClass]="{
            'btn-toggle-selected': selectedToggle == item
          }"
        >
          {{ item }}</mat-button-toggle
        >
      </mat-button-toggle-group>
      <div
        *ngIf="togglePopup.length <= 1 || !istogglePopupVisible"
        style="margin-bottom: 8px"
      ></div>
      <ng-container *ngIf="selectedToggle == 'Regular'">
        <div class="align-items-center">
          <div class="mr10 hours-field-highlight" *ngIf="isDecimalTs === '0'">
            <p class="form-label">Hours Worked</p>
            <mat-form-field appearance="outline" class="hours-form-field">
              <input
                #hoursworkedInput1
                (input)="
                  reverseFromAndToTime('Regular');
                  restrictOutOfRangeValues($event, 'hoursworked')
                "
                (paste)="preventDecimalOnPaste($event)"
                (keydown.Tab)="$event.stopPropagation()"
                matInput
                formControlName="hoursworked"
                type="number"
                min="0"
                max="24"
                step="1"
                (keydown)="preventMinusSignAndMaxTwoDigit($event)"
              />
            </mat-form-field>
          </div>
          <div class="mr10 hours-field-highlight" *ngIf="isDecimalTs === '1'">
            <p class="form-label">Hours Worked</p>
            <mat-form-field appearance="outline" class="hours-form-field">
              <input
                #hoursworkedInput2
                (keydown.Tab)="$event.stopPropagation()"
                type="text"
                placeholder="hh:mm"
                maxlength="5"
                (keydown)="onKeyDownPreventDecimal($event)"
                (paste)="preventPaste($event)"
                (keyup)="
                  onInputChangeFormatTime($event);
                  reverseFromAndToTime('Regular')
                "
                matInput
                formControlName="hoursworked"
              />
            </mat-form-field>
          </div>
          <div class="mr10" *ngIf="basicTsConfig?.display_from_to_time == '1'">
            <p class="form-label">Time In</p>
            <mat-form-field appearance="outline" class="from-form-field">
              <input
                (keydown.Tab)="$event.stopPropagation()"
                matInput
                type="text"
                placeholder="hh:mm"
                maxlength="5"
                (keydown)="onKeyDownPreventDecimal($event)"
                (keyup)="onInputFormatInAndOut($event, 'fromTime')"
                (paste)="preventPaste($event)"
                formControlName="fromTime"
              />
            </mat-form-field>
          </div>
          <div class="mr10" *ngIf="basicTsConfig?.display_from_to_time == '1'">
            <p class="form-label">Time Out</p>
            <mat-form-field appearance="outline" class="to-form-field">
              <input
                (keydown.Tab)="$event.stopPropagation()"
                matInput
                type="text"
                placeholder="hh:mm"
                maxlength="5"
                (keydown)="onKeyDownPreventDecimal($event)"
                (keyup)="onInputFormatInAndOut($event, 'toTime')"
                (paste)="preventPaste($event)"
                formControlName="toTime"
              />
            </mat-form-field>
          </div>
          <div>
            <p class="form-label">Location *</p>
            <app-input-search
              (keydown.Tab)="$event.stopPropagation()"
              (keydown.enter)="$event.stopPropagation()"
              class="location-form-field"
              [list]="locationList"
              [hideMatLabel]="true"
              placeholder="Select"
              formControlName="location"
              [matTooltip]="locationConfig[timeFillForm.value.location]?.name"
              [disableNone]="true"
            >
            </app-input-search>
          </div>
        </div>
        <div class="align-items-center" style="justify-content: space-between">
          <div style="display: flex">
            <div
              class="p-0 mr10"
              *ngIf="basicTsConfig?.display_hours_splitup == '1'"
            >
              <mat-checkbox
                (keydown.Tab)="$event.stopPropagation()"
                width="16px"
                height="16px"
                formControlName="isHoursSplitup"
                [ngStyle]="{'pointer-events': isHoursSplitupDisabled ? 'none' : 'auto'}"
                [disabled]="isHoursSplitupDisabled"
                [(ngModel)]="isHoursSplitup"
                (ngModelChange)="isHoursSplitupChanges()"
              ></mat-checkbox>
            </div>
            <div
              class="p-0 mr20"
              *ngIf="basicTsConfig?.display_hours_splitup == '1'"
            >
              <p class="billable-txt">Hours Split Up</p>
            </div>
            <div
              class="p-0 mr10"
              *ngIf="basicTsConfig?.display_billing_type_checkbox == '1'"
            >
              <mat-checkbox
                (keydown.Tab)="$event.stopPropagation()"
                width="16px"
                height="16px"
                formControlName="isBillable"
              ></mat-checkbox>
            </div>
            <div
              class="p-0 mr20"
              *ngIf="basicTsConfig?.display_billing_type_checkbox == '1'"
            >
              <p class="billable-txt">Billable</p>
            </div>
            <div
              class="p-0 mr10"
              *ngIf="
                basicTsConfig.is_holiday_need_to_be_displayed_in_timesheet ===
                  '1' &&
                (timeFillHeaderDate | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : (dayIndex != -1 && ccIndex != - 1 && basicTsConfig?.show_holidays_based_on_projects == 1 && currentWeek?.days[dayIndex]?.costcenter && currentWeek?.days[dayIndex]?.costcenter[ccIndex] ? currentWeek?.days[dayIndex]?.costcenter[ccIndex]['costCenterId'] : 0)) &&
                holidayDetails.length > 0
              "
            >
              <mat-checkbox
                (keydown.Tab)="$event.stopPropagation()"
                width="16px"
                height="16px"
                formControlName="isHoliday"
              ></mat-checkbox>
            </div>
            <div
              class="p-0 mr20"
              *ngIf="
                basicTsConfig.is_holiday_need_to_be_displayed_in_timesheet ===
                  '1' &&
                (timeFillHeaderDate | holidayTooltip : holidayConfig : basicTsConfig?.show_holidays_based_on_projects : (dayIndex != -1 && ccIndex != - 1 && basicTsConfig?.show_holidays_based_on_projects == 1 && currentWeek?.days[dayIndex]?.costcenter && currentWeek?.days[dayIndex]?.costcenter[ccIndex] ? currentWeek?.days[dayIndex]?.costcenter[ccIndex]['costCenterId'] : 0)) &&
                holidayDetails.length > 0
              "
            >
              <p class="billable-txt">Holiday</p>
            </div>
            <div
              class="p-0 mr10"
              *ngIf="
                basicTsConfig
                  | displayWeekOffCheckbox
                    : employee_wo
                    : dayIndex
                    : currentWeek
              "
            >
              <mat-checkbox
                (keydown.Tab)="$event.stopPropagation()"
                width="16px"
                height="16px"
                formControlName="isWeekOff"
                (change)="isWeekOffCheckboxChange($event)"
              ></mat-checkbox>
            </div>
            <div
              class="p-0 mr20"
              *ngIf="
                basicTsConfig
                  | displayWeekOffCheckbox
                    : employee_wo
                    : dayIndex
                    : currentWeek
              "
            >
              <p class="billable-txt">Week Off</p>
            </div>
            <div
              class="p-0 mr10"
              *ngIf="
                basicTsConfig.show_nwd_checkbox === '1' &&
                nwdDetails.length > 0 &&
                employeeConfig[0]?.employment_type_id ==
                  basicTsConfig.nwd_allowed_employment_type
              "
            >
              <mat-checkbox
                (keydown.Tab)="$event.stopPropagation()"
                width="16px"
                height="16px"
                formControlName="isNwd"
              ></mat-checkbox>
            </div>
            <div
              class="p-0 mr20"
              *ngIf="
                basicTsConfig.show_nwd_checkbox === '1' &&
                nwdDetails.length > 0 &&
                employeeConfig[0]?.employment_type_id ==
                  basicTsConfig.nwd_allowed_employment_type
              "
            >
              <p class="billable-txt">NWD</p>
            </div>
          </div>
          <div
            (click)="storeClockInOutDetails(1)"
            class="timer-off-bg"
            [ngClass]="{
              'timer-on-bg': isTimerOn ? true : false
            }"
            [ngStyle]="{
              'pointer-events': isTimerApiRunning ? 'none' : ''
            }"
            *ngIf="basicTsConfig | displayTimer : ccIndex : taskIndex"
          >
            <mat-icon
              class="timer-off-icon"
              [ngClass]="{
                'timer-on-icon': isTimerOn ? true : false
              }"
              >timer</mat-icon
            >
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="selectedToggle === 'Leave'">
        <div class="align-items-center">
          <div class="mr10">
            <p class="form-label">Leave Type *</p>
            <app-input-search
              (keydown.Tab)="$event.stopPropagation()"
              class="leave-form-field"
              [list]="leaveBalanceSearchList"
              [hideMatLabel]="true"
              placeholder="Leave Type"
              formControlName="leaveType"
              [disableNone]="true"
            >
            </app-input-search>
          </div>
          <div
            class="p-0 mr10"
            style="margin-top: 12px; margin-left: 5px; margin-right: 5px"
            *ngIf="basicTsConfig.display_leave_dropdown_ui === '1'"
          >
            <mat-checkbox
              (keydown.Tab)="$event.stopPropagation()"
              width="16px"
              height="16px"
              formControlName="isHalfDay"
            ></mat-checkbox>
          </div>
          <div
            class="p-0"
            style="margin-top: 11px; margin-right: 5px"
            *ngIf="basicTsConfig.display_leave_dropdown_ui === '1'"
          >
            <p class="billable-txt">Half Day</p>
          </div>
          <div>
            <p class="form-label">Location *</p>
            <app-input-search
              (keydown.Tab)="$event.stopPropagation()"
              class="location-form-field"
              [list]="locationList"
              [hideMatLabel]="true"
              placeholder="Location"
              formControlName="location"
              [matTooltip]="locationConfig[timeFillForm.value.location]?.name"
              [disableNone]="true"
            >
            </app-input-search>
          </div>
        </div>
        <ng-container
          *ngIf="
            timeFillForm.value.isHalfDay == true &&
            basicTsConfig?.leave_booking_config_in_ts == '0'
          "
        >
          <div class="align-items-center">
            <div class="mr10" *ngIf="isDecimalTs === '0'">
              <p class="form-label">Hours Worked</p>
              <mat-form-field appearance="outline" class="hours-form-field">
                <input
                  (input)="
                    reverseFromAndToTime('Regular');
                    restrictOutOfRangeValues($event, 'hoursworked')
                  "
                  (paste)="preventDecimalOnPaste($event)"
                  (keydown.Tab)="$event.stopPropagation()"
                  matInput
                  formControlName="hoursworked"
                  type="number"
                  min="0"
                  max="24"
                  step="1"
                  (keydown)="preventMinusSignAndMaxTwoDigit($event)"
                />
              </mat-form-field>
            </div>
            <div class="mr10" *ngIf="isDecimalTs === '1'">
              <p class="form-label">Hours Worked</p>
              <mat-form-field appearance="outline" class="hours-form-field">
                <input
                  (keydown.Tab)="$event.stopPropagation()"
                  type="text"
                  placeholder="hh:mm"
                  maxlength="5"
                  (keydown)="onKeyDownPreventDecimal($event)"
                  (paste)="preventPaste($event)"
                  (keyup)="
                    onInputChangeFormatTime($event);
                    reverseFromAndToTime('Regular')
                  "
                  matInput
                  formControlName="hoursworked"
                />
              </mat-form-field>
            </div>
            <div
              class="mr10"
              *ngIf="basicTsConfig?.display_from_to_time == '1'"
            >
              <p class="form-label">Time In</p>
              <mat-form-field appearance="outline" class="from-form-field">
                <input
                  (keydown.Tab)="$event.stopPropagation()"
                  matInput
                  type="text"
                  placeholder="hh:mm"
                  maxlength="5"
                  (keydown)="onKeyDownPreventDecimal($event)"
                  (keyup)="onInputFormatInAndOut($event, 'fromTime')"
                  (paste)="preventPaste($event)"
                  formControlName="fromTime"
                />
              </mat-form-field>
            </div>
            <div
              class="mr10"
              *ngIf="basicTsConfig?.display_from_to_time == '1'"
            >
              <p class="form-label">Time Out</p>
              <mat-form-field appearance="outline" class="to-form-field">
                <input
                  (keydown.Tab)="$event.stopPropagation()"
                  matInput
                  type="text"
                  placeholder="hh:mm"
                  maxlength="5"
                  (keydown)="onKeyDownPreventDecimal($event)"
                  (keyup)="onInputFormatInAndOut($event, 'toTime')"
                  (paste)="preventPaste($event)"
                  formControlName="toTime"
                />
              </mat-form-field>
            </div>
            <div
              class="p-0 mr10"
              style="margin-top: 12px"
              *ngIf="basicTsConfig?.display_billing_type_checkbox == '1'"
            >
              <mat-checkbox
                (keydown.Tab)="$event.stopPropagation()"
                width="16px"
                height="16px"
                formControlName="isBillable"
              ></mat-checkbox>
            </div>
            <div
              class="p-0"
              style="margin-top: 11px"
              *ngIf="basicTsConfig?.display_billing_type_checkbox == '1'"
            >
              <p class="billable-txt">Billable</p>
            </div>
          </div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="selectedToggle == 'Overtime'">
        <div class="align-items-center">
          <div class="mr10" *ngIf="isDecimalTs === '0'">
            <p class="form-label">Hours Worked</p>
            <mat-form-field appearance="outline" class="hours-form-field">
              <input
                (input)="
                  reverseFromAndToTime('Overtime');
                  restrictOutOfRangeValues($event, 'overtimehoursworked')
                "
                (paste)="preventDecimalOnPaste($event)"
                (keydown.Tab)="$event.stopPropagation()"
                matInput
                formControlName="overtimeHours"
                type="number"
                min="0"
                max="24"
                step="1"
                (keydown)="preventMinusSignAndMaxTwoDigit($event)"
              />
            </mat-form-field>
          </div>
          <div class="mr10" *ngIf="isDecimalTs === '1'">
            <p class="form-label">Hours Worked</p>
            <mat-form-field appearance="outline" class="hours-form-field">
              <input
                (keydown.Tab)="$event.stopPropagation()"
                type="text"
                placeholder="hh:mm"
                maxlength="5"
                (keydown)="onKeyDownPreventDecimal($event)"
                (paste)="preventPaste($event)"
                (keyup)="
                  onInputChangeFormatOvertimeTime($event);
                  reverseFromAndToTime('Overtime')
                "
                matInput
                formControlName="overtimeHours"
              />
            </mat-form-field>
          </div>
          <!-- <div class="mr10">
            <p class="form-label">Time In</p>
            <mat-form-field appearance="outline" class="from-form-field">
              <input
                (keydown.Tab)="$event.stopPropagation()"
                matInput
                type="text"
                placeholder="hh:mm"
                maxlength="5"
                (keyup)="onInputFormatInAndOut($event, 'overtimeFromTime')"
                formControlName="overtimeFromTime"
              />
            </mat-form-field>
          </div>
          <div class="mr10">
            <p class="form-label">Time Out</p>
            <mat-form-field appearance="outline" class="to-form-field">
              <input
                (keydown.Tab)="$event.stopPropagation()"
                matInput
                type="text"
                placeholder="hh:mm"
                maxlength="5"
                (keyup)="onInputFormatInAndOut($event, 'overtimeToTime')"
                formControlName="overtimeToTime"
              />
            </mat-form-field>
          </div> -->
          <div>
            <p class="form-label">Location *</p>
            <app-input-search
              (keydown.Tab)="$event.stopPropagation()"
              (keydown.enter)="$event.stopPropagation()"
              class="location-form-field"
              [list]="locationList"
              [hideMatLabel]="true"
              placeholder="Select"
              formControlName="overtimeLocation"
              [matTooltip]="
                locationConfig[timeFillForm.value.overtimeLocation]?.name
              "
              [disableNone]="true"
            >
            </app-input-search>
          </div>
        </div>
        <div class="align-items-center">
          <div class="p-0 mr10">
            <mat-checkbox
              (keydown.Tab)="$event.stopPropagation()"
              width="16px"
              height="16px"
              formControlName="overtimeBillable"
            ></mat-checkbox>
          </div>
          <div class="p-0">
            <p class="billable-txt">Billable</p>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="!isHoursSplitup">
        <mat-form-field
          appearance="outline"
          class="add-msg-field"
          [matTooltip]="timeFillForm.value.msg"
        >
          <input
            type="text"
            maxlength="200"
            matInput
            (keydown.Tab)="$event.stopPropagation()"
            formControlName="msg"
            placeholder="Enter comments here"
          />
        </mat-form-field>
      </ng-container>
      <ng-container formArrayName="hours_splitup" *ngIf="isHoursSplitup">
        <span class="form-label" style="margin-top: 6px">Hours Split Up</span>
        <dx-scroll-view>
          <div class="align-items-column-array">
            <div
              class="align-items-center"
              *ngFor="
                let singleSplit of timeFillForm.get('hours_splitup').controls;
                let singleSplitIndex = index
              "
              [formGroupName]="singleSplitIndex"
            >
              <div class="mr10">
                <p class="form-label" *ngIf="singleSplitIndex == 0">Hours *</p>
                <mat-form-field appearance="outline" class="hours-form-field">
                  <input
                    (keydown.Tab)="$event.stopPropagation()"
                    type="text"
                    placeholder="hh:mm"
                    maxlength="5"
                    (keydown)="onKeyDownPreventDecimal($event)"
                    (paste)="preventPaste($event)"
                    (keyup)="
                      onInputChangeFormatSplitupTime($event, singleSplitIndex)
                    "
                    (input)="reverseHoursSplitupFromAndToTime(singleSplitIndex)"
                    matInput
                    formControlName="hours"
                  />
                </mat-form-field>
              </div>
              <div class="mr10" *ngIf="basicTsConfig?.is_break_allowed_in_ts == 1 && basicTsConfig?.break_cc_id != currentCCId">
                <p class="form-label" *ngIf="singleSplitIndex == 0">Comments</p>
                <mat-form-field
                  appearance="outline"
                  class="add-comment-field"
                  [matTooltip]="singleSplit.get('comments').value"
                >
                  <input
                    type="text"
                    maxlength="200"
                    matInput
                    (keydown.Tab)="$event.stopPropagation()"
                    formControlName="comments"
                    placeholder="Type here..."
                  />
                </mat-form-field>
              </div>
              <div class="mr10" *ngIf="basicTsConfig?.is_break_allowed_in_ts == 1 && basicTsConfig?.break_cc_id == currentCCId">
                <p class="form-label" *ngIf="singleSplitIndex == 0">Time In</p>
                <mat-form-field appearance="outline" class="from-form-field">
                  <input
                    (keydown.Tab)="$event.stopPropagation()"
                    matInput
                    type="text"
                    placeholder="hh:mm"
                    maxlength="5"
                    (keydown)="onKeyDownPreventDecimal($event)"
                    (keyup)="onInputFormatHoursSplitUpInAndOut($event, 'hoursSplitFromTime', singleSplitIndex)"
                    (paste)="preventPaste($event)"
                    formControlName="inTime"
                  />
                </mat-form-field>
              </div>
              <div class="mr10" *ngIf="basicTsConfig?.is_break_allowed_in_ts == 1 && basicTsConfig?.break_cc_id == currentCCId">
                <p class="form-label" *ngIf="singleSplitIndex == 0">Time Out</p>
                <mat-form-field appearance="outline" class="to-form-field">
                  <input
                    (keydown.Tab)="$event.stopPropagation()"
                    matInput
                    type="text"
                    placeholder="hh:mm"
                    maxlength="5"
                    (keydown)="onKeyDownPreventDecimal($event)"
                    (keyup)="onInputFormatHoursSplitUpInAndOut($event, 'hoursSplitToTime', singleSplitIndex)"
                    (paste)="preventPaste($event)"
                    formControlName="outTime"
                  />
                </mat-form-field>
              </div>
              <div
                class="mr10 add-split-btn"
                (click)="addHoursSplitupFormGroup(singleSplitIndex)"
              >
                <mat-icon class="add-icon">add</mat-icon>
              </div>
              <div
                *ngIf="timeFillForm.get('hours_splitup').controls.length > 1"
                class="delete-split-btn"
                (click)="deleteHoursSplitupArray(singleSplitIndex)"
              >
                <mat-icon class="delete-icon">delete</mat-icon>
              </div>
            </div>
          </div>
        </dx-scroll-view>
      </ng-container>
      <div
        class="align-items-center"
        style="margin-top: 6px"
        [ngStyle]="{
          'justify-content':
            basicTsConfig?.allow_planned_hours_booking_in_ts_in_project_cc ==
              '1' &&
            taskIndex != -1 &&
            selectedToggle == 'Regular' &&
            !isDeptCc &&
            isEtcAllowed
              ? 'space-between'
              : 'flex-end'
        }"
      >
        <div
          style="display: flex; flex-direction: row"
          *ngIf="
            basicTsConfig?.allow_planned_hours_booking_in_ts_in_project_cc ==
              '1' &&
            taskIndex != -1 &&
            selectedToggle == 'Regular' &&
            !isDeptCc &&
            isEtcAllowed
          "
        >
          <div style="margin-right: 10px">
            <p class="form-label">ETC - Hours</p>
            <mat-form-field
              appearance="outline"
              class="planned-hours-form-field"
            >
              <input
                (keydown.Tab)="$event.stopPropagation()"
                placeholder="Hours"
                type="number"
                step="1"
                min="0"
                matInput
                formControlName="plannedHours"
                (keydown)="preventMinusSign($event)"
                (paste)="preventDecimalOnPaste($event)"
                (input)="
                  restrictOutOfRangeValues($event, 'plannedHoursTimeFill')
                "
              />
            </mat-form-field>
          </div>
          <div style="margin-right: 10px">
            <p class="form-label">ETC - Mins</p>
            <mat-form-field
              appearance="outline"
              class="planned-hours-form-field"
            >
              <input
                (keydown.Tab)="$event.stopPropagation()"
                placeholder="Mins"
                type="number"
                step="1"
                min="0"
                max="59"
                matInput
                formControlName="plannedMins"
                (keydown)="preventMinusSignAndMaxTwoDigit($event)"
                (input)="
                  restrictOutOfRangeValues($event, 'plannedMinsTimeFill')
                "
                (paste)="preventDecimalOnPaste($event)"
              />
            </mat-form-field>
          </div>
          <div class="consumed-hours">
            <p class="form-label" style="margin-bottom: 10px">Consumed Hours</p>
            <p
              class="form-label"
              style="
                font-size: 12px;
                font-weight: 600;
                text-overflow: ellipsis;
                overflow: hidden;
              "
              [matTooltip]="consumedHoursFormatted | hoursWorkedSplit"
            >
              {{ consumedHoursFormatted | hoursWorkedSplit }}
            </p>
          </div>
        </div>
        <div style="display: flex">
          <div class="p-0">
            <button
              class="clear-btn"
              (click)="onClearTimeFill(); isFillHoursPopupVisible = false"
            >
              Clear
            </button>
          </div>
          <div class="p-0">
            <button
              #updateBtn
              type="submit"
              class="update-btn"
              [disabled]="!timeFillFormValidations()"
              (click)="onSubmitTimeFill(); isFillHoursPopupVisible = false"
            >
              Update
            </button>
          </div>
        </div>
      </div>
    </div>
  </form>
</dx-popover>
