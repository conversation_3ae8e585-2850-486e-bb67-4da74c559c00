import {
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  OnInit,
  Output,
  ViewChild,
  HostListener,
} from '@angular/core';
import { FileUploader } from 'ng2-file-upload';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { SharedLazyLoadedComponentsService } from '../../../services/shared-lazy-loaded-components.service';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { DocumentViewerComponent } from '../document-viewer/document-viewer.component';
import { LoginService } from 'src/app/services/login/login.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { FormGroup, FormControl } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import * as _ from 'underscore';
import { DocumentManagerService } from '../../service/document-manager.service';

@Component({
  selector: 'app-document-upload',
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss'],
})
export class DocumentUploadComponent implements OnInit {
  @Output() closeDialog: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('fileInput') fileInput: ElementRef;
  expHeaderId: any;
  routingKey: any;
  documentType: any;
  destinationBucket: any;
  patchAndDisable: any;
  filesCreatedAndUploaded: boolean = false;
  selectedTypeId: any;
  miniLoader: boolean = false;
  isUploading: boolean = false;
  existingDocumentType: any;

  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick();
  }

  @ViewChild('editorContent') editorElement: any;

  URL = '/api/exPrimary/uploadObjectFromDevice';
  hasBaseDropZoneOver: boolean;
  uploader: FileUploader;
  filesUploaded: any = [];
  isEditingAllowed: boolean = true;
  selectedFile: any;
  isFileAdded: boolean = false;
  row: any;
  id: any;
  formConfig: any;
  type_selected: any = 3;
  allowedMimeType = ['*/*'];
  maxFileSize = 10 * 1024 * 1024; // 10MB
  fileType = environment.fileuploadedSupportedFileTypes;
  isDialogOpened: boolean = false;
  projectAttachmentConfig: any;
  contextId: any;
  optionTabAccess: boolean = false;
  descriptionTabAccess: boolean = false;
  advanceTabAccess: boolean = false;
  projectFormConfig: any;
  tab_selected_color: any = '#EE4961';
  description: any = '';
  mode: any;
  file_name: any;
  validError: boolean = false;
  name_length: any;
  color: any;
  fontStyle: any;
  fileTypeDropDown: any = [];
  formGroup: FormGroup;
  attachmentType: any;
  attachmentTypeName: any;
  reviewersList: any = [];
  constructor(
    public dialogRef: MatDialogRef<DocumentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private loginService: LoginService,
    private sharedService: SharedLazyLoadedComponentsService,
    public dialog: MatDialog,
    private utilityService: UtilityService,
    private toasterService: ToasterService,
    private documentService: DocumentManagerService
  ) {
    dialogRef.disableClose = true;

    dialogRef.backdropClick().subscribe(() => {
      this.onCloseClick();
    });
    console.log('Received data from parent:', data);
    this.data = data['data'];
    this.expHeaderId = data['expHeaderId'];
    this.contextId = this.data.contextId;
    this.routingKey = this.data.routingKey;
    this.documentType = this.data.documentType || '';
    this.destinationBucket = this.data.destinationBucket;


    this.fileTypeDropDown = this.data.fileTypeDropDown;
    this.formConfig = this.data.formConfig;
    this.mode = this.data.mode;
    this.color = this.data.color;
    this.attachmentType = this.data?.document_type_id;
    this.attachmentTypeName = this.data?.documentType;
    this.patchAndDisable = this.data?.patchAndDisable;

    if (this.mode == 'edit') {
      this.file_name = data['data']['name'];
      this.description = data['data']['description'];
    }
  }

  ngOnInit(): void {
    this.formGroup = new FormGroup({
      attachmentType: new FormControl(''),
      attachmentTypeName: new FormControl(''),
    });

    if (this.data?.existingDocument) {
      this.filesUploaded = [this.data.existingDocument];
      this.existingDocumentType = this.data.existingDocument.document_type_id;
      this.formGroup.get('attachmentType')?.patchValue(this.data.existingDocument.document_type_id);
      this.selectedTypeId = this.data.existingDocument.document_type_id;
    }

    this.formGroup.get('attachmentType').patchValue(this.attachmentType);
    this.formGroup.get('attachmentTypeName').patchValue(this.attachmentTypeName);

    this.formGroup.get('attachmentType')?.valueChanges.subscribe(value => {
      const selectedFileType = this.fileTypeDropDown.find((fileType: any) => fileType.id === value);
      if (selectedFileType) {
        console.log('Selected document_type:', selectedFileType.document_type);
        this.getApproversBasedOnType(selectedFileType.document_type);
      }
    });

    // if (this.attachmentTypeName === 'po_value')
    //   this.getApproversBasedOnType('po_value');

    if (this.attachmentTypeName != 'GENERAL')
      this.getApproversBasedOnType(this.attachmentTypeName);


    document.documentElement.style.setProperty('--createButton', this.color);
    const length = _.where(this.formConfig, {
      type: 'attachments',
      field_name: 'file_name',
      is_active: true,
    });
    this.name_length = length.length > 0 ? length[0].maxLength : 100;
    if (this.formConfig) {
      const retrieveStyles = _.where(this.formConfig, {
        type: 'project-theme',
        field_name: 'styles',
        is_active: true,
      });
      console.log(retrieveStyles);
      if (retrieveStyles.length > 0) {
        let button = retrieveStyles[0].data.stepper_theme
          ? retrieveStyles[0].data.stepper_theme
          : '#90ee90';

        document.documentElement.style.setProperty('--button', button);
        this.fontStyle =
          retrieveStyles.length > 0
            ? retrieveStyles[0].data.font_style
              ? retrieveStyles[0].data.font_style
              : 'Plus Jakarta Sans'
            : 'Plus Jakarta Sans';
        document.documentElement.style.setProperty(
          '--attachFont',
          this.fontStyle
        );
      }
    }

    this.uploader = new FileUploader({
      url: this.URL,
      authToken: 'Bearer ' + this.loginService.getToken(),
      disableMultipart: false,
      headers: [
        {
          name: 'context-id',
          value: this.contextId,
        },
        {
          name: 'routing-key',
          value: this.routingKey,
        },
        {
          name: 'bucket-name',
          value: this.destinationBucket,
        },
      ],
      maxFileSize: 1024 * 1024 * 10, //10mb
    });

    this.detectUploadChanges();
  }

  openFileInput() {
    this.fileInput.nativeElement.click();
  }

  onCloseClick() {
    console.log('closeCalled');

    if (this.filesUploaded.length > 0) {
      console.log('S');

      this.utilityService.openConfirmationSweetAlertWithCustom(
        'You have changes',
        'Are you sure you want to discard these changes?'
      ).then((confirm) => {
        if (confirm && !this.data.existingDocument) {
          this.deleteAllFiles().then(() => {
            this.dialogRef.close({
              messType: 'E',
              refreshLanding: this.filesCreatedAndUploaded,
            });
          }).catch((err) => {
            console.error('Error during file deletion:', err);
          });
        } else if (confirm && this.data.existingDocument)
          this.dialogRef.close({
            messType: 'E',
            refreshLanding: false,
          });
      });
    } else {
      this.dialogRef.close({ messType: 'E', refreshLanding: this.filesCreatedAndUploaded });
    }
  }

  async deleteAllFiles(): Promise<void> {
    const deletionPromises = this.filesUploaded.map((file, index) =>
      this.sharedService.deleteObj(file, 't_app_attachments_meta').toPromise()
        .then(() => {
          console.log(`File ${index + 1} deleted successfully`);
        })
        .catch((err) => {
          console.error(`Error deleting file ${index + 1}:`, err);
        })
    );

    await Promise.all(deletionPromises);
  }

  getDocumentHeading(attachmentTypeName: string, existingDocument: boolean): string {
    const baseText = existingDocument ? 'Edit Document' : 'Add Document';

    if (!existingDocument && attachmentTypeName !== 'GENERAL') {
      const documentType = this.fileTypeDropDown.find(
        (type) => type.document_type === attachmentTypeName
      )?.document_label;

      return `${baseText} - ${documentType || ''}`;
    }

    return baseText;
  }


  createFiles(approval?) {
    console.log(this.filesUploaded);
    if (this.filesUploaded.length > 0) {
      if (this.fileTypeDropDown.length > 0) {
        this.filesUploaded.forEach(file => {
          let type = this.fileTypeDropDown.find((type) => type.document_type === this.attachmentTypeName)
          file.fileType = this.attachmentTypeName !== 'GENERAL' ? type?.id : this.selectedTypeId;
        });
        for (let item of this.filesUploaded) {
          if (!item.fileType || item.fileType == null) {
            this.toasterService.showWarning(
              'Kindly select the file type to proceed',
              ''
            );
            return;
          }
        }
      }

      // if (this.reviewersList.length == 0)
      //   return this.toasterService.showWarning('Cannot save a Financial Document without Approvers!', '', this.documentService.longInterval)

      // Map and update this.filesUploaded with document_type and document_type_id
      this.filesUploaded = this.filesUploaded.map((file) => {
        const selectedType = this.fileTypeDropDown.find(
          (type) => type.id === file.fileType
        );

        if (selectedType) {
          file.document_type = selectedType.document_type;
          file.document_type_id = selectedType.id;
          file.document_type_label = selectedType.document_label;
        }

        return file; // Return the updated file object
      });


      this.filesUploaded.forEach(file => {
        file.attachementStatusId = 8;
      });

      // Close the dialog and pass the updated filesUploaded array
      this.dialogRef.close({
        messType: 'S',
        description: this.description,
        mode: this.mode,
        filesUploaded: this.filesUploaded, // Use the updated array
        refreshLanding: true,
        reviewersList: this?.reviewersList,
        isApprovalRequired: approval ? true : false,
        appReferenceID: Number(this.data.applicationReferenceId),
        appID: this.data?.appId
      });
      this.filesCreatedAndUploaded = true;
    } else {
      this.toasterService.showWarning('Please add files to proceed!', '');
    }
  }

  sendForApproval() {
    if (this.reviewersList.length === 0) {
      return this.toasterService.showWarning('Cannot send for Approval!', 'No Approvers Found', this.documentService.longInterval);
    }
    this.createFiles('approval');
  }

  // onFileSelect(event: any) {
  //   const selectedFiles = event.addedFiles;
  //   if (selectedFiles.length > 0) {
  //     const file = selectedFiles[0]; // Get the first selected file
  //     console.log('Selected file:', file);
  //     // You can handle the selected file here, e.g., upload it or process it.
  //   }
  // }

  public fileOverBase(e: any): void {
    this.hasBaseDropZoneOver = e;
  }

  drop(event: CdkDragDrop<string[]>) {
    console.log('Drop Function Called');
    console.log('Event', event);
  }

  detectUploadChanges() {
    this.isUploading = true;
  
    this.uploader.onProgressItem = (progress: any) => {
      this.isUploading = true;
      console.log('Uploading...');
    };
  
    this.uploader.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
      console.log(item, response);
      this.uploader.removeFromQueue(item);
  
      let meta_data = JSON.parse(response).data;
      meta_data.isUploaded = true;
      
      let index = this.filesUploaded.findIndex(x => x.file_name === meta_data.file_name);
      this.filesUploaded[index] = meta_data;
  
      this.isUploading = this.uploader.queue.some(file => !file.isUploaded);
    };
  
    this.uploader.onAfterAddingFile = (item) => {
      if (this.isEditingAllowed) {
        let originalFileName = item.file.name;
        let fileName = this.containsSpecialChars(originalFileName) 
          ? originalFileName.replace(/[^a-zA-Z0-9. ]/g, '_') 
          : originalFileName;
    
        let fileExtension = fileName.includes('.') ? fileName.split('.').pop() : '';
        let baseName = fileExtension ? fileName.replace(`.${fileExtension}`, '') : fileName;
    
        let count = 1;
        let newFileName = fileName;
    
        while (this.filesUploaded.some(f => f.file_name === newFileName)) {
          newFileName = `${baseName}_${count}.${fileExtension}`;
          count++;
        }
    
        let temp_file_data = {
          file_name: newFileName,
          file_format: fileExtension,
          cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
          isUploaded: false,
        };

        item.file.name = newFileName;
    
        this.filesUploaded.push(temp_file_data);
        this.uploader.uploadItem(item);
      }
    };
    
  
    this.uploader.onCompleteAll = () => {
      this.isUploading = false;
    };
  }
  

  containsSpecialChars = (str) => {
    const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/;
    return specialChars.test(str);
  };

  onFileAdd(event) {
    this.isFileAdded = true;
    console.log(this.uploader);
    this.selectedFile = this.uploader['queue'][0]._file;
  }

  uploadFiles() {
    this.uploader.uploadAll();
  }

  removeAttachment = () => {
    this.uploader.clearQueue();
    this.selectedFile = null;
    console.log(this.selectedFile);
  };

  selectType(selectedType) {
    console.log(selectedType);
    this.type_selected = selectedType;
  }

  viewFile(file) {
    let cdn_link = file.cdn_link
    if (!this.isDialogOpened) {
      this.isDialogOpened = true
      this.sharedService.getDownloadUrl(cdn_link).subscribe((res: any) => {
        if (this.data?.viewerOption?.openInNewTabMozillaViewer && file.file_format == 'pdf') {
          this.toasterService.showInfo('Opening in New tab!', '', 2500)
          window.open(res.mozUrl, '_blank')
          this.isDialogOpened = false
          return
        }

        else if (this.data?.viewerOption?.openInNewTabGoogleViewer && file.file_format == 'pdf') {
          this.toasterService.showInfo('Opening in New tab!', '', 2500)
          window.open(res.googleOpenInNewTab, '_blank')
          this.isDialogOpened = false
          return
        }

        else if ((this.data?.viewerOption?.openInNewTabMozillaViewer && this.documentService.isImageFormat(file.file_format)) || this.data?.viewerOption?.showInGoogleViewer || this.data?.viewerOption?.showInGoogleViewer || file.file_format != 'pdf') {
          const dialogRef = this.dialog.open(DocumentViewerComponent, {
            width: '100%',
            height: '100%',
            data: {
              selectedFileUrl: res.data,
              fileFormat: file.file_format,
              expHeaderId: '',
            },
          })

          dialogRef.afterClosed().subscribe((res: any) => {
            this.isDialogOpened = false
          });
        }

        else {
          this.toasterService.showWarning('Cannot show preview for the uploaded file type', '', 2000)
          this.isDialogOpened = false
        }
      })
    }
  }

  removeFile(file, index) {
    this.utilityService
      .openConfirmationSweetAlertWithCustom(
        'Are you sure',
        'You want to remove this file!'
      )
      .then((result) => {
        if (result) {
          this.sharedService
            .deleteObj(file, 't_app_attachments_meta')
            .subscribe(
              (res: any) => {
                this.filesUploaded.splice(index, 1);
              },
              (err) => {
                console.error(err);
              }
            );
        }
      });
  }

  validateTabAccess() {
    return (
      this.advanceTabAccess || this.descriptionTabAccess || this.optionTabAccess
    );
  }

  valueChange = (value) => {
    if (value) {
      this.description = value;
    }
  };

  editFiles() {
    let checkLength = this.file_name.trim();

    if (checkLength.length == 0) {
      console.log(this.file_name.length == 0, this.validError);
      this.validError = true;
      return;
    }

    if (
      this.fileTypeDropDown.length > 0 &&
      (this.formGroup.get('attachmentType').value == '' ||
        this.formGroup.get('attachmentType').value == null)
    ) {
      this.toasterService.showWarning(
        'Kindly select the file type to proceed',
        ''
      );
      return;
    }

    this.dialogRef.close({
      messType: 'S',
      description: this.description,
      mode: this.mode,
      file_name: this.file_name,
      filesUploaded: this.filesUploaded,
      documentType: this.formGroup.get('attachmentType').value,
      documentTypeName: this.formGroup.get('attachmentTypeName').value,
      refreshLanding: true,
    });
  }

  onFileTypeSelected(selectedValue: any, file: any) {
    file.fileType = selectedValue.id;
  }

  onAllFileTypeSelected(selectedValue: any) {
    this.selectedTypeId = selectedValue.id
    this.filesUploaded.forEach(file => {
      file.fileType = selectedValue.id;
    });
  }

  onFileTypeSelectedOnEdit(selectedValue: any) {
    this.formGroup.get('attachmentType').patchValue(selectedValue.id);
    this.formGroup.get('attachmentTypeName').patchValue(selectedValue.name);
  }

  getSelectedFileType(file: any): any {
    if (this.attachmentTypeName === 'po_value') {
      const poValueType = this.fileTypeDropDown.find(
        (type) => type.document_type === 'po_value'
      );
      if (poValueType) {
        file.fileType = poValueType.id;
        file.document_type = poValueType.document_type;
        file.document_type_id = poValueType.id;
        return poValueType;
      }
    }

    // Otherwise, return the existing fileType if available
    return this.fileTypeDropDown.find((type) => type.id === file.fileType);
  }

  getApproversBasedOnType(document_type) {
    if (!this.data.approversEnabled) return;
    this.miniLoader = true;
    let details = {
      document_type: document_type,
      app_id: this.data?.appId,
      app_reference_id: Number(this.data.applicationReferenceId)
    };

    this.documentService.getApproversBasedOnType(details).subscribe(
      (res: any) => {
        console.log('Approvers:', res);
        this.reviewersList = res?.data;
        if (this.reviewersList.length == 0)
          this.toasterService.showWarning('No Approvers Found for the selected type!', '')
        this.miniLoader = false;
      },
      (error) => {
        console.error('Error fetching approvers:', error);
        this.miniLoader = false;
      }
    );
  }

  get tooltipText() {
    if (this.attachmentTypeName !== 'po_value') {
      return !this.formGroup.get('attachmentType').value ?
        'Kindly select file type to get Approvers!' :
        'No Approvers';
    }
    return '-';
  }

}
