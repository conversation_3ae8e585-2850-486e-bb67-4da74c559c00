import { Component, OnInit, Inject } from '@angular/core';
import { SubSink } from 'subsink';
import * as _ from 'underscore';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { pluck } from 'rxjs/operators';
import { DocViewerComponent } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/components/doc-viewer/doc-viewer.component';
import sweetAlert from 'sweetalert2';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import {
  FormGroup,
  FormControl,
  FormArray,
  Validators,
  FormBuilder,
} from '@angular/forms';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { truncate } from 'fs';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { number } from '@amcharts/amcharts4/core';
import { TmService } from 'src/app/modules/shared-lazy-loaded-components/team-members/services/tm.service';
import moment from 'moment';
import { PmInternalStakeholderService } from '../../../../../../../project-management/features/pm-details/features/pm-project-team/features/project-internal-stakeholders/services/pm-internal-stakeholder.service';
import { Router } from '@angular/router';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';

export interface projectList {
  id: number,
  name: string,
  requestId: number
}

@Component({
  selector: 'app-confirm-alloaction-form',
  templateUrl: './confirm-alloaction-form.component.html',
  styleUrls: ['./confirm-alloaction-form.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MMM-YYYY',
        },
        display: {
          dateInput: 'DD-MMM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
        useUtc: true,
      },
    }
  ],
})
export class ConfirmAlloactionFormComponent implements OnInit {
  associate_id: number;
  request_id: number;
  contextId:any;
  detailData: any = null;
  selectedVal: number;
  bookingTypeValue: string;
  isFormSubmitted: boolean = false;
  fieldConfig = {};
  allocated_hours: number = 0;
  locationSpecific: boolean = false;
  reportsToMasterList: any = [];
  doj: any;
  e360_end_date: any;
  constructor(
    private _rmService: RmService,
    private _toaster: ToasterService,
    public dialogRef: MatDialogRef<ConfirmAlloactionFormComponent>,
    private _tmService: TmService,
    private _router: Router,
    public dialog: MatDialog,
    private PmInternalStakeholderService: PmInternalStakeholderService,
    @Inject(MAT_DIALOG_DATA) public inData: any
  ) { }

  subs = new SubSink();
  sample = [
    { id: 1, name: 'Hard Booking' },
    { id: 2, name: 'Soft Booking' },
  ]
  BookingTypeMaster = [
    { id: 1, booking_type: 'Hard Booking' },
    { id: 2, booking_type: 'Soft Booking' },
  ];
  // criteria_arr :[ {
  //   "criteria_id":number,
  //   "is_satisfied":boolean
  // }] 
  criteria_arr = [];
  isSoftBookingEnabled: boolean = false;
  isComponentLoading: boolean = true;
  isResumeButtonEnabled:boolean=false;
  filteredList: Array<projectList> = [];
  isFormVisible: boolean = false;
  workflowId: number = null;
  placeHolder:string = 'Project';
  projectName: string = null;
  requestProjectDetails: Object = null;
  workCityMasterList: any = []
  workPremisisMasterList: any = []
  travelTypeList: any = []
  travelConfig: any = {};
  shiftConfig: any = {};
  shiftMasterList: any = [];

  async ngOnInit() {
    this.isComponentLoading = true;
    this.rmConfig = await this.retrieveRmConfig()
    this.reportOptionEnable = this.rmConfig['reporting_enable'] ? this.rmConfig['reporting_enable'] : false
    this.isResumeButtonEnabled=this.rmConfig['resume_enable'] ? this.rmConfig['resume_enable'] : false
    this.travelConfig = (this.rmConfig && this.rmConfig['travel_type_config']) ? this.rmConfig['travel_type_config'] : null;
    this.shiftConfig = (this.rmConfig && this.rmConfig['shift_config']) ? this.rmConfig['shift_config'] : null;
    this.workPremisisMasterList =  await this.getWorkPremsisList();
    if(this.travelConfig && this.travelConfig['is_active']){
      this.travelTypeList = await this.getTravelTypeList();
    }
    if(this.shiftConfig && this.shiftConfig['is_active']){
      this.shiftMasterList = await this.getShiftMasterList();
    }
    this.isSoftBookingEnabled = this.inData.modalParams.softBooking ? this.inData.modalParams.softBooking : false;
    if (!this.isSoftBookingEnabled) {
      this.associate_id = this.inData.modalParams.associate_id;
      this.request_id = this.inData.modalParams.request_id;
      await this.getEmployeeDetails(this.associate_id);
      await this.handleConfirmAllocationForm();
    }
    else {
      await this.handleSearchFieldChanges()
      await this.handleSoftBooking();
    }
    this.requestProjectDetails = await this.getRequestProjectDetails();
    this.isComponentLoading = false;
  }
  confirmAllocationForm = new FormGroup({
    start_date: new FormControl('', Validators.required),
    end_date: new FormControl('', Validators.required),
    booking_type: new FormControl(''),
    utilization_capacity: new FormControl(''),
    notes: new FormControl(''),
    schedule_interview: new FormControl(''),
    criteria: new FormControl('', Validators.required),
    searchField: new FormControl(''),
    work_city: new FormControl(''),
    work_premisis: new FormControl(''),
    reports_to: new FormControl(''),
    is_head: new FormControl(''),
    travel_type: new FormControl(''),
    shift: new FormControl('')
  });
  rmConfig : any = null;
  reportOptionEnable: boolean = false;
  
  is_head: boolean = true;
  reports_to_list: any = [];
  projectHeadName: string;

  toggle: boolean;

  async onSubmit() {
    this.isFormSubmitted = true;
    if(!this.checkMandate(this.confirmAllocationForm.value)){
      this.isFormSubmitted = false;
      return;
    }
    if(this.reportOptionEnable && this.isSoftBookingEnabled){
      if(this.confirmAllocationForm.value.is_head == 0){
        if((this.confirmAllocationForm.value.reports_to == null || this.confirmAllocationForm.value.reports_to == '' || this.confirmAllocationForm.value.reports_to == undefined)){
          this._toaster.showWarning('Warning', 'Reporting is mandatory for this Action.');
          this.isFormSubmitted = false;
          return;
        }
      }
    }
    if(this.travelConfig && this.travelConfig['is_mandatory']  && this.isSoftBookingEnabled){
      if((this.confirmAllocationForm.value.travel_type == null || this.confirmAllocationForm.value.travel_type == '' || this.confirmAllocationForm.value.travel_type == undefined)){
        this._toaster.showWarning('Warning', `${(this.travelConfig['field_label'] ? this.travelConfig['field_label'] : 'Travel Type')} is Mandatory`);
        this.isFormSubmitted = false;
        return;
      }
    }

    if(this.shiftConfig && this.shiftConfig['is_mandatory']  && this.isSoftBookingEnabled){
      if((this.confirmAllocationForm.value.shift == null || this.confirmAllocationForm.value.shift == '' || this.confirmAllocationForm.value.shift == undefined)){
        this._toaster.showWarning('Warning', `${(this.shiftConfig['field_label'] ? this.shiftConfig['field_label'] : 'Shift')} is Mandatory`);
        this.isFormSubmitted = false;
        return;
      }
    }

    if (!this.requestProjectDetails) {
      this._toaster.showWarning('Warning', 'Project Details not found');
      this.isFormSubmitted = false;
      return;
    }


    // Safely accessing properties using optional chaining (?.)
    const billable = this.requestProjectDetails['commercial'] ? true : false;
    const qtc_project = this.requestProjectDetails['with_opportunity'] ? true : false;
    const blanket_po = this.requestProjectDetails['blanket_po'] ? true : false;
    const internal_project = this.requestProjectDetails['is_internal'] ? true : false;
    let booking_type = this.confirmAllocationForm.get('booking_type').value;
    
    const allow_check = billable && qtc_project && !blanket_po && !internal_project;

    //Planned Allocated Hours for Non Quote Position Check
    const api_response = await this.getAllocatedHours(this.confirmAllocationForm.value);
    let total_allocated_hours =  api_response?.['total_allocated_hours'] ? api_response?.['total_allocated_hours'] : 0
    this.allocated_hours = await this.calculatePlannedHours(total_allocated_hours,this.confirmAllocationForm.value.utilization_capacity)
    console.log('Planned Allocated Hours:',this.allocated_hours)

    // Stage 1: Day-Wise Allocation Check
    const day_wise_check = this.rmConfig['is_day_wise_availability_allowed'] || false;
    
    if (day_wise_check && booking_type == 1) {
      const allow_allocation = await this.dayWiseAllocationCheck(this.confirmAllocationForm.value);
      
      if (!allow_allocation) {
        if(this.isSoftBookingEnabled)
          await this.cancelRequest('Employee is unavailable for allocation')
        else
          this._toaster.showWarning('Warning', 'Employee is unavailable for allocation during the selected duration.');
        this.isFormSubmitted = false;
        return;
      }
    }

    // Stage 2: Checking project types & Billable 
    if (!allow_check) {
      await this.processRequest();
      this.isFormSubmitted = false;
      return;
    }

    // Stage 3: Proceed with Quote Position Check
    const disable_position_check = this.rmConfig['disable_position_check'] || false;

    if (!disable_position_check && booking_type == 1) {
      await this.handleAllocationChecks();
    } else {
      await this.processRequest();
    }

    this.isFormSubmitted = false;
  }
  // Helper to check allocation and handle remaining balance
  async handleAllocationChecks() {
    if(!(this.allocated_hours > 0)){
      this._toaster.showWarning('Warning', 'Planned Working Hours are 0 for the selected duration. Please select a different date range.');
      this.isFormSubmitted = false;
      return;
    }
    const data = await this.remainingBalanceCheck(this.confirmAllocationForm.value);
    const allowAllocationCheck = data?.['allow_allocation'] || false;
    if (allowAllocationCheck) {
      await this.processRequest();
    } else {
      if(!this.isSoftBookingEnabled)
        this._toaster.showWarning('Warning', 'Quote Position Balance is not available to allocate');
      else
        await this.cancelRequest('Quote Position Balance is not available to allocate')
      this.isFormSubmitted = false;
    }
  }

  // Helper for soft booking or initiating request
  async processRequest() {
    if (this.isSoftBookingEnabled) {
      await this.initiateSoftBooking(this.confirmAllocationForm.value);
    } else {
      await this.initiateRequest(this.confirmAllocationForm.value);
    }
  }

  initiateRequest(val: any) {
    this.isFormSubmitted = true;
    console.log(val);
    
    let params = {
      request_id: this.request_id,
      resource_aid: this.associate_id,
      notes: val.notes,
      criteria_arr: val.criteria,
      booking_type: val.booking_type,
      end_date: moment(val.end_date).format('YYYY-MM-DD'),
      schedule_interview: val.schedule_interview,
      start_date: moment(val.start_date).format('YYYY-MM-DD'),
      utilization_capacity: val.utilization_capacity,
      work_premisis: val.work_premisis,
      work_city: val.work_city
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.initiateResourceAllocation(params).subscribe(
        (res: any) => {
          if (!res.err) {

            sweetAlert.fire(
              `Associate ID ${params.resource_aid}`,
              'Allocated Successfully !',
              'success'
            );

            this.closeDialog({
              reqStatusId: res['reqStatusId'],
              reqStatusName: res['reqStatusName'],
              reqStatusColor: res['reqStatusColor']
            })
            resolve(res.data);
          }
          else {
            if (res['allocationNotAllowed']) {
              this.closeDialog({
                allocationNotAllowed: true,
                reqStatusId: res['reqStatusId'],
                reqStatusName: res['reqStatusName'],
                reqStatusColor: res['reqStatusColor']
              })
              this._toaster.showWarning("Request status changed !", res['msg'])
              resolve(res);
            }
            else
              this._toaster.showError("Failed", "Failed to allocate request", 3000)
          }
          this.isFormSubmitted = false;
        },
        (err) => {
          this.isFormSubmitted = false;
          if (err.error.user_msg)
            this._toaster.showWarning("Error", err.error.msg)
          console.log(err);
          reject(err);
        }
      );
    });
  }

  clickarrBookingType = [];
  BookingTypeToggle(value: any) {
    console.log(value)
    this.confirmAllocationForm
      .get('booking_type')
      .patchValue(parseInt(value));
    _.find(this.BookingTypeMaster, x => {
      if (x.id === value) {
        this.bookingTypeValue = x.booking_type
      }
    })
    console.log(this.bookingTypeValue)
    // if (this.clickarrBookingType.length == 0) {
    //   this.clickarrBookingType.push(value);
    //   console.log(this.clickarrBookingType);
    // } else {
    //   this.clickarrBookingType = [];
    //   this.clickarrBookingType.push(value);
    //   console.log(this.clickarrBookingType);
    // }

    //this.contactForm.value.booking_type = parseInt(value.id);
    console.log(this.confirmAllocationForm.value, parseInt(value));
  }

  toggleChanges($event: MatSlideToggleChange) {
    console.log($event.checked);
    this.toggle = $event.checked;
    if (this.toggle) {
      this.confirmAllocationForm.value.schedule_interview = 1;
    } else {
      this.confirmAllocationForm.value.schedule_interview = 0;
    }
    console.log(
      this.confirmAllocationForm.value,
      this.confirmAllocationForm.status,
      this.confirmAllocationForm.valid
    );
  }
  onButtonClick(associateId) {
    this.bindSavedValues(associateId)
      .then(() => {
        // Call retrieveUploadedObjects once the contextId is retrieved
        this.retrieveUploadedObjects(this.contextId);
      })
      .catch(err => {
        console.error('Error fetching saved values:', err);
      });
  }

  bindSavedValues(associateId): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this._rmService.getHrDocuments(associateId).subscribe(
        (res: any) => {
          if (!res.err) {
            if (!res.is_empty) {
              this.contextId = res.data.resume;
            }
            resolve(true);
          } else {
            reject(new Error('No data returned.'));
          }
        },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  }

  retrieveUploadedObjects(contextId) {
    if(contextId){
    this._rmService.retrieveUploadedObjects("kebs-e360", contextId)
      .subscribe(
        (res: any) => {
          console.log(res['data'])
          const data = res['data'];
          if (data && data.length) {
            // View the first file for demonstration
            console.log("view file")
            this.viewFile(data[0]);
          }
        },
        (err) => {
          console.error('Error retrieving uploaded objects:', err);
        }
      );}
      else{
        this._toaster.showWarning('Warning', 'No Resume Found')
      }
  }

  viewFile(file) {
    const cdn_link = file.cdn_link;
    this._rmService.getDownloadUrl(cdn_link).subscribe((res: any) => {
      this.dialog.open(DocViewerComponent, {
        width: '100%',
        height: '90%',
        data: {
          selectedFileUrl: res.data,
          fileFormat: file.file_format,
        },
      });
    });
  }


  getDetail = () => {
    let params = {
      associate_id: this.associate_id,
      request_id: this.request_id
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService
        .getConfirmAllocationDetail(params)
        .subscribe(
          (res: any) => {
            if (res.err == false) resolve(res.data);
            else reject(res);
          },
          (err) => {
            if (err.error.user_msg)
              this._toaster.showWarning('Error', err.error.msg)
            console.log(err);
            reject(err);
          }
        );
    });
  }
  handleFormPrePatching(startDate, endDate, utilization, bookingType, scheduleInterview,work_city,work_premisis) {
    let patch_start_date = this.compareDateMinimum(startDate,this.doj)
    let patch_end_date = this.compareDate(endDate,this.e360_end_date)
    this.confirmAllocationForm.get('start_date').patchValue(patch_start_date);
    this.confirmAllocationForm.get('end_date').patchValue(patch_end_date);
    this.confirmAllocationForm.get('utilization_capacity').patchValue(utilization);
    this.confirmAllocationForm.get('booking_type').patchValue(bookingType);
    this.confirmAllocationForm.get('schedule_interview').patchValue(scheduleInterview);
    if (this.confirmAllocationForm.get('schedule_interview').value === 1) {
      // console.log("True Toggle:", this.confirmAllocationForm.get('schedule_interview'));
      this.toggle = true;
    }
    else {
      // console.log("False Toggle:", this.confirmAllocationForm.get('schedule_interview'));
      this.toggle = false;
    }
    this.confirmAllocationForm.get('work_city').patchValue(work_city);
    this.confirmAllocationForm.get('work_premisis').patchValue(work_premisis);
  }

  closeDialog(data) {
    this.dialogRef.close(data)
  }

  onCheckChange(ob: MatCheckboxChange, data) {
    console.log("PQR checked: " + ob.checked);
    console.log(data)
    _.find(this.criteria_arr, x => {
      if (x.criteria_id === data.criteria_id) {
        x.is_satisfied = ob.checked;
      }
    });
    console.log(this.criteria_arr)
    this.confirmAllocationForm.get('criteria').patchValue(this.criteria_arr);
  }

  getCriteria = () => {
    console.log("Length:", this.detailData.criteria.length)
    for (let i = 0; i < this.detailData.criteria.length; i++) {
      console.log(this.detailData.criteria[i]);
      const newData = {
        criteria_id: this.detailData.criteria[i].criteria_id,
        is_satisfied: false
      }
      this.criteria_arr = this.criteria_arr.concat(newData)
    }
    console.log(this.criteria_arr);
    this.confirmAllocationForm.get('criteria').patchValue(this.criteria_arr);
  }

  get getstartdate() {
    return this.confirmAllocationForm.get('start_date').value;
  }
  get getenddate() {
    return this.confirmAllocationForm.get('end_date').value;
  }
  retrieveFieldConfig(view_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.retrieveFieldConfig(view_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }


  async handleFieldConfig() {
    
    let fieldList: any = await this.retrieveFieldConfig('create_request')
    
    if (fieldList.length > 0) {
      fieldList.forEach((val, i) => {
        this.fieldConfig[val.field_key] = val

        let formField = this.confirmAllocationForm.get(val.field_key)

        if (formField && val.is_active_field) {


          if (val.is_mandatory) {
            // if(val.field_type == 'array'){
            //   for(let i=0;i<val.array_items.length;i++){
            //     if(val.array_items[i].is_mandatory){
            //       const itemControls = <FormArray>this.confirmAllocationForm.controls[val.field_key];
            //       console.log(itemControls)
            //       const itemFormGroup = <FormGroup>itemControls.controls[0];
            //       itemFormGroup.controls[val.array_items[i].field_key].setValidators([Validators.required])
            //     }
            //   }
            // }
            // else {
            formField.setValidators([Validators.required])
            // }
          }
          else {
            formField.clearValidators()
          }

          if (val.has_default_value) {
            if (val.field_type == 'toggle-button') {
              switch (val.field_key) {
                case 'booking_type': {
                  this.BookingTypeToggle(val.field_default_value)
                  break
                }
              }
            }
            else if (val.field_type == 'toggle') {
              this.confirmAllocationForm.get('need_client_interview').setValue(val.field_default_value);
              if (val.field_default_value == 1) {
                this.toggle = true
              }
              else {
                this.toggle = false
              }
              console.log("toggle default value", typeof (val.field_default_value), this.confirmAllocationForm.value)
            }
            else {
              formField.patchValue(val.field_default_value)
            }
          }
        }
      })
    }
    
    if((this.fieldConfig['work_premisis'] && this.fieldConfig['work_premisis']['is_allocate_active']) || (this.fieldConfig['work_city'] && this.fieldConfig['work_city']['is_allocate_active'])){
      this.locationSpecific = true;
    }
    else{
      this.locationSpecific = false;
    }
  }
  preventDefaultCapacity(e: any) {
    console.log(this.confirmAllocationForm.value.utilization_capacity)
    let val = this.confirmAllocationForm.value.utilization_capacity;
    if (val > 100 && e != null) {
      this.confirmAllocationForm.get('utilization_capacity').patchValue(100);
      this._toaster.showWarning(
        'Default',
        'Utilization Capacity Must Not be greater than 100'
      );
    }
    else if (val === 0 && e != null) {
      this.confirmAllocationForm.get('utilization_capacity').patchValue(null);
      this._toaster.showWarning(
        'Default',
        'Utilization Capacity Must Not be Less than 1'
      );
    }
  }

  async handleConfirmAllocationForm() {
    this.isFormVisible = true;
    await this.handleFieldConfig();
    if(this.fieldConfig['work_city'] && this.fieldConfig['work_city']['is_allocate_active']){
      this.workCityMasterList =  await this.getWorkCityList();
    }
    this.detailData = await this.getDetail();
    this.handleFormPrePatching(this.detailData.request_start_date, this.detailData.request_end_date, this.detailData.utilization_capacity, this.detailData.booking_type, this.detailData.need_client_interview,null,null);
    this.getCriteria();
    _.find(this.BookingTypeMaster, x => {
      if (x.id === this.detailData.booking_type) {
        this.bookingTypeValue = x.booking_type
      }
    })
  }

  async handleSoftBooking() {

    let projectDetails = this.inData.modalParams.resourceDetails;


    if (projectDetails.has_multiple_project) {
      this.placeHolder = this.inData.modalParams.placeHolder
      await this.searchFilteredData(projectDetails.project_list);
    }
    else {
      let projectInfo = projectDetails.project_list[0];
      this.associate_id = projectInfo.resource_aid;
      this.request_id = projectInfo.request_id;
      this.workflowId = projectInfo.workflow_id;
      this.projectName = projectInfo.project_name;
      // console.log('Project Name: ',this.projectName);
      if(this.reportOptionEnable){
        await this.patchReportingDetails(projectInfo.project_id)
      }
      await this.handleConfirmAllocationForm();
      await this.handleFormPrePatching(projectInfo.project_start_date, projectInfo.project_end_date, projectInfo.utilization_capacity, projectInfo.booking_type, projectInfo.need_client_interview,projectInfo.work_city,projectInfo.work_premisis)
      _.find(this.BookingTypeMaster, x => {
        if (x.id === projectInfo.booking_type) {
          this.bookingTypeValue = x.booking_type
        }
      })
    }
  }

  searchFilteredData(data) {
    this.filteredList = [];

    for (let i = 0; i < data.length; i++) {
      let formattedDate = `${moment(data[i]['project_start_date']).format('DD-MMM-YYYY')} - ${moment(data[i]['project_end_date']).format('DD-MMM-YYYY')}`;

      let item = {
        'id': data[i]['request_id'],
        'name': `${data[i]['project_name']} (${formattedDate})`,
        'requestId': data[i]['request_id']
      };
      this.filteredList.push(item);

    }

  }

  handleSearchFieldChanges() {
    this.confirmAllocationForm.get('searchField').valueChanges.subscribe(async () => {
      await this.patchFormDetails()
    })
  }

  async patchFormDetails() {
    let projectList = this.inData.modalParams.resourceDetails.project_list;
    let requestId = this.confirmAllocationForm.get('searchField').value;
    let projectInfo
    _.find(projectList, item => {
      if (item.request_id === requestId) {
        projectInfo = item
      }
    })
    console.log('Project Info:', projectInfo);
    this.isComponentLoading = true;
    this.associate_id = projectInfo.resource_aid;
    this.request_id = projectInfo.request_id;
    this.workflowId = projectInfo.workflow_id;
    this.projectName = projectInfo.project_name;
    this.requestProjectDetails = await this.getRequestProjectDetails();
    if(this.reportOptionEnable){
      await this.patchReportingDetails(projectInfo.project_id)
    }
    await this.getEmployeeDetails(this.associate_id);
    // console.log('Project Name: ',this.projectName);
    await this.handleConfirmAllocationForm();
    await this.handleFormPrePatching(projectInfo.project_start_date, projectInfo.project_end_date, projectInfo.utilization_capacity, projectInfo.booking_type, projectInfo.need_client_interview,projectInfo.work_city,projectInfo.work_premisis)
    _.find(this.BookingTypeMaster, x => {
      if (x.id === projectInfo.booking_type) {
        this.bookingTypeValue = x.booking_type
      }
    })
    this.isComponentLoading = false;
  }

  initiateSoftBooking(val: any) {
    this.isFormSubmitted = true;
    if (val.booking_type == 1) {
      
      let params = {
        request_id: this.request_id,
        resource_aid: this.associate_id,
        booking_type: val.booking_type,
        end_date: moment(val.end_date).format('YYYY-MM-DD'),
        start_date: moment(val.start_date).format('YYYY-MM-DD'),
        utilization_capacity: val.utilization_capacity,
        workflow_id: this.workflowId,
        allocated_hours: this.allocated_hours,
        work_premisis: val.work_premisis,
        work_city: val.work_city,
        is_head: val.is_head ? val.is_head : 0,
        reports_to: val.reports_to ? val.reports_to : null,
        travel_type: val.travel_type ? val.travel_type : null,
        shift: val.shift ? val.shift : null
      }
      return new Promise((resolve, reject) => {
        this.subs.sink = this._rmService.initiateSoftBooking(params).subscribe(
          (res: any) => {
            if (!res.err) {

              sweetAlert.fire(
                `Associate ID ${params.resource_aid}`,
                'Assigned Successfully !',
                'success'
              );

              this.closeDialog({
                softBookedStatus: 'S'
              })
              resolve(res.data);
            }
            else {
              if (res['isEmpRetired'])
                this._toaster.showWarning('Not allowed', res['msg'])
              else if (res['isa_member_restriction']) {
                this._toaster.showWarning('Assignment not Allowed', res['messText']);
              }
              else if (res['isa_member_restriction_error']) {
                this._toaster.showError("Error", res['messText'], 3000);
              }
              else
                this._toaster.showError("Error", "Something went wrong !", 3000)
            }
            resolve(res);
            this.isFormSubmitted = false;
          },
          (err) => {
            this.isFormSubmitted = false;
            this._toaster.showError("Failed","Something went wrong !",3000)
            console.log(err);
            reject(err);
          }
        );
      });
    }
    else {
      this._toaster.showWarning("Allocation Status !", 'No changes for Soft Booking')
      this.isFormSubmitted = false;
    }
  }
  
  getRequestProjectDetails(){
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getRequestProjectDetails(this.request_id).subscribe(
        (res: any) => {
          if (!res.err) {
            resolve(res.data);
          }
          else {
            this._toaster.showError("Failed","Error in Retreiving Project Details !",3000)
            resolve(null)
          }
        },
        (err) => {
          this._toaster.showError("Failed","Error in Retreiving Project Details !",3000)
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  getAllocatedHours(val:any){
    let item = {
      start_date : moment(val.start_date).format('YYYY-MM-DD'),
      end_date: moment(val.end_date).format('YYYY-MM-DD'),
      associate_id: this.associate_id,
      allocation_percentage : val.utilization_capacity,
      isa_id: null,
      itemId: this.requestProjectDetails['project_id'] ? this.requestProjectDetails['project_id'] : null
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getPlannedAllocatedHours(item).subscribe(
        (res: any) => {
          if (res && res['messType'] == 'S') {
            resolve(res);
          }
          else {
            this._toaster.showError("Failed","Error in  fetching Planned Allocated Hours !",3000)
            resolve(null)
          }
        },
        (err) => {
          this._toaster.showError("Failed","Error in fetching Planned Allocated Hours !",3000)
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  remainingBalanceCheck(val:any){
    let withOpportunity = this.requestProjectDetails['with_opportunity'] ? this.requestProjectDetails['with_opportunity'] : 0
    let item = {
      start_date : moment(val.start_date).format('YYYY-MM-DD'),
      end_date: moment(val.end_date).format('YYYY-MM-DD'),
      associate_id: this.associate_id,
      allocation_percentage : val.utilization_capacity,
      isa_id: null,
      itemId: this.requestProjectDetails['project_id'] ? this.requestProjectDetails['project_id'] : null,
      position: this.requestProjectDetails['project_role'] ? this.requestProjectDetails['project_role'] : null,
      position_type: withOpportunity ? 'QTC' : 'OTC',
      allocated_hours: this.allocated_hours,
      is_billable: this.requestProjectDetails['commercial'] ? this.requestProjectDetails['commercial'] : null
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getPlannedAllocatedAndRemainingBillableHours(item).subscribe(
        (res: any) => {
          if (res) {
            resolve(res);
          }
          else {
            this._toaster.showError("Failed","Error in  Checking Remainining Balance !",3000)
            resolve(null)
          }
        },
        (err) => {
          this._toaster.showError("Failed","Error in Checking Remainining Balance  !",3000)
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  dayWiseAllocationCheck(val:any){
    let item = {
      start_date : moment(val.start_date).format('YYYY-MM-DD'),
      end_date: moment(val.end_date).format('YYYY-MM-DD'),
      associate_id: this.associate_id,
      allocation_percentage : val.utilization_capacity,
      project_id: this.requestProjectDetails['portfolio_id'] ? this.requestProjectDetails['portfolio_id'] : null,
      item_id: this.requestProjectDetails['project_id'] ? this.requestProjectDetails['project_id'] : null
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.checkDayWiseAllocation(item).subscribe(
        (res: any) => {
          if (res && res['messType'] == 'S') {
            resolve(res['data'] ? res['data'] : false)
          }
          else {
            this._toaster.showError("Warning","Failed in  Checking Day Wise Availability !",3000)
            resolve(false)
          }
        },
        (err) => {
          this._toaster.showError("Failed", "Error in  Checking Day Wise Availability !",3000)
          console.log(err);
          resolve(false);
        }
      );
    });
  }

  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._rmService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err)
          resolve(res.data)
      }, (err) => {
        console.log(err)
      })
    })
  }

  cancelRequest(msg){

    sweetAlert.fire({
      title: 'Reject Request',
      text: msg,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, reject it!',
      cancelButtonText: "No, let me think",
    }).then(async (result) => {
      if(result.isConfirmed) {
        let params = {
          request_id: this.request_id,
          status_id: 5
        };
        return new Promise((resolve, reject) => {
          this.subs.sink = this._tmService.updateRequestStatus(params).subscribe(
            async (res: any) => {
              if (res.err == false) {
                if (!res.err) {
                  // sweetAlert.fire(`${res.msg}`, 'success');
                  if(res['has_access']) {
                    this._toaster.showSuccess(
                      'Success',
                      'Request Got Cancelled !',
                      2000
                    );
                    this.dialogRef.close();
                    this.closeDialog({
                      softBookedStatus: 'S'
                    })
                  }
                  else {
                    this._toaster.showWarning(
                      'Access denied',
                      'You do not have access to cancel the request.'
                    )
                    this.dialogRef.close();
                    this.closeDialog({
                      softBookedStatus: 'S'
                    })
                  }

                }
                resolve(res.data);
              } else {
                this.dialogRef.close();
                this._toaster.showWarning('Warning', 'Failed in Rejecting the Request!')
                this.closeDialog({
                  softBookedStatus: 'S'
                })
                resolve(null)
              }
            },
            (err) => {
              this._toaster.showError('Error', 'Failed in Rejecting the Request!','1000')
              this.closeDialog({
                softBookedStatus: 'S'
              })
              console.log(err);
              resolve(null);
            }
          );
        });
      }
    })
  }
  isBookingTypeDisabled(value:boolean){
    let status = (this.requestProjectDetails && this.requestProjectDetails['project_status']) ? this.requestProjectDetails['project_status'] : []
    let enable_booking = this.rmConfig['allow_booking_type_change'] && this.rmConfig['allow_booking_type_change'].includes(status)
    if(this.isSoftBookingEnabled){
      return false
    }
    else if(value){
      if(enable_booking){
        return false
      }
      return value
    }
    else{
      return false
    }
  }

  checkMandate(data){
    if(this.fieldConfig['request_start_date']['is_mandatory']){
      if(!this.confirmAllocationForm.get('start_date').valid){
        this._toaster.showWarning('Warning', `${this.fieldConfig['request_start_date']['field_label']} is Mandatory`);
        return false;
      }
    }
    if(this.fieldConfig['request_end_date']['is_mandatory']){
      if(!this.confirmAllocationForm.get('end_date').valid){
        this._toaster.showWarning('Warning', `${this.fieldConfig['request_end_date']['field_label']} is Mandatory`);
        return false;
      }
    }
    if(this.fieldConfig['utilization_capacity']['is_mandatory']){
      if(!this.confirmAllocationForm.get('utilization_capacity').valid){
        this._toaster.showWarning('Warning', `${this.fieldConfig['utilization_capacity']['field_label']} is Mandatory`);
        return false;
      }
    }
    if(this.fieldConfig['booking_type']['is_mandatory']){
      if(!this.confirmAllocationForm.get('booking_type').valid){
        this._toaster.showWarning('Warning', `${this.fieldConfig['booking_type']['field_label']} is Mandatory`);
        return false;
      }
    }
    if(this.fieldConfig['work_premisis']['is_mandatory'] && this.fieldConfig['work_premisis']['is_allocate_active']){
      if(data.work_premisis == null || data.work_premisis == undefined || data.work_premisis == ''){
        this._toaster.showWarning('Warning', `${this.fieldConfig['work_premisis']['field_label']} is Mandatory`);
        return false;
      }
    }
    if(this.fieldConfig['work_city']['is_mandatory'] && this.fieldConfig['work_city']['is_allocate_active']){
      if(data.work_city == null || data.work_city == undefined || data.work_city == ''){
        this._toaster.showWarning('Warning', `${this.fieldConfig['work_city']['field_label']} is Mandatory`);
        return false;
      }
    }
    return true
  }

  getWorkCityList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getWorkCityList().subscribe(
        (res: any) => {
          if(res.messType == 'S' && res.data && res.data.length > 0) {
            resolve(res.data)
         } 
         else{
           resolve([]);
         }
        },
        (err) => {
          console.log(err);
          resolve([]);
        }
      );
    });
  }

  getWorkPremsisList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getWorkPremisisList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getTravelTypeList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getTravelTypeList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  async patchReportingDetails(itemId){
    this.reportOptionEnable = this.rmConfig['reporting_enable'] ? this.rmConfig['reporting_enable'] : false

    if(this.reportOptionEnable){
      let currentDate = moment().format();
      await this.PmInternalStakeholderService.getreportsToMaster(
        itemId,
        currentDate
      ).then((res) => {
        
        this.reports_to_list = res['data'];
        this.reportsToMasterList = this.reports_to_list.map(item => ({
          id: item.id,
          name: item.name
        }));
      });
  
      await this.PmInternalStakeholderService.getHeadForISA(
        itemId
      ).then((res) => {
        
        if (res['data'].length > 0) {
          this.is_head = false;
          let data = res["data"][0]
          
          // const head_name = this.reports_to_list.find(option => option.associate_id === data['aid']);
          const head_name = data['head_name'] ? data['head_name'] : null;
          this.projectHeadName = head_name ? head_name : null;
        }
      });
    }
  }

  // Handle head checkbox change
  isHeadChange(isChecked: boolean) {  
    this.confirmAllocationForm.patchValue({ ['is_head']: isChecked });
    if (isChecked) {
      this.confirmAllocationForm.patchValue({ ['reports_to']: '' });
    }
  }

  /**
    * @description calculation Planned Hours
    * @param total_hours 
    * @param percentage 
    */
  calculatePlannedHours(total_hours:number,percentage:number){
    if(percentage && total_hours){
      let plannedValue = (percentage * total_hours) / 100;
      plannedValue = parseFloat(plannedValue.toFixed(2))
      
      return plannedValue;
    }
    else{
      return 0;
    } 
   }

   onCustomSelectChanges(event){
    // console.log(event,'Coty Evebt')
    this.confirmAllocationForm.get('work_city').patchValue(event.val ? event.val : null)
   }
   getEmployeeDetails(associate_id) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getEmployeeDetails(associate_id).subscribe(
        (res: any) => {
          if(!res.err){
            this.doj = res.date_of_joining ? res.date_of_joining : null;
            this.e360_end_date = res.end_date ? res.end_date : null;
          }
          resolve(res);
        },
        (err) => {
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  compareDate(date1: any, date2: any) {
    if (
      (date1 == '' || date1 == null || date1 == undefined) &&
      (date2 == '' || date2 == null || date2 == undefined)
    ) {
      return '';
    } else if (date1 == '' || date1 == null || date1 == undefined) {
      return date2;
    } else if (date2 == '' || date2 == null || date2 == undefined) {
      return date1;
    } else {
      date1 = moment(date1).format('YYYY-MM-DD');
      date2 = moment(date2).format('YYYY-MM-DD');
      if (date1 < date2) return date1;
      else return date2;
    }
  }
  compareDateMinimum(date1: any, date2: any): string {
   
    const date1Timestamp = date1 ? moment(date1).valueOf() : null;
    const date2Timestamp = date2 ? moment(date2).valueOf() : null;

    const timestamps: number[] = [date1Timestamp, date2Timestamp].filter(ts => ts !== null);

    // If no valid timestamps, return an empty string
    if (timestamps.length === 0) {
        return '';
    }

    // Return the maximum of the valid timestamps
    const maxTimestamp = Math.max(...timestamps);
    return moment(maxTimestamp).format('YYYY-MM-DD');
    
  }
  /**
   * @description Getting Min Date
   * @param projectEndDate 
   * @param e360EndDate 
   * @returns 
   */
  getMinDate(projectEndDate, e360EndDate) {
    if (!projectEndDate && !e360EndDate) {
      return null;
    }
    if (!projectEndDate) {
      return e360EndDate;
    }
    if (!e360EndDate) {
      return projectEndDate;
    }
    return projectEndDate > e360EndDate ? e360EndDate : projectEndDate ;
  }

  getShiftMasterList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getShiftMasterList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
}
