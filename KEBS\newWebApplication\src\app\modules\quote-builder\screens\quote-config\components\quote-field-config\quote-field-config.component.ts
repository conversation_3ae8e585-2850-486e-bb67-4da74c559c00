import { Component, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FormArray, FormBuilder, Validators } from '@angular/forms';

import { QuoteMainService } from 'src/app/modules/quote-builder/services/quote-main.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';

@Component({
  selector: 'app-quote-field-config',
  templateUrl: './quote-field-config.component.html',
  styleUrls: ['./quote-field-config.component.scss']
})
export class QuoteFieldConfigComponent implements OnInit {

  protected _onDestroy = new Subject<void>();

  isConfigLoading: boolean = true;
  isFieldDataBeingSaved: boolean = false;
  isBtnDataBeingSaved: boolean = false;

  fieldConfigName = 'quote_field_config';
  actionBtnConfigName = 'quote_action_button_config';

  quoteFieldArr = new FormArray([]);
  quoteActionBtnArr = new FormArray([]);

  constructor(private _quoteService: QuoteMainService, private _toaster: ToasterService, private fb: FormBuilder) { }

  ngOnInit(): void {

    this.getQuoteConfiguration();

  }

  getQuoteConfiguration = () => {

    const config = [this.fieldConfigName, this.actionBtnConfigName];

    this.isConfigLoading = true;

    this._quoteService.getQuoteConfiguration(config)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S" && res['data'] && res['data'].length) {

          for (const configItem of res['data'])
            if (configItem && configItem['quote_config_name'] && configItem.hasOwnProperty('quote_config_value')) {

              if (configItem['quote_config_name'] === this.fieldConfigName) {

                const quoteFieldList = configItem['quote_config_value'];

                for (const fieldItem of quoteFieldList)
                  this.quoteFieldArr.push(this.fb.group({
                    key: [fieldItem['key']],
                    columnName: [fieldItem['columnName']],
                    label: [fieldItem['label'], Validators.required],
                    fieldType: [fieldItem['fieldType']],
                    position: [fieldItem['position']],
                    col: [fieldItem['col']],
                    isForManpowerOnly: [fieldItem['isForManpowerOnly']],
                    isMandatory: [fieldItem['isMandatory']],
                    isVisible: [fieldItem['isVisible']],
                    isActive: [fieldItem['isActive']],
                    enableVariableName: [fieldItem['enableVariableName']],
                    decimalPart: [fieldItem['decimalPart']]
                  }));

              }

              else if (configItem['quote_config_name'] === this.actionBtnConfigName) {

                const actionBtnList = configItem['quote_config_value'];

                for (const btnItem of actionBtnList)
                  this.quoteActionBtnArr.push(this.fb.group({
                    id: [btnItem['id']],
                    key: [btnItem['key']],
                    label: [btnItem['label'], Validators.required],
                    isActive: [btnItem['isActive']]
                  }));

              }

            }

            for (const fieldItem of this.quoteFieldArr.controls){
              if (fieldItem.get('fieldType').value === 'dropdown') {

                fieldItem.get('decimalPart').disable();

              }

              if (fieldItem.get('key').value === 'positionId') {

                fieldItem.get('position').disable();
                fieldItem.get('isVisible').disable();
                fieldItem.get('isActive').disable();

                break;

              }
            }
        }

        else
          this._toaster.showError("Error", "Error in getting Quote Configuration", 3000);

        this.isConfigLoading = false;

      },
        err => {
          this.isConfigLoading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Quote configuration", 3000);
        });

  }

  saveFieldConfig = () => {

    this.isFieldDataBeingSaved = true;

    const config = [
      {
        config_name: this.fieldConfigName,
        config_value: this.quoteFieldArr.getRawValue()
      }
    ];

    this.updateQuoteConfig(config);

  }

  saveActionBtnConfig = () => {

    this.isBtnDataBeingSaved = true;

    const config = [
      {
        config_name: this.actionBtnConfigName,
        config_value: this.quoteActionBtnArr.value
      }
    ];

    this.updateQuoteConfig(config);

  }

  updateQuoteConfig = (config) => {

    this._quoteService.updateQuoteConfiguration(config)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess("Updated", `Configuration updated Successfully !`, 2000);

        else
          this._toaster.showError("Error", `Error in updating Configuration`, 3000);

        this.isFieldDataBeingSaved = false;
        this.isBtnDataBeingSaved = false;

      },
        err => {
          console.log(err);
          this.isFieldDataBeingSaved = false;
          this.isBtnDataBeingSaved = false;
          this._toaster.showError("Error", "Error in updating Quote Configuration", 3000);
        });

  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
