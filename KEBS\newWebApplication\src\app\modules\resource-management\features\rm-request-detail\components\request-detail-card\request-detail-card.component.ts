import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { ReqDetailDialogComponent } from 'src/app/modules/resource-management/shared-component/req-detail-dialog/req-detail-dialog/req-detail-dialog.component';
import { TmService } from 'src/app/modules/shared-lazy-loaded-components/team-members/services/tm.service';
import { SubSink } from 'subsink';
import { RmRequestDetailService } from '../../services/rm-request-detail.service';
import moment from 'moment';
import { ProjectListService } from 'src/app/modules/projects/features/project-list/services/project-list.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';

@Component({
  selector: 'app-request-detail-card',
  templateUrl: './request-detail-card.component.html',
  styleUrls: ['./request-detail-card.component.scss']
})
export class RequestDetailCardComponent implements OnInit {
  private subs = new SubSink();
  data:any=null;
  projectData:any=null;
  isDataLoaded:boolean = false;
  projectVersion:number;
  projectV2Data:any;
  rmConfig: any = null
  constructor(public dialog : MatDialog,public _rmService : RmService, private _router:Router, private _rmReqDetailService:RmRequestDetailService, private _tmService:TmService,private _projectService: ProjectListService,private _utilityService : UtilityService,
    private _toaster: ToasterService,private authService: PmAuthorizationService,) { }
  reqID:any;
  fieldConfig={};
  @Output() requestData = new EventEmitter();


  async ngOnInit() {
    await this.handleFieldConfig();
    this.rmConfig = await this.retrieveRmConfig()
    await this.checkProjectVersion();
    await this.authService.getProjectOverallAccess()
    await this.authService.getUserRoleAccessProjectList()
    await this.authService.getStatusList()
    this.reqID = parseInt(this._router.url.split('/')[5]);
    this.data = await this.getRequestDetail();
    this.projectData = await this.getProjectData(this.data['project_id']);
    this.projectV2Data = await this.getProjectV2Data(this.data['item_id'])
    this.isDataLoaded = true;
    this.requestData.emit(this.data)
    this.patchStatus()
    this._rmReqDetailService.setRequestData(this.data)
  }

  patchStatus() {
    this.subs.sink = this._rmReqDetailService.getRequestData().subscribe((res: any) => {
      // console.log('LUFFYYYYYYYY',res)
      if(res) {
        console.log('request detail card sub',this.data)
        this.data.STATUS = res['STATUS'] 
        this.data.request_status_id = res['request_status_id']
      }
    },(err) => {
      console.log(err)
    })
  }


  viewMore(){
    let dialogRef = this.dialog.open(ReqDetailDialogComponent, {
      height: '550px',
      width: '450px',
      data:{modalParams:this.reqID,fieldConfig:this.fieldConfig}
    });
  }
  getRequestDetail=()=>{
    let params = {
      'id':this.reqID
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService
        .getRequestDetailData(params)
        .subscribe(
          (res: any) => {
            if (res.err == false) {
              res['data'] = this.utcDateFormat(res['data'])
              resolve(res['data']);
            }
            else reject(res);
          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });
  }

  utcDateFormat(data) {
    data['start_date'] = moment(data['start_date']).utc().format('DD-MMM-YYYY')
    data['end_date'] = moment(data['end_date']).utc().format('DD-MMM-YYYY')
    data['expected_closure_date'] = moment(data['expected_closure_date']).utc().format('DD-MMM-YYYY')
    return data
  }

  retrieveFieldConfig(view_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.retrieveFieldConfig(view_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  async handleFieldConfig() {
    console.log("Handle field Config Triggered !!!!!!!!");
    let fieldList: any = await this.retrieveFieldConfig('create_request')
    console.log("Field List",fieldList)
    if(fieldList.length > 0) {
      fieldList.forEach((val,i) => {

        this.fieldConfig[val.field_key] = val

        // let formField = this.contactForm.get(val.field_key)

        // if(formField && val.is_active_field) {
          

        //   if(val.is_mandatory) {
        //     if(val.field_type == 'array'){
        //       for(let i=0;i<val.array_items.length;i++){
        //         if(val.array_items[i].is_mandatory){
        //           const itemControls = <FormArray>this.contactForm.controls[val.field_key];
        //           const itemFormGroup = <FormGroup>itemControls.controls[0];
        //           itemFormGroup.controls[val.array_items[i].field_key].setValidators([Validators.required])
        //         }
        //       }
        //     }
        //     else {
        //       formField.setValidators([Validators.required])
        //     }
        //   }
        //   else {
        //     formField.clearValidators()
        //   }

        //   if(val.has_default_value) {
        //     if(val.field_type == 'toggle-button') {
        //       switch(val.field_key) {
        //         case 'operation_model': {
        //           this.OperationModelToggle(val.field_default_value)
        //           break
        //         }
        //         case 'commercial': {
        //           this.CommercialToggle(val.field_default_value)
        //           break
        //         }
        //         case 'booking_type': {
        //           this.BookingTypeToggle(val.field_default_value)
        //           break
        //         }
        //       }
        //     }
        //     else if(val.field_type == 'toggle'){
        //       this.contactForm.get('need_client_interview').setValue(val.field_default_value);
        //       if(val.field_default_value == 1){
        //         this.toggle = true
        //       }
        //       else{
        //         this.toggle = false
        //       }
        //       console.log("toggle default value",typeof(val.field_default_value),this.contactForm.value)
        //     }
        //     else {
        //       formField.patchValue(val.field_default_value)
        //     }      
        //   }
        // }
      })
    }
    console.log("FIeld config",this.fieldConfig)
  }

  //Getting Project Data
  getProjectData(projectid) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._projectService.getProjectData(projectid,"ALL",false).subscribe(
        (res: any) => {
          if(res){
            resolve(res);
          }
          
        },
        (err) => {
          resolve(null);
        }
      );
    });
  }

  //Getting Project Data
  getProjectV2Data(item_id) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmReqDetailService.getProjectV2Data(item_id).subscribe(
        (res: any) => {
          if(res['messType'] == 'S'){
            resolve(res.data);
          }
          else{
            resolve(null)
          }
        },
        (err) => {
          resolve(null);
        }
      );
    });
  }

  checkProjectVersion() {
    this.subs.sink = this._rmReqDetailService.retrieveProjectVersion().subscribe((res: any) => {
      if(res) {
        console.log(res);
        this.projectVersion = res
      }
    },(err) => {
      this.projectVersion = 1
      console.log(err)
    })
  }

  async routeToProject(){
    
    if(this.projectVersion == 1){
      if(this.projectData){
        let itemData = this.projectData['item'];
        let index = 0;
        console.log(itemData)
        for(let i = 0; i < itemData.length; i++){
          if(itemData[i].itemId ==  this.data.item_id){
            index = i;
            console.log("Found the Index!!!");
            break;
          }
        }
        let navigationUrl = `main/project/${this.projectData.id}/${this._utilityService.encodeURIComponent(this.projectData.projectName)}/${index}/ ${this._utilityService.encodeURIComponent(this.data.item_name)}/${this.data.item_id}/teamMembers`;
        window.open(navigationUrl);
      }
      else{
        this._toaster.showError("Authorization","User dont have access to the project",3000)
      }
    }
    else{
      if(this.projectV2Data){

        if(this.data.quote_id && this.data.quote_id != null){
            let navigationUrl = `main/project-management/${this.projectV2Data.project_id}/${this._utilityService.encodeURIComponent(this.projectV2Data.project_code)}/${this.data.item_id}/ ${this._utilityService.encodeURIComponent(this.data.item_name)}/project-team/request-status`;
            window.open(navigationUrl);
          // else{
          //   this._toaster.showError("Authorization","User dont have access to the project",3000)
          // }
        }
        else if(this.data.quote_id == null){
          
            let navigationUrl = `main/project-management/${this.projectV2Data.project_id}/${this._utilityService.encodeURIComponent(this.projectV2Data.project_code)}/${this.data.item_id}/ ${this._utilityService.encodeURIComponent(this.data.item_name)}/project-team/resource-request`;
            window.open(navigationUrl);
          // }
          // else{
          //   this._toaster.showError("Authorization","User dont have access to the project",3000)
          // }
        }
        else{
          this._toaster.showError("Authorization","User dont have access to the project",3000)
        }
      }
      else{
        this._toaster.showError("Authorization","Project Details Not Found",3000)
      }
    }
    
  }

  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._rmReqDetailService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err)
          resolve(res.data)
      }, (err) => {
        console.log(err)
      })
    })
  }

}
