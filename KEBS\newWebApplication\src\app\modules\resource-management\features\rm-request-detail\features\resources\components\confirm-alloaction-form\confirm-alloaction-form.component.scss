::ng-deep .toast-warning {
  font-family: 'Plus Jakarta Sans' !important;
}
::ng-deep .toast-success {
  font-family: 'Plus Jakarta Sans' !important;
}
::ng-deep .toast-error {
  font-family: 'Plus Jakarta Sans' !important;
}
.allocation {
  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background{
    background-color:#79BA44 // Zifo theme
  }
   font-family: 'Plus Jakarta Sans';
   ::ng-deep .mat-button-toggle{
    font-family: 'Plus Jakarta Sans';
  }
  ::ng-deep .mat-select{
    font-family: 'Plus Jakarta Sans';
  }
  ::ng-deep .mat-select-value {
    display: table-cell;
    max-width: 0;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #5f6c81 !important;
    font-size: 14px !important;
    font-weight: 400 !important;
  }
  ::ng-deep .mat-radio-label-content{
    font-family: 'Plus Jakarta Sans'
  }
  .dateFieldBorder {
    border: 1px solid;
    border-color: #b9c0ca;
    // width: 260px;
    height: 38px;
    display: flex;
    border-radius: 8px;
  }
  .required-star {
    color: #cf0001;
  }
  .changetoRed {
   // background-color: #ee4961 !important;
    background-color:#79BA44 !important; // Zifo theme
    color: white !important;
    border: none !important;
  }
  .changetowhite {
    background-color: white !important;
    color: #8b95a5 !important;
    border: none !important;
  }
  .submitButton {
    border: none;
    margin-right: 10px;
    border-radius: 4px;
    padding: 10px;
    color: white;
    line-height: 16px;
    //background-color: #ee4961;
    background-color:#79BA44; // Zifo theme
  }
  .activityButton{
    margin: 4px;
    padding: 4px;
    border-radius: 4px;
    display: inline;
    background-color: white;
    color: #526179;
    border-color: #526179;
    border-width: 1.5px;
    font-style: normal;
    font-weight: 700;
    font-size: 10px;
    line-height: 16px;
    /* padding-top: 13px; */
    margin-top: 18px;
    margin-left: 2%;
  }
  .capacity{
    border: 1px solid;
    border-radius: 4px;
    border-color: #b9c0ca;
    width: 100%;
    height: 38px;
    display: flex;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.02em;
    color: #5F6C81;
  }
  .width-class{
    display: inline-block;
    width: 90%;
  }
  .buttonOn {
   // background-color: #ee4961;
    background-color:#79BA44; // Zifo theme
    cursor: pointer;
  }
  .buttonOff {
    //background-color: #d18c96;
    background-color:#a7c193;; // Zifo theme
    cursor: not-allowed;
  }
  .fieldheading {
    font-weight: 500;
    font-size: 12px;
    line-height: 24px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #6e7b8f;
    display: block;
  }
  .fieldvalue {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.02em;
    color: #5f6c81;
    width: 100%;
  }
  .single-chip{
    font-family:  'Plus Jakarta Sans' !important;
    font-size: 14px !important;
    font-weight: 400;
    color: #5f6c81 !important;
  }
  .head_class{
    font-weight: 400;
    font-size: 14px;
    color: #5f6c81;
  }
  .txtArea{
    height: 100px;
    width: 100%;
    border-radius: 4px;
    text-align: start;
    padding: 11px;
    border-color: lightgrey;
    outline: thin;
    border-radius: 8px;
    text-align: left !important;
    resize: none;
  }
  .title {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    display: flex;
    align-items: center;
    text-transform: capitalize;
    color: #45546e;
  }

  .profilename {
    font-weight: 700;
    font-size: 14px;
    line-height: 16px;
    text-transform: capitalize;
    color: #45546e;
    // margin-bottom: 0px;
    margin-bottom: 10px;
  }

  .imgProfile{
    height: 40px;
    width: 45px;
    border-width: 0px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
    border-radius: 50% !important;
    margin-right: 5px;
    margin-top: -3px;
  }
  .blurName {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    // text-align: right;
    letter-spacing: 0.02em;
    color: #5F6C81 !important;
  }
  .profiledata {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
  }

  .report_text{
    font-weight: 400;
    font-size: 13px;
    text-transform: capitalize;
    color: #5f6c81;
    display: block;
    padding-right: 7px;
    // margin-top: -12px;
    padding-bottom: 6px;
    font-weight: 500;

    &.ellipsis-class{
      width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }

  ::ng-deep .mat-form-field{
    width: 131%;
  }

  // ::ng-deep .mat-datepicker-toggle{
  //   margin-left: 2.5rem;
  // }

  ::ng-deep.mat-checkbox-frame {
    border-color: #B9C0CA; 
  }
  ::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
    background-color: #FFFFFF !important;
    margin-left: 0px !important;
    margin-top: 4.5px !important;
    width: 13px !important;
    height: 13px !important;
}

::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
    background-color: limegreen !important;
    width: 31px !important;
    height: 17px !important;
}

::ng-deep .mat-slide-toggle-bar {
    position: relative !important;
    width: 31px !important;
    height: 17px !important;
    flex-shrink: 0 !important;
    border-radius: 8px !important;
}

::ng-deep .mat-slide-toggle-thumb {
    margin-top: 4.5px !important;
    margin-left: 2px !important;
    width: 13px !important;
    height: 13px !important;
}

// ::ng-deep.mat-radio-button.mat-accent.mat-radio-checked
//     .mat-radio-outer-circle {
//     border-color: #ee4961;
//   }
//   ::ng-deep.mat-radio-button.mat-accent .mat-radio-inner-circle {
//     background-color: #ee4961;
//   }

  //Zifo theme
  ::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color:  #79BA44!important;   /*inner circle color change*/
  }
  ::ng-deep.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
   border-color: #79BA44 !important; /*outer ring color change*/
  }

  ::ng-deep.mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
    line-height: 30px !important;
    padding: 0 5px !important;
    font-weight: 400 !important;
    font-size: 12px !important;
  }

  .criteriaText {
    margin-left: 5px;
  }

  .mat-form-field {
    font-size: 13px !important;
    width: 100%;
  }
  .field-project-search{
    width: 11vw;
    display: block;
  }
  .header-name{
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 16px;
    margin-top: 0px;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 90%;
    overflow: hidden;
    line-height: 24px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
  }
  .inputSearch{
    display: flex;
    width: 77%;
  }
}

::ng-deep .single-chip .main-container .chip-text {
    font-family:  'Plus Jakarta Sans' !important;
    font-size: 14px !important;
    font-weight: 400;
    color: #5f6c81 !important;
}