<div class="row d-flex align-items-center justify-content-between p-3 doc-viewer">
    <div class="header">Document Viewer</div>
    <div class="d-flex align-items-center justify-content-around">
        <button *ngIf="isSupportedFormat(data.fileFormat)" mat-icon-button (click)="downloadFile(data.selectedFileUrl)"
            matTooltip="Download File">
            <mat-icon>download</mat-icon>
        </button>
        <button mat-icon-button (click)="dialogRef.close()" matTooltip="Close Viewer">
            <mat-icon>close</mat-icon>
        </button>
    </div>
</div>

<!-- Loading Spinner -->
<div *ngIf="!isLoaded">
    <div class="d-flex align-items-center justify-content-center">
        <img src="https://assets.kebs.app/images/spinner.svg" alt="Loading..." />
    </div>
    <div class="d-flex align-items-center justify-content-center">
        Please wait, we are opening the file!
    </div>
</div>

<!-- File Preview -->
<div *ngIf="isLoaded">

    <!-- Image Preview -->
    <div *ngIf="isImageFormat(data.fileFormat)" class="d-flex align-items-center justify-content-center mt-5">
        <img [src]="data.selectedFileUrl" alt="File Preview" style="height: 80%; width: 80%;" />
    </div>

    <!-- Other Format Previews -->
    <div *ngIf="!isImageFormat(data.fileFormat)" style="height: 80vh;">
        <!-- Google Docx Preview -->
        <ng-container>
            <ngx-doc-viewer *ngIf="isGoogleDocsSupportedFormat()" [url]="data.selectedFileUrl" viewer="google"
                style="height: 100%" (loaded)="isLoaded = true">
            </ngx-doc-viewer>
        </ng-container>

        <!-- iFrame Preview -->
        <!-- <ng-template *ngIf="isGoogleViewerBlocked">
            <iframe [src]="data.googleOpenInNewTab" width="100%" height="600px" style="border: none;"></iframe>
        </ng-template> -->
    </div>

    <!-- Unsupported File Format -->
    <!-- <div *ngIf="!isSupportedFormat(data.fileFormat)" class="unsupported-format">
        <p>This file format is not supported for preview. Please download the file to view it.</p>
        <button mat-button (click)="downloadFile(data.selectedFileUrl)">
            Download File
        </button>
    </div> -->
</div>