import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class TsReportService {

  constructor(private $http: HttpClient) { }

  // To Calculate MIS from tally data and split data 
  misAllInOne = (misPostingPeriod) => {
    return this.$http.post("/api/project/misFunctions/misAllInOne", {
      posting_period: misPostingPeriod
    });
  };

  // To run MIS related function post MIS 
  misPost = (periodYear, periodMonth) => {
    return this.$http.post("/api/project/misFunctions/misPost", {
      period_year: periodYear,
      period_month: periodMonth
    });
  };

  // MIS Related Table Data
  getMisTableData = (misView, date) => {
    console.log("getMisTableData")
    return this.$http.post("/api/project/misFunctions/getMisTableData", {
      table_params: misView,
      date: date,
      application_id : 283
    });
  };

  // MIS related table Master data
  getMisTableMasterData = () => {
    console.log("misTableMasterData")
    return this.$http.post("/api/project/misFunctions/getMisTableMasterData", {
    });
  };

  // Get Tally Month Freeze Period
  tallyMonthDataFreezePeriod = () => {
    return this.$http.post("/api/project/misFunctions/tallyMonthDataFreezePeriod", {
    });
  };


  // Tally Month Data Update 
  tallyMonthDataUpdate = (startDate, endDate, isToDelete, isToUpdateYTD, freezePeriod, changeInFreezePeriod) => {
    console.log("tallyMonthDataUpdate")
    return this.$http.post("/api/project/misFunctions/tallyMonthDataInsert", {
      start_date: startDate,
      end_date: endDate,
      is_to_delete: isToDelete,
      is_to_update_ytd: isToUpdateYTD,
      freeze_period: freezePeriod,
      change_in_freeze_period: changeInFreezePeriod
    });
  };

  // MIS related table Master data
  tsNotifApprovalReminders = () => {
    console.log("tsNotifApprovalReminders")
    return this.$http.post("/api/tsPrimary/tsNotifApprovalReminders", {
    });
  };
  
  // MIS related table Master data
  tsNotifDataRetrival = (apiName, data) => {
    console.log(apiName)
    return this.$http.post(apiName, data);
  };
  
  // MIS related table Master data
  tsNotifFetchReports = () => {
    console.log("tsNotifFetchReports")
    return this.$http.post("/api/tsPrimary/tsNotifFetchReports", {
    });
  };
}
