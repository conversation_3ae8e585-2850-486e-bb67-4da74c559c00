import { CheckWeekOffPipe } from './pipes/checkWeekOff/check-week-off.pipe';
import { CheckIfTaskPresentPipe } from './pipes/checkIfTaskPresent/check-if-task-present.pipe';
import { CheckFullDayLeavePipe } from './pipes/checkFullDayLeave/check-full-day-leave.pipe';
import { TotalHoursDailyPipe } from './pipes/totalHoursDaily/total-hours-daily.pipe';
import { WeeklyOvertimeColorStampPipe } from './pipes/weeklyOvertimeColorStamp/weekly-overtime-color-stamp.pipe';
import { WeeklyLeaveColorStampPipe } from './pipes/weeklyLeaveColorStamp/weekly-leave-color-stamp.pipe';
import { DateFormat3Pipe } from './pipes/dateFormat3/date-format3.pipe';
import { HttpClientModule } from '@angular/common/http';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TsV2SubmissionRoutingModule } from './ts-v2-submission-routing.module';
import { TsV2SubmissionLandingPageComponent } from './pages/ts-v2-submission-landing-page/ts-v2-submission-landing-page.component';
import { MatMenuModule } from '@angular/material/menu';
import { TsPoliciesModalComponent } from './lazy-loaded-components/ts-policies-modal/ts-policies-modal.component';
import { TsApproversModalComponent } from './lazy-loaded-components/ts-approvers-modal/ts-approvers-modal.component';
import { TsRemoveFilledhoursModalComponent } from './lazy-loaded-components/ts-remove-filledhours-modal/ts-remove-filledhours-modal.component';
import { TsV2AddTaskManuallyComponent } from './lazy-loaded-components/ts-v2-add-task-manually/ts-v2-add-task-manually.component';
import { TsClearModalComponent } from './lazy-loaded-components/ts-clear-modal/ts-clear-modal.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatExpansionModule } from '@angular/material/expansion';
import { DateFormatPipe } from './pipes/dateFormat/date-format.pipe';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateFormat2Pipe } from './pipes/dateFormat2/date-format2.pipe';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatListModule } from '@angular/material/list';
import { MatToolbarModule } from '@angular/material/toolbar';
import { SharedComponentsModule } from '../../../../app-shared/app-shared-components/components.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CcDisplayFormatPipe } from './pipes/ccDisplayFormat/cc-display-format.pipe';
import { MatTableModule } from '@angular/material/table';
import { TsAccessModalComponent } from './lazy-loaded-components/ts-access-modal/ts-access-modal.component';
import { MatButtonModule } from '@angular/material/button';
import { OverlayModule } from '@angular/cdk/overlay';
import { TsOverallStatusPipe } from './pipes/tsOverallStatus/ts-overall-status.pipe';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { StatusTooltipClassPipe } from './pipes/statusTooltipClass/status-tooltip-class.pipe';
import { HoursWorkedSplitPipe } from './pipes/hoursWorkedSplit/hours-worked-split.pipe';
import { TotalHoursWeeklyPipe } from './pipes/totalHoursWeekly/total-hours-weekly.pipe';
import { TotalOvertimeHoursWeeklyPipe } from './pipes/totalOvertimeHoursWeekly/total-overtime-hours-weekly.pipe';
import { TotalLeavesWeeklyPipe } from './pipes/totalLeavesWeekly/total-leaves-weekly.pipe';
import { WeeklyHoursColorStampPipe } from './pipes/weeklyHoursColorStamp/weekly-hours-color-stamp.pipe';
import { TotalHoursCcWeeklyPipe } from './pipes/totalHoursCcWeekly/total-hours-cc-weekly.pipe';
import { TotalBillingTypeHoursWeeklyPipe } from './pipes/totalBillingTypeHoursWeekly/total-billing-type-hours-weekly.pipe';
import { IsDeptCcPipe } from './pipes/isDeptCc/is-dept-cc.pipe';
import { StatusRoundSymbolClassPipe } from './pipes/statusRoundSymbolClass/status-round-symbol-class.pipe';
import { StatusTextClassPipe } from './pipes/statusTextClass/status-text-class.pipe';
import { TsHistoryModalComponent } from './lazy-loaded-components/ts-history-modal/ts-history-modal.component';
import { HolidayTooltipPipe } from './pipes/holidayTooltip/holiday-tooltip.pipe';
import { DateOfJoiningCheckPipe } from './pipes/dateOfJoiningCheck/date-of-joining-check.pipe';
import { TimeFormatPipe } from './pipes/timeFormat/time-format.pipe';
import { LeaveCountZeroPatchPipe } from './pipes/leaveCountZeroPatch/leave-count-zero-patch.pipe';
import { TsHolidayCalendarModalComponent } from './lazy-loaded-components/ts-holiday-calendar-modal/ts-holiday-calendar-modal.component';
import { DateFormat4Pipe } from './pipes/dateFormat4/date-format4.pipe';
import { AlertPopupModalComponent } from './lazy-loaded-components/alert-popup-modal/alert-popup-modal.component';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { TsPlannedHoursHistoryComponent } from './lazy-loaded-components/ts-planned-hours-history/ts-planned-hours-history.component';
import { TsV2DashboardComponent } from './lazy-loaded-components/ts-v2-dashboard/ts-v2-dashboard.component';
import { LocalTimestampPipe } from './pipes/localTimestamp/local-timestamp.pipe';
import { IsQuickFillActivePipe } from './pipes/isQuickFillActive/is-quick-fill-active.pipe';
import { TsV2PreviousWeekInfoModalComponent } from './lazy-loaded-components/ts-v2-previous-week-info-modal/ts-v2-previous-week-info-modal.component';
import { IsTimesheetSubmissionLockedPipe } from './pipes/isTimesheetSubmissionLocked/is-timesheet-submission-locked.pipe';
import { GetCurrentWeekEndDatePipe } from './pipes/getCurrentWeekEndDate/get-current-week-end-date.pipe';
import { TsV2EventsModalComponent } from './lazy-loaded-components/ts-v2-events-modal/ts-v2-events-modal.component';
import { PendingChangesGuard } from '../ts-v2-submission/guards/pending-changes.guard';
import { SanitizeVideoUrlPipe } from './pipes/sanitizeVideoUrl/sanitize-video-url.pipe';
import { NoAvailableTasksMessagePipe } from './pipes/noAvailableTasksMessage/no-available-tasks-message.pipe';
import {
  DxPopoverModule,
  DxTooltipModule,
  DxTemplateModule,
  DxScrollViewModule,
} from 'devextreme-angular';
import { IsCostCenterBlockedPipe } from './pipes/isCostCenterBlocked/is-cost-center-blocked.pipe';
import { DateOfExitCheckPipe } from './pipes/dateOfExitCheck/date-of-exit-check.pipe';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { TimeInOutFormatPipe } from './pipes/timeInOutFormat/time-in-out-format.pipe';
import { GetRegularAndOvertimeHoursPipe } from './pipes/getRegularAndOvertimeHours/get-regular-and-overtime-hours.pipe';
import { DailyHoursColorStampPipe } from './pipes/dailyHoursColorStamp/daily-hours-color-stamp.pipe';
import { CheckAttendanceTypeIdPipe } from './pipes/checkAttendanceTypeId/check-attendance-type-id.pipe';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { NoOfFutureMonthsAllowedPipe } from './pipes/noOfFutureMonthsAllowed/no-of-future-months-allowed.pipe';
import { NoOfPastMonthsAllowedPipe } from './pipes/noOfPastMonthsAllowed/no-of-past-months-allowed.pipe';
import { TsV2ApproversNotFoundModalComponent } from './lazy-loaded-components/ts-v2-approvers-not-found-modal/ts-v2-approvers-not-found-modal.component';
import { TsV2QuotaNotMetModalComponent } from './lazy-loaded-components/ts-v2-quota-not-met-modal/ts-v2-quota-not-met-modal.component';
import { LwdSweetAlertModalComponent } from './lazy-loaded-components/lwd-sweet-alert-modal/lwd-sweet-alert-modal.component';
import { TsV2ManualApproverSelectionModalComponent } from './lazy-loaded-components/ts-v2-manual-approver-selection-modal/ts-v2-manual-approver-selection-modal.component';
import { TsV2EditApproversModalComponent } from './lazy-loaded-components/ts-v2-edit-approvers-modal/ts-v2-edit-approvers-modal.component';
import { PlannedHoursLatestRecordPipe } from './pipes/plannedHoursLatestRecord/planned-hours-latest-record.pipe';
import { TsV2HelpManualComponent } from './lazy-loaded-components/ts-v2-help-manual/ts-v2-help-manual.component';
import { NgxDocViewerModule } from 'ngx-doc-viewer';
import { TsV2ApproversNewModalComponent } from './lazy-loaded-components/ts-v2-approvers-new-modal/ts-v2-approvers-new-modal.component';
import { TimerDurationCalcPipe } from './pipes/timerDurationCalc/timer-duration-calc.pipe';
import { DisplayTimerPipe } from './pipes/displayTimer/display-timer.pipe';
import { IsTimerRunningPipe } from './pipes/isTimerRunning/is-timer-running.pipe';
import { GetTimerIndexPipe } from './pipes/getTimerIndex/get-timer-index.pipe';
import { TsTaskModalComponent } from './lazy-loaded-components/ts-task-modal/ts-task-modal.component';
import { DisplayWeekOffCheckboxPipe } from './pipes/displayWeekOffCheckbox/display-week-off-checkbox.pipe';
import { TsPrefillModalComponent } from './lazy-loaded-components/ts-prefill-modal/ts-prefill-modal.component';
import { MatChipsModule } from '@angular/material/chips';
import { TimesheetRejectCommentsComponent } from './lazy-loaded-components/timesheet-reject-comments/timesheet-reject-comments.component';
import { WarningMessagePopupModalComponent } from './lazy-loaded-components/warning-message-popup-modal/warning-message-popup-modal.component';
import { DateFormat5Pipe } from './pipes/dateFormat5/date-format5.pipe'
import { CurrentDateBorderColorPipe } from './pipes/currentDateBorder/current-date-border-color.pipe';
@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [
    TsV2SubmissionLandingPageComponent,
    TsPoliciesModalComponent,
    TsClearModalComponent,
    TsApproversModalComponent,
    TsRemoveFilledhoursModalComponent,
    TsV2AddTaskManuallyComponent,
    DateFormatPipe,
    DateFormat2Pipe,
    CcDisplayFormatPipe,
    TsAccessModalComponent,
    DateFormat3Pipe,
    TsOverallStatusPipe,
    StatusTooltipClassPipe,
    HoursWorkedSplitPipe,
    TotalHoursWeeklyPipe,
    TotalOvertimeHoursWeeklyPipe,
    TotalLeavesWeeklyPipe,
    WeeklyHoursColorStampPipe,
    TotalHoursCcWeeklyPipe,
    TotalBillingTypeHoursWeeklyPipe,
    WeeklyLeaveColorStampPipe,
    WeeklyOvertimeColorStampPipe,
    TotalHoursDailyPipe,
    CheckFullDayLeavePipe,
    CheckIfTaskPresentPipe,
    CheckWeekOffPipe,
    IsDeptCcPipe,
    StatusRoundSymbolClassPipe,
    StatusTextClassPipe,
    TsHistoryModalComponent,
    HolidayTooltipPipe,
    DateOfJoiningCheckPipe,
    TimeFormatPipe,
    LeaveCountZeroPatchPipe,
    TsHolidayCalendarModalComponent,
    DateFormat4Pipe,
    AlertPopupModalComponent,
    TsPlannedHoursHistoryComponent,
    TsV2DashboardComponent,
    LocalTimestampPipe,
    IsQuickFillActivePipe,
    TsV2PreviousWeekInfoModalComponent,
    IsTimesheetSubmissionLockedPipe,
    GetCurrentWeekEndDatePipe,
    TsV2EventsModalComponent,
    SanitizeVideoUrlPipe,
    NoAvailableTasksMessagePipe,
    IsCostCenterBlockedPipe,
    DateOfExitCheckPipe,
    TimeInOutFormatPipe,
    GetRegularAndOvertimeHoursPipe,
    DailyHoursColorStampPipe,
    CheckAttendanceTypeIdPipe,
    NoOfFutureMonthsAllowedPipe,
    NoOfPastMonthsAllowedPipe,
    TsV2ApproversNotFoundModalComponent,
    TsV2QuotaNotMetModalComponent,
    LwdSweetAlertModalComponent,
    TsV2ManualApproverSelectionModalComponent,
    TsV2EditApproversModalComponent,
    PlannedHoursLatestRecordPipe,
    TsV2HelpManualComponent,
    TsV2ApproversNewModalComponent,
    TimerDurationCalcPipe,
    DisplayTimerPipe,
    IsTimerRunningPipe,
    GetTimerIndexPipe,
    TsTaskModalComponent,
    DisplayWeekOffCheckboxPipe,
    TsPrefillModalComponent,
    TimesheetRejectCommentsComponent,
    WarningMessagePopupModalComponent,
    DateFormat5Pipe,
    CurrentDateBorderColorPipe
  ],
  imports: [
    CommonModule,
    TsV2SubmissionRoutingModule,
    MatDividerModule,
    MatIconModule,
    MatMenuModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    MatExpansionModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    HttpClientModule,
    MatSelectModule,
    MatGridListModule,
    MatListModule,
    MatToolbarModule,
    SharedComponentsModule,
    MatCheckboxModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatTableModule,
    MatButtonModule,
    OverlayModule,
    MatProgressSpinnerModule,
    InfiniteScrollModule,
    DxTemplateModule,
    DxPopoverModule,
    DxTooltipModule,
    MatButtonToggleModule,
    ScrollingModule,
    NgxDocViewerModule,
    DxScrollViewModule,
    MatChipsModule,
  ],
  providers: [PendingChangesGuard],
})
export class TsV2SubmissionModule {}
