import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { PmHistoryDialogComponent } from '../../components/pm-history-dialog/pm-history-dialog.component';
import { ComponentCanDeactivate } from '../../guards/changes-alert.guard';
import { Observable, Subject } from 'rxjs';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { elementAt, takeUntil } from 'rxjs/operators';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { ResourceLoadingServiceService } from '../../services/resource-loading-service.service'
import { SubSink } from 'subsink';
import { AnyMxRecord } from 'dns';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';

@Component({
  selector: 'app-resource-loading-landing-page',
  templateUrl: './resource-loading-landing-page.component.html',
  styleUrls: ['./resource-loading-landing-page.component.scss']
})
export class ResourceLoadingLandingPageComponent implements OnInit, ComponentCanDeactivate {

  @Output() displayForecast = new EventEmitter<any>();

  @Input() height: string = null;
  @Input() deliverable_id: any=null;
  @Input() start_date: any;
  @Input() end_date: any;
  @Input() resource_type_id: any=1;
  @Input() quote_id: any;
  @Input() projectId: any;
  @Input() itemId: any;
  @Input() actualData: any;
  @Input() area: any;
  @Input() PositionData: any;
  @Input() billing_advice_month: any;
  @Input() billing_advice_year: any;

  currentDate = new Date();
  monthList: any;
  monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];
  positionList: any = [];
 
  toggleViewData:any
  toggleViewList:any = [
    {
      id:1,
      name:'People',
      mat_icon:'people'
    },
    {
      id:2,
      name:'Employee',
      mat_icon:'employee'
    },
  ]
  plannedViewEnabled:boolean = false;
  apiChanges:boolean = false;
  projectData:any = null;
  protected _onDestroy = new Subject<void>();
  public isComponentLoading:boolean = false;
  carryForwardList:any;
  subs = new SubSink();
  quoteData:any = null;
  versionId:number;
  formConfig:any;
  retrieveMessages:any;
  updatedMonthList:any = [];
  statusId:any;
  value_digit: number = 2;
  loaderObject = {
    saveLoader: false,
    releaseLoader: false
  }
  initialLoading: boolean = false;
  balanceCheck: any;
  serviceTypeList:any=[];
  serviceData:any = null;
  decimalPlaces:number = 2;
  quote_decimalPlaces: number = 2;
  
  is_deliverable: boolean=true;
  unit:string = 'Hours';
  resourceTypeConfig: Array<Object> = []

  fixedBillingPlanEnabled: boolean = false;
  resource_config: Object = null;
  isAlreadyForecasted: boolean=false;

  constructor(private _router: Router, private _route: ActivatedRoute,public dialog: MatDialog,private _utilityService: UtilityService, private _resourceService : ResourceLoadingServiceService, private _toasterService: ToasterMessageService,private masterService: PmMasterService) { }

  async ngOnInit() {
    this.isComponentLoading = true;
    await this.initializeFormConfigData();
    console.log("Area", this.area)
    if(this.area!="billing-advice")
    {
      
      this.projectId = parseInt(this._router.url.split('/')[3]);
      this.itemId = parseInt(this._router.url.split('/')[4]);
      this.quote_id = parseInt(this._router.url.split('/')[5]);
      if(this.deliverable_id)
        this.fixedBillingPlanEnabled = true
      else
        this.fixedBillingPlanEnabled = false
    }
    else
    {
  
        this.fixedBillingPlanEnabled = true
      
    }
    this.calculateDynamicStyle();
    await this.getProjectDetails();
    await this.resourceTypeConfigQuote()
    await this.getVersionData();
    await this.getQuoteDetailsForResourceLoading();

    let billingPlanMilestoneDate = _.findWhere(this.formConfig,{type:"billing-plan", field_name:"date_pass_milestone", is_active: true})

    if(this.deliverable_id && billingPlanMilestoneDate)
    {
      this.monthList = this.generateMonths(new Date(this.start_date), new Date(this.end_date));
    }
    else
    {
      this.monthList = this.generateMonths(new Date(this.projectData?.planned_start_date), new Date(this.projectData?.planned_end_date));
    }
    this.positionList = await this.getResourceLoadingData();

    console.log("Area", this.area, this.PositionData)

    if(this.area=="billing-advice")
    {
        console.log("Inside Area")
        for(let position of this.positionList)
        {
            let isPositionPresent = _.findWhere(this.PositionData,{id: position['position_id']})

            if(isPositionPresent)
            {
              for(let item of this.PositionData)
              {
                  if(position['position_id'] == item['id'])
                  {
                      position['balance'] = item['future_effort_required']
                      position['rate_per_unit'] =item['revised_per_hour_rate']

                      for(let month of position['monthList'])
                      {
                        if(month['month']==this.billing_advice_month && month['year']==this.billing_advice_year)
                        {
                            month['actual'] = item['actual_billable_hours'];
                            month['carry_forward']=  month['planned'] - month['actual']
                            month['is_disabled']=true
                        }
                      }
                  }
              }
            }
            else
            {
              for(let month of position['monthList'])
              {
                if(month['month']==this.billing_advice_month && month['year']==this.billing_advice_year)
                {
                    month['actual'] = 0
                    position['balance']  = month['planned']
                    month['carry_forward']=  month['planned'] - month['actual']
                    month['is_disabled']=true
                }
              }
            }

            
        }

        this.editPlanned()

      
    }
    

    if(this.initialLoading){
      let data: any = await this.getInitialLoadQuoteData()
      await this.formatPositionQuoteList(data);
      this.statusId = 1;

      for(let d of data)
      {
         d['position_id'] =d['position']
      }
      let data_payload = {
        data: data,
        project_id: this.projectId,
        project_item_id: this.itemId,
        quote_id:this.quote_id,
        version_id: 1,
        status_id: this.statusId,
        action_id:2,
        resource_type_id: this.resource_type_id,
        deliverable_id: this.deliverable_id
      }
      
      await this.getSaveResourceLoadingData(data_payload)
      this.positionList = await this.getResourceLoadingData();
    }
    this.updateCarryForwardList();
    await this.masterService.serviceTypeList().then((res)=>{
      this.serviceTypeList = res;
    });
    
    this.serviceData = _.findWhere(this.serviceTypeList, {
      id: (this.projectData.service_type_id ? this.projectData.service_type_id : null),
    });
    // await this.updatePositionList(); //Commenting for Updating indicator for Position Wise
    this.resource_config = _.findWhere(this.resourceTypeConfig, {
      quote_resource_type: this.resource_type_id
    });
    this.calculateDynamicStyle();
    this.isComponentLoading = false;
    this.displayForecast.emit()
  }

  /**
   * @description initializing Form Config Master data
   */
  async initializeFormConfigData() {
    await this.masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });
    this.retrieveMessages = _.where(this.formConfig, {
      type: 'resource-loading',
      field_name: 'messages',
      is_active: true,
    });

    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });
    let button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : '#90ee90'
        : '#90ee90';
    
    document.documentElement.style.setProperty('--teamButton', button);
    let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    let balanceConfig = _.where(this.formConfig, {
      type: 'resource-loading',
      field_name: 'balance-check'
    });
    this.balanceCheck  =
    balanceConfig.length > 0
        ? balanceConfig[0]
          ? balanceConfig[0]['is_active']
          : true
        : true;
    let noteHeader = retrieveStyles.length > 0 ? retrieveStyles[0].data.note_header ? retrieveStyles[0].data.note_header : "rgb(66, 191, 221)" : "rgb(66, 191, 221)";
    document.documentElement.style.setProperty('--milestoneNoteheader', noteHeader)

  
    let retrieveDecimalPlaces = _.where(this.formConfig, {
      type: 'billing-plan',
      field_name: 'decimal_places',
      is_active: true,
    });
    let decimal_value = (retrieveDecimalPlaces && retrieveDecimalPlaces.length > 0) ? (retrieveDecimalPlaces[0]) : null;
    this.quote_decimalPlaces = decimal_value ? (decimal_value.disable_decimal ? 0 : (decimal_value.decimal_places ? decimal_value.decimal_places : 2)) : 2
    
    let resourceTypeConfig = _.where(this.formConfig, {
      type: 'resource-loading',
      field_name: 'resource-type-config'
    });
    this.resourceTypeConfig  =
    resourceTypeConfig.length > 0
        ? (resourceTypeConfig[0] && resourceTypeConfig[0]['resource_config'])
          ? resourceTypeConfig[0]['resource_config']
          : this.resourceTypeConfig
        : this.resourceTypeConfig;

    let shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#FDE4E2" : "#FDE4E2";
    document.documentElement.style.setProperty('--teamShade', shades);
  }

  toggleSelectedButton(value) {
    this.toggleViewData = value
  }

  editPlanned(){
    this.apiChanges = true;
    this.plannedViewEnabled = true;
    this.calculateDynamicStyle();
  }

  async savePlanned(){
    
    this.loaderObject.saveLoader = true;
    if(this.updatedMonthList.length > 0){
      this.statusId = 1;
      this.versionId += 1;
      let data_payload = {
        data: this.positionList,
        project_id: this.projectId,
        project_item_id: this.itemId,
        quote_id:this.quote_id,
        version_id: this.versionId,
        status_id:this.statusId,
        action_id:1,
        resource_type_id: this.resource_type_id,
        deliverable_id: this.deliverable_id
      }
    
      await this.getSaveResourceLoadingData(data_payload)
      this.updatedMonthList = [];
    }
    else{
      this.updatedMonthList = [];
      this._toasterService.showWarning('No Changes Made',10000);
    }
    
    this.loaderObject.saveLoader = false;
    this.plannedViewEnabled = false;
    this.calculateDynamicStyle();
    this.apiChanges = false;
  }

  async releaseForCosting(){
    
    this.loaderObject.releaseLoader = true;
    let writeOff = _.findWhere(this.formConfig,{
      type: 'resource-loading',
      field_name: 'write-off',
      is_active: true,
    })
    if(writeOff)
    {
      if(this.checkWriteOffPositionBalance())
      {
        this.versionId += 1;
        this.statusId = 2;
        let data_payload = {
          data: this.positionList,
          project_id: this.projectId,
          project_item_id: this.itemId,
          quote_id:this.quote_id,
          version_id: this.versionId,
          status_id:this.statusId,
          action_id:1,
          resource_type_id: this.resource_type_id,
          deliverable_id: this.deliverable_id
        }
        await this.getSaveResourceLoadingData(data_payload)
        let promises = [];
        let manPowerCostPositionList = this.positionList.filter((item) => item.resource_type === 1);
      
        if (manPowerCostPositionList.length > 0) {
            promises.push(this.createMilestoneData(manPowerCostPositionList, true));
        }

        let nonManPowerCostPositionList = this.positionList.filter((item) => item.resource_type != 1);

        if (nonManPowerCostPositionList.length > 0) {

            for(let position of nonManPowerCostPositionList)
            {
                let val = _.where(this.resourceTypeConfig,{quote_resource_type: position['resource_type']})


                if(val.length>0)
                {
                  position['milestone_type'] = val['milestone_type']
                }
                
            }
            promises.push(this.createBillsData(nonManPowerCostPositionList, true));
        }
        this._resourceService.insertPlannedTimeTracker(this.projectId, this.itemId, this.quote_id)
        this.updatedMonthList = [];
        this.loaderObject.releaseLoader = false;
        this.plannedViewEnabled = false; 
        this.calculateDynamicStyle();
        this.apiChanges = false;
      }
      
      
    }
    else
    {

      if(this.checkPositionBalance()){
        this.versionId += 1;
        this.statusId = 2;
        let data_payload = {
          data: this.positionList,
          project_id: this.projectId,
          project_item_id: this.itemId,
          quote_id:this.quote_id,
          version_id: this.versionId,
          status_id:this.statusId,
          action_id:1,
          resource_type_id: this.resource_type_id,
          deliverable_id: this.deliverable_id
        }

        console.log("Save Resource Loading!")
        await this.getSaveResourceLoadingData(data_payload)
        let promises = [];
        let manPowerCostPositionList = this.positionList.filter((item) => item.resource_type === 1);
      
        if (manPowerCostPositionList.length > 0) {
            promises.push(this.createMilestoneData(manPowerCostPositionList, true));
        }

        let nonManPowerCostPositionList = this.positionList.filter((item) => item.resource_type != 1);

        if (nonManPowerCostPositionList.length > 0) {
            
            for(let position of nonManPowerCostPositionList)
            {
                let val = _.where(this.resourceTypeConfig,{quote_resource_type: position['resource_type']})


                if(val.length>0)
                {
                  position['milestone_type'] = val['milestone_type']
                }
                
            }
            promises.push(this.createBillsData(nonManPowerCostPositionList, true));
        }
        
        this.updatedMonthList = [];
        this.loaderObject.releaseLoader = false;
        this.plannedViewEnabled = false; 
        this.calculateDynamicStyle();
        this.apiChanges = false;
      }
      else if(!this.checkPositionBalance()){
        this._toasterService.showWarning('Please Plan for Total Budget',20000);
        this.loaderObject.releaseLoader = false;
      }
      else{
        this.updatedMonthList = [];
        this._toasterService.showWarning('No Changes Made',10000);
        this.loaderObject.releaseLoader = false;
        this.plannedViewEnabled = false;
        this.calculateDynamicStyle();
        this.apiChanges = false;
      }
    }
  }

  routeBack(){
    if(this.apiChanges){
      this._utilityService.openConfirmationSweetAlertWithCustom("Are you sure", "You want to Close without saving").then((result) => {
        
        if (result && this.projectData) {
           
           let project_name = this.projectData?.project_name;
           let item_name = this.projectData?.item_name;
           let navigationUrl = `main/project-management/${this.projectId}/${this._utilityService.encodeURIComponent(project_name)}/${this.itemId}/ ${this._utilityService.encodeURIComponent(item_name)}/project-team/team`;
           this._router.navigateByUrl(navigationUrl);
        }
      });
    }
    else{
      
      if(this.projectData){
        let project_name = this.projectData?.project_name;
           let item_name = this.projectData?.item_name;
           let navigationUrl = `main/project-management/${this.projectId}/${this._utilityService.encodeURIComponent(project_name)}/${this.itemId}/ ${this._utilityService.encodeURIComponent(item_name)}/project-team/team`;
           this._router.navigateByUrl(navigationUrl);
      }
    }
    
  }

  openHistoryDialog(){
    let data = {
      projectId: this.projectId,
      itemId: this.itemId,
      quoteId: this.quote_id,
      deliverableId: this.deliverable_id,
      resourceTypeId: this.resource_type_id
    }
    const dialogRef = this.dialog.open(PmHistoryDialogComponent, {
      width: "35%",
      height: "100%",
      position: {top:'0px',right: '0%' },
      data: { modalParams: data},
    });
    dialogRef.afterClosed().subscribe((result:any) => {
      
    });
  }

  /**
   * @description checking whether the month is editable or not
   * @param month 
   * @returns 
   */
  isMonthEditable(month: {
    id: number;
    month: string;
    year: number;
    index: number;
  }): boolean {
    const currentMonthIndex = this.currentDate.getMonth(); // 0-based month index
    const currentYear = this.currentDate.getFullYear();
    return (
      month.year > currentYear ||
      (month.year === currentYear && month.index >= currentMonthIndex)
    );
  }

  /**
   * @description Generating all the months from start and end date
   * @param startDate 
   * @param endDate 
   * @returns 
   */
  generateMonths(
    startDate: Date,
    endDate: Date
  ): { id: number; month: string; year: number }[] {
    let months = [];
    let currentDate = new Date(Date.UTC(startDate.getUTCFullYear(), startDate.getUTCMonth(), 1));
    let endUTCDate = new Date(Date.UTC(endDate.getUTCFullYear(), endDate.getUTCMonth(), 1));
    let id = 1;

    while (currentDate <= endUTCDate) {
      months.push({
        id: id++,
        index: currentDate.getUTCMonth(),
        month: this.monthNames[currentDate.getUTCMonth()],
        year: currentDate.getUTCFullYear(),
      });

      // Move to the next month
      currentDate.setUTCMonth(currentDate.getUTCMonth() + 1);
    }

    return months;
  }

  /**
   * @description Getting Table cell Value
   * @param position_id 
   * @param month 
   * @param type 
   * @returns 
   */
  getCellValue(position_id: number, month: any, type: string): number {
    const position = this.positionList.find(p => p.position_id === position_id);
    if (position  && position?.monthList) {
      const monthData = position.monthList.find(m => m.month === (month.index + 1) && m.year === month.year);
      if (monthData) {
        if (type === 'planned') {
          return monthData.planned ?? 0;
        } else if (type === 'actual') {
          return monthData.actual ?? null;
        } else if (type === 'plannedVsActual') {
          if(monthData.is_disabled){
            return monthData.actual ?? 0
          }
          else{
           return monthData.planned ?? 0
          }
        }
        else if(type == 'carryForward'){
          return monthData.carry_forward ?? null
        }
        else if(type == 'disabled'){
          return (monthData.is_disabled) ?? false
        }
      }
    }
    return 0;
  }

  /**
   * @description Updating the Final Carry Forward value
   */
  updateCarryForwardList(){
    for(let position of this.positionList){
      
      let sum_value = 0;
      if(position?.monthList){
        for(let month of position?.monthList){
          
          sum_value += (month.carry_forward ? month.carry_forward : 0)
        }
      }
      position['carry_forward'] = sum_value
      
    }
  }

  @HostListener('window:beforeunload')
  canDeactivate(): Observable<boolean> | boolean {
    if (this.apiChanges === true) {
      return false;
    } else {
      return true;
    }
  }

  updateCellValue(event: any, positionId: any, monthValue: any, key: string) {
    const value = parseFloat(event.target.innerText);
    let positionData = this.positionList.find(pos => pos.position_id === positionId);
    
    // Check the Config to allow decimals, otherwise restrict to whole numbers
    const decimalPlace =  (this.resource_config && this.resource_config['allow_decimals'] ? this.resource_config['allow_decimals'] : false) ;
    
    if (!isNaN(value)) {
      // If decimals are allowed, round to this.decimalPlaces; otherwise, force to integer
      const formattedValue = decimalPlace 
        ? parseFloat(value.toFixed(this.decimalPlaces)) 
        : Math.floor(value); // Whole number if no decimals allowed
  
      const originalValue = decimalPlace 
        ? parseFloat(this.getCellValue(positionId, monthValue, key).toFixed(this.decimalPlaces)) 
        : Math.floor(this.getCellValue(positionId, monthValue, key));
  
      // Update the cell value
      this.setCellValue(positionId, monthValue.index + 1, monthValue.year, key, formattedValue);
  
      const position = this.positionList.find(pos => pos.position_id === positionId);
      let positionBalance = position ? (position.balance ? position.balance : 0) : 0;
      let balance = 0;
  
      // Calculate the difference between the original and formatted values
      if (originalValue > formattedValue) {
        let diff = originalValue - formattedValue; 
        balance = positionBalance + diff; 
      } else {
        let diff = formattedValue - originalValue; 
        balance = positionBalance - diff;
      }
  
      // Ensure balance is rounded based on decimal places
      balance = decimalPlace 
        ? parseFloat(balance.toFixed(this.decimalPlaces)) 
        : Math.floor(balance);
  
      // Check balance and update if sufficient
      if (balance >= 0 || (positionBalance < 0 && originalValue >= formattedValue)) {
        this.setCellValue(positionId, monthValue.index + 1, monthValue.year, key, formattedValue); 
        
        let data = {
          position_id: positionId,
          rate_per_unit: position.rate_per_unit,
          rate_currency_code: position.rate_currency_code,
          month: monthValue.index + 1,
          year: monthValue.year,
          planned: formattedValue
        };
  
        this.updatedMonthList.push(data);
        event.target.innerText = decimalPlace 
          ? formattedValue.toFixed(this.decimalPlaces) 
          : formattedValue.toString(); // Display without decimals if not allowed
  
        this.updateBalance(positionId, balance);
        
      } else {

        let balanceCheck = _.findWhere(this.formConfig,{
          type: 'resource-loading',
          field_name: 'check_balance',
          is_active: true,
        })

        if(balanceCheck)
        {
          this._toasterService.showWarning('Balance is not Sufficient',10000);
          this.setCellValue(positionId, monthValue.index+1,monthValue.year, key, originalValue);
          event.target.innerText = originalValue.toFixed(this.decimalPlaces);
        }
        else
        {
          this.setCellValue(positionId, monthValue.index+1,monthValue.year, key, formattedValue); 
          event.target.innerText = formattedValue.toFixed(this.decimalPlaces);
          this.updateBalance(positionId,balance); 
        }
      }
    } else {
      // If value is not a number, revert to original value
      const originalValue = this.getCellValue(positionId, monthValue, key);
      this.setCellValue(positionId, monthValue.index + 1, monthValue.year, key, originalValue);
      event.target.innerText = decimalPlace 
        ? originalValue.toFixed(this.decimalPlaces) 
        : originalValue.toString(); // Revert without decimals if not allowed
    }
  }
  

  setCellValue(positionId: any, month: number, year: number, key: string, value: number) {
    const position = this.positionList.find(pos => pos.position_id === positionId);
    if (position) {
      if (!position.monthList) {
        position.monthList = [];
      }
      let monthValue = position.monthList.find(mon => mon.month === month && mon.year === year);
      
      if (monthValue) {
        monthValue.planned = value;
      } else {
        position.monthList.push({
          month: month,
          year: year,
          planned: value,
          actual: null,
          carry_forward: null
        });
      }
    }
  }

  updateBalance(positionId: any,balance) {
    const position = this.positionList.find(pos => pos.position_id === positionId);
    if (position) {
      position.balance = balance;
    }
  }

  calculateNewBalance(positionId: any) {
    const position = this.positionList.find(pos => pos.position_id === positionId);
    
    if (!position) return 0;

      const totalPlannedVsActual = this.monthList.reduce((sum, month) => {
      const value = this.getCellValue(position.position_id, month, 'plannedVsActual');
      return sum + (value ? value : 0);
    }, 0);
    let data = {
      balance : position.balance ?? 0,
      total: totalPlannedVsActual
    }
    return data; 
  }

  restrictInput(event: KeyboardEvent) {
    // const regex = /^[0-9]*\.?[0-9]{0,1}$/;
    const decimalAllowed = (this.resource_config && this.resource_config['allow_decimals'] ? this.resource_config['allow_decimals'] : false) ;
    const decimalPart = decimalAllowed && this.decimalPlaces > 0 ? `{0,${this.decimalPlaces}}` : `{0}`;
    const regexPattern = decimalAllowed ? `^[0-9]*\\.?[0-9]${decimalPart}$` : `^[0-9]*$`;
    const regex = new RegExp(regexPattern);
    const element = event.target as HTMLElement;
    const currentValue = element.innerText;
    const selection = window.getSelection();
  
    if (!selection || !selection.rangeCount) {
        return;
    }
  
    const range = selection.getRangeAt(0);
    const startOffset = range.startOffset;
    const endOffset = range.endOffset;
  
    const textBeforeCursor = currentValue.substring(0, startOffset);
    const textAfterCursor = currentValue.substring(endOffset, currentValue.length);
    const futureValue = textBeforeCursor + event.key + textAfterCursor;
  
    // Allow control keys (backspace, delete, arrows)
    const controlKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'];
    if (controlKeys.includes(event.key)) {
        return;
    }
  
    // Allow only numeric keys and a single decimal point
    const isNumericKey = event.key >= '0' && event.key <= '9';
    const isDecimalPoint = event.key === '.';
    
    if (!decimalAllowed && isDecimalPoint) {
      event.preventDefault();
      return;
    }

    // Prevent multiple decimal points
    if (decimalAllowed && isDecimalPoint && currentValue.includes('.')) {
        event.preventDefault();
        return;
    }
  
    // Prevent more than two decimal places
    const decimalIndex = currentValue.indexOf('.');
    if (decimalIndex !== -1 && decimalAllowed) {
        const decimals = currentValue.substring(decimalIndex + 1);
        
        if (decimals.length >= this.decimalPlaces && (startOffset-1) > decimalIndex) {
            event.preventDefault();
            return;
        }
    }
  
    // Test the future value against the regex
    if (!regex.test(futureValue) && !isNumericKey) {
        event.preventDefault();
    }
  }

  isValidNumber(value: string): boolean {
    // const regex = /^[0-9]*\.?[0-9]{0,1}$/;
    const decimalAllowed = (this.resource_config && this.resource_config['allow_decimals'] ? this.resource_config['allow_decimals'] : false) ;
    const decimalPart = decimalAllowed && this.decimalPlaces > 0 ? `{0,${this.decimalPlaces}}` : `{0}`;
    const regexPattern = decimalAllowed ? `^[0-9]*\\.?[0-9]${decimalPart}$` : `^[0-9]*$`;
    const regex = new RegExp(regexPattern);
    return regex.test(value);
  }

  handlePaste(event: ClipboardEvent) {
    event.preventDefault();
    const clipboardData = event.clipboardData || (window as any).clipboardData;
    const pastedText = clipboardData.getData('text');

    if (this.isValidNumber(pastedText)) {
      const inputElement = event.target as HTMLElement;
      document.execCommand('insertText', false, pastedText);
    } else {
      const decimalMessage = (this.resource_config && this.resource_config['allow_decimals'] ? this.resource_config['allow_decimals'] : false) ? ` with ${this.decimalPlaces} decimal places` : '';
      alert(`Invalid input! Please paste a valid number${decimalMessage}.`);
    }
  }

  /**
   * @description Fetching Project Details
   * @returns 
   */
  getProjectDetails() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getProjectQuote(this.projectId,this.itemId).subscribe(
         (res:any) => {
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
             this.projectData = res['data'][0] ? res['data'][0] : null;
          } 
          else{
            this.projectData = null;
          }
          resolve(true)
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Project Details');
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  /**
   * @description changing the carry Forward dialog Data
  */
  selectcarryForward(position_id) {
    this.carryForwardList = this.positionList.filter((item) => item.position_id === position_id);
    if (this.carryForwardList.length > 0) {
      this.carryForwardList = this.carryForwardList[0];
    }
    else{
      this.carryForwardList = null;
    }
    
  }

  onBlur(event: any, positionId: string, month: AnyMxRecord, field: string): void {
    this.updateCellValue(event, positionId, month, field);
  }

  // This function is triggered when the user presses enter
  onEnter(event: any, positionId: string, month: any, field: string): void {
    this.updateCellValue(event, positionId, month, field);
  }

  /**
   * @description Fetching the Quote Details
   * @returns 
   */
  getQuoteDetailsForResourceLoading() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getQuoteDetailsForResourceLoading(this.projectId,this.itemId,this.quote_id, this.resource_type_id).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
             this.quoteData = res['data'][0] ? res['data'][0] : null;
          } 
          else{
            this.quoteData = null;
          }
          resolve(true)
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Quote Details');
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  /**
   * @description Fetching the Resource Loading  Details
   * @returns 
   */
  getResourceLoadingData() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getResourceLoadingData(this.projectId,this.itemId,this.quote_id, this.resource_type_id, this.deliverable_id, this.monthList,this.decimalPlaces).subscribe(
         (res:any) => {
          
          if(res && res['messType']=='I')
          {
              this.initialLoading = true;

          }

          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) 
          {
            this.initialLoading=false;
             resolve(res['data'])
          } 
          else{
            resolve([]);
          }
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Position Details');
          console.log(err);
          resolve([]);
        }
      );
    });
  }

  /**
   * @description Saving the Resource Loading  Details
   * @returns 
   */
  getSaveResourceLoadingData(data) {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getSaveResourceLoadingData(data).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S') {
            // this._toasterService.showSuccess('Saved Successfully!',10000);
             resolve(true)
          } 
          else{
            this._toasterService.showError('Error in Saving Details');
            resolve(false);
          }
        },
        (err) => {
          this._toasterService.showError('Error in Saving Details');
          console.log(err);
          resolve(false);
        }
      );
    });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }
  /**
   * @description For Calculating Dynamic Height
   */
  calculateDynamicStyle(){
    const offsetHeight = (this.plannedViewEnabled || (this.serviceData && !this.serviceData?.billing_plan_edit)) ? 282 : 220;
    
    let cardHeight = window.innerHeight - offsetHeight + 'px'
    let pageHeight = window.innerHeight - 60 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicResourceTableHeight',
      (this.height ? this.height :  cardHeight)
    )
    document.documentElement.style.setProperty(
      '--dynamicResourcePageHeight',
      pageHeight
    )
  }

  /**
   * @description For Calculating Month Wise Planned, Actual
   */
  getMonthWiseData(month: number, year: number, type: string, isBill:false): number {
    let total = 0;
    let filteredPositionList = _.filter(this.positionList, (item) => {
      //Commenting IsBill Status
      // if (isBill) {
      //     return item.resource_type != 1;
      // } else {
      //     return item.resource_type == 1;
      // }
      return item
    });

    total = filteredPositionList.reduce((acc, item) => {
      // If monthList is null or undefined, skip this item
      if (!item['monthList']) {
          return acc;
      }
  
      // Find the value for the specific month and year
      let monthData = item['monthList'].find(value => value['month'] === month && value['year'] === year);
      if (monthData) {
          if (type === 'planned') {
              acc += monthData['planned'] || 0;  // Ensure 'planned' value is not undefined
          } else if (type === 'actual') {
              acc += monthData['actual'] || 0;  // Ensure 'actual' value is not undefined
          }
      }
  
      return acc;
    }, 0);

    // Iterate over each position in the positionList
    // for (let item of filteredPositionList) {
    //     // If monthList is null or undefined, continue to the next item
    //     if (!item['monthList']) {
    //         continue;
    //     }
    //     for (let value of item['monthList']) {
    //       if (value['month'] === month && value['year'] === year) {
    //           if (type === 'planned') {
    //               total += value['planned'] || 0;  // Ensure 'planned' value is not undefined
    //           } else if (type === 'actual') {
    //               total += value['actual'] || 0;  // Ensure 'actual' value is not undefined
    //           }
    //           break;  // Break out of the inner loop once the correct month and year are found
    //       }
    //   }
        
    // }

    return total;
}

  /**
   * @description Fetching Initial Quote Load Details
   * @returns 
   */
  getInitialLoadQuoteData() {
    let startDate = this.projectData?.planned_start_date;
    let endDate = this.projectData?.planned_end_date
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getInitialLoadQuoteData(startDate,endDate,this.quote_id).subscribe(
         (res:any) => {
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
             resolve(res['data'])
          } 
          else{
            resolve([])
          }
        },
        (err) => {
          this._toasterService.showError('Error in Importing Quote data');
          console.log(err);
          resolve([]);
        }
      );
    });
  }

  /**
   * @description Formatting the initial load data
   */
  formatPositionQuoteList(data){
    for(let item of this.positionList){

      data = _.where(data,{quote_resource_type: this.resource_type_id})
      for(let quote of data){
        
        if(quote['position'] == item['position_id']){
          item['monthList'] = quote['monthList']
          item['balance'] = quote['balance']
          item['planned_hours'] = quote['total_hrs']
        }
      }
    }
  }

  /**
   * @description Creating Milestone Data
   * @returns 
   */
  createMilestoneData(data,is_all_data) {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.createMilestoneFromResourceLoading(this.projectId,this.itemId,this.quote_id, this.deliverable_id, data,is_all_data).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S') {
            this._toasterService.showSuccess('Milestones Generated Successfully!',10000);
             resolve(true)
          } 
          else{
            this._toasterService.showError('Error in generating Milestones');
            resolve(false);
          }
        },
        (err) => {
          this._toasterService.showError('Error in generating Milestones');
          console.log(err);
          resolve(false);
        }
      );
    });
  }

  /**
   * @description Creating Bills Data
   * @returns 
   */
  createBillsData(data,is_all_data) {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.createBillsFromResourceLoading(this.projectId,this.itemId,this.quote_id, this.resource_type_id, data,is_all_data).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S') {
            this._toasterService.showSuccess('Bills Generated Successfully!',10000);
             resolve(true)
          } 
          else{
            this._toasterService.showError('Error in generating Bills');
            resolve(false);
          }
        },
        (err) => {
          this._toasterService.showError('Error in generating Bills');
          console.log(err);
          resolve(false);
        }
      );
    });
  }

  /**
   * @description Getting Total Quote Balance
   */
  getRemainingBalance(){
    let total_balance = 0;
    for(let position of this.positionList){
      total_balance += (position['balance'] ? position['balance'] : 0)
    }
    return total_balance
  }

  /**
   * @description Milestone Check
   * @returns 
   */
  initialMilestoneCheck() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.initialMileStoneCheck(this.projectId,this.itemId,this.quote_id).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S') {
            let flag = (res['is_milestone_created'] == 1) ? true : false
             resolve(flag)
          } 
          else{
            resolve(false);
          }
        },
        (err) => {
          this._toasterService.showError('Error in Verifying Milestone');
          console.log(err);
          resolve(false);
        }
      );
    });
  }

  /**
   * @description Checking for balance at every position 
   */
  checkPositionBalance(){
    
    if(this.balanceCheck){

      let condition = this.balanceCheck['condition'] ? this.balanceCheck['condition'] : '=='
      let value = this.balanceCheck['value'] ? this.balanceCheck['value'] : 0
      const allowDecimals = this.resource_config && this.resource_config['allow_decimals'] ? this.resource_config['allow_decimals'] : false;

      const formatValue = (number) => {
        return allowDecimals ? parseFloat(number.toFixed(this.decimalPlaces)) : parseFloat(number);
      };

      const evaluateCondition = (balance, condition, value) => {
        
        const formattedBalance = formatValue(balance);
        const formattedValue = formatValue(value);

        switch (condition) {
          case '>':
              return formattedBalance > formattedValue;
          case '>=':
              return formattedBalance >= formattedValue;
          case '<':
              return formattedBalance < formattedValue;
          case '<=':
              return formattedBalance <= formattedValue;
          case '==':
              return formattedBalance == formattedValue;
          case '!=':
              return formattedBalance != formattedValue;
          default:
              throw new Error(`Unsupported condition: ${condition}`);
        }
      };
      
      for (let item of this.positionList) {
        if (!evaluateCondition(item.balance, condition, value)) {
          return false;
        }
      }
  
      return true;
     
    } else {
      return true;
    }
  }

  /**
   * @description For Calculating Milestone Value
   */
  getMilestoneValue(month: number, year: number, type: string, isBill: boolean = false): number {
    return this.positionList
        .filter(item => item.monthList)
        .reduce((total, item) => {
            let monthData = item.monthList.find(value => value.month === month && value.year === year);
            // let per_hour_rate = parseFloat(item?.rate_per_unit.toFixed(this.quote_decimalPlaces)) // Commenting the Per Hour Rate Value
            let per_hour_rate = item?.rate_per_unit || 0
            if (monthData && per_hour_rate) {
                let value = type === 'planned' ? monthData.planned : monthData.actual;
                if (value) {
                    total += value * per_hour_rate;
                }
            }
            return total;
        }, 0);
  }
  /**
   * @description Fetching the Class
   * @param positionId 
   * @param month 
   * @returns 
   */
  getCellClass(positionId: number, month: any): string {
    const value = this.getCellValue(positionId, month, 'carryForward');
    if (value > 0) {
      return 'negative-class';
    } else if (value < 0) {
      return 'warning-class';
    } else if (value == 0) {
      return 'positive-class';
    }
    return '';
  }

  /**
   * @description For Checking Disabled Or not
   */
  getDisabledStatus(month: number, year: number): boolean {
    let disabled = false;
    
    for (let item of this.positionList) {
        if (!item['monthList']) {
            continue;
        }

        // Find the value for the specific month and year
        for (let value of item['monthList']) {
            if (value['month'] === month && value['year'] === year) {
                if(value['is_disabled']){
                  disabled = true;
                }
                break;  // Break out of the inner loop once the correct month and year are found
            }
        }
        if(disabled){
          break;
        }
    }

    return disabled;
  }

  getVersionData() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getResourceLoadingHeaderVersions(this.projectId,this.itemId,this.quote_id, this.deliverable_id, this.resource_type_id).subscribe(
         async(res:any) => {
          
          if(res && res['messType'] == 'S' && res['data'] && res['data']['data'].length > 0) {
            
             this.statusId = res['data']['data'][0]['status_id']

             await this.masterService.serviceTypeList().then((res)=>{
              this.serviceTypeList = res;
             });

             this.serviceData = _.findWhere(this.serviceTypeList, {
              id: (this.projectData.service_type_id ? this.projectData.service_type_id : null),
             });

             let do_not_consider_forecast = this.serviceData ? this.serviceData.do_not_consider_forecast == 1 ? true: false : false;

             if(do_not_consider_forecast)
                this.isAlreadyForecasted = res['data']['data'][0]['forecast_version_id'] == 0 ? false : true;
              
             console.log("Already Forecasted", this.isAlreadyForecasted, do_not_consider_forecast, this.serviceData, res['data']['data'][0])
             resolve(res['data'])
          } 
          else{
            this.statusId = 1
            resolve([]);
          }
        },
        (err) => {
          this.statusId = 1
          this._toasterService.showError('Error in Fetching Position Details');
          console.log(err);
          resolve([]);
        }
      );
    });
  }

  /**
   * @description Getting Color for balance
   */
  getColor(balance){
    if (balance > 0) {
      return '#fa8c16'; // positive balance
    } else if (balance < 0) {
      return '#FF3A46'; // negative balance
    }
    return 'rgba(0, 0, 0, 0.87)';
  }

  /**
   * @description Updating the Quote Position List
   */
  updatePositionList(){
    for(let item of this.positionList){
      let positionData = _.findWhere(this.resourceTypeConfig, {
        id: item.resource_type
      });
      item['resource_config'] = positionData ? positionData : null
    }
    // console.log('updated positionList:',this.positionList)
  }

  checkWriteOffPositionBalance(){

    for (let item of this.positionList) 
    {
        if(item['carryForward']<=0 && item['balance']!=0)
        {
            let negativeBalanceWriteoff= this.retrieveMessages.length>0 ? this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.negativeBalanceWriteoff ? this.retrieveMessages[0].errors.negativeBalanceWriteoff : "Please split the hours to bring the balance to 0 before continuing with the forecast." : "Please split the hours to bring the balance to 0 before continuing with the forecast.": "Please split the hours to bring the balance to 0 before continuing with the forecast."
            this._toasterService.showWarning(negativeBalanceWriteoff, 10000)
            this.loaderObject.releaseLoader = false;
            return false;
        }
        if(item['carryForward']>0 && item['balance']>item['carryForward'])
        {
            let balanceGreaterCarryWriteoff= this.retrieveMessages.length>0 ? this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.balanceGreaterCarryWriteoff ? this.retrieveMessages[0].errors.balanceGreaterCarryWriteoff : "Please adjust the Balance to be less than or equal to the Carryforward value to proceed." : "Please adjust the Balance to be less than or equal to the Carryforward value to proceed.": "Please adjust the Balance to be less than or equal to the Carryforward value to proceed."
            this._toasterService.showWarning(balanceGreaterCarryWriteoff, 10000)
            this.loaderObject.releaseLoader = false;
            return false;
        }
        if(item['carryForward']>0 && item['balance']<0)
        {
            let balanceNegativeWriteoff= this.retrieveMessages.length>0 ? this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.balanceNegativeWriteoff ? this.retrieveMessages[0].errors.balanceNegativeWriteoff : "Please split the hours to bring the balance to 0 before continuing with the forecast." : "Please split the hours to bring the balance to 0 before continuing with the forecast.": "Please split the hours to bring the balance to 0 before continuing with the forecast."
            this._toasterService.showWarning(balanceNegativeWriteoff, 10000)
            this.loaderObject.releaseLoader = false;
            return false;
        }
    }

    return true;
  }

  async selectToggle(event,type){
    console.log(":::::::", type, event)
    if(type.quote_resource_type != this.resource_type_id){
      this.isComponentLoading = true;

      this.resource_type_id = type.quote_resource_type;

      console.log(":::",event, this.resource_type_id)

      let val = _.where(this.resourceTypeConfig,{quote_resource_type: this.resource_type_id})

      console.log("VAL", val)
      if(val.length>0)
      {
         this.resource_type_id = val[0]['quote_resource_type']
         this.resource_config= val[0]
      }

      await this.getQuoteDetailsForResourceLoading();
      this.positionList = await this.getResourceLoadingData();

      if(this.initialLoading){
        let data: any = await this.getInitialLoadQuoteData()
        await this.formatPositionQuoteList(data);
        this.statusId = 1;
  
        for(let d of data)
        {
           d['position_id'] =d['position']
        }
        let data_payload = {
          data: data,
          project_id: this.projectId,
          project_item_id: this.itemId,
          quote_id:this.quote_id,
          version_id: 1,
          status_id: this.statusId,
          action_id:2,
          resource_type_id: this.resource_type_id,
          deliverable_id: this.deliverable_id
        }
        
        await this.getSaveResourceLoadingData(data_payload)
        this.positionList = await this.getResourceLoadingData();
      }
      await this.updateCarryForwardList();
     
      if(this.plannedViewEnabled){
        this.plannedViewEnabled = false;
      }
      this.isComponentLoading = false;
      this.displayForecast.emit()
    }
  }

  resourceTypeConfigQuote() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.resourceTypeConfigQuote(this.itemId,this.quote_id).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
            
             this.resourceTypeConfig = res['data']

             if(this.resourceTypeConfig.length>0)
             {
              this.resource_type_id = this.resourceTypeConfig[0]['quote_resource_type']
              this.resource_config= this.resourceTypeConfig[0]
              console.log("::::",this.resource_config, this.resourceTypeConfig, this.resource_type_id)
             }
             resolve(res['data'])
          } 
          else{
        
            resolve([]);
          }
        },
        (err) => {
     
          this._toasterService.showError('Error in Fetching Position Details');
          console.log(err);
          resolve([]);
        }
      );
    });
  }


}