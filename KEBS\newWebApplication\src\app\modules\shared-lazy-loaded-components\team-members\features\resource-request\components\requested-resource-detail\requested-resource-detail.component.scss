.requested-resource {
  font-family: "Plus Jakarta Sans"; //Zifo theme
  .detailCard {
    height: 100vh;
    box-shadow: 0 0px 8px 0 rgb(0 0 0 / 20%);
  }
  .spacer {
    flex: 1 0 auto;
  }
  .required-star{
    color: #cf0001;
  }
  .status {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 8px;
    gap: 8px;
    width: auto;
    height: 24px;
    // background: #FFBD3D;
    border-radius: 4px;
    flex: none;
    order: 0;
    flex-grow: 0;

    font-style: normal;
    font-weight: 500;
    font-size: 10px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #ffffff;
  }
  .activityButton{
    margin: 4px;
    padding: 4px;
    border-radius: 4px;
    display: inline;
    background-color: white;
    color: #526179;
    border-color: #526179;
    border-width: 1.5px;
    font-style: normal;
    font-weight: 700;
    font-size: 10px;
    line-height: 16px;
    /* padding-top: 13px; */
    margin-top: 18px;
    margin-left: 20px;
  }
  .outlineButton {
    width: 82px;
    height: 21px;
    font-style: normal;
    font-weight: 700;
    font-size: 10px;
    display: flex;
    align-items: center;
    letter-spacing: -0.02em;
    text-transform: capitalize;
    color: #45546e;
    border-radius: 3px;
    border-width: thin;
    background: #ffffff;
    box-shadow: 0px 1px 1px rgb(0 0 0 / 12%), 0px 0px 5px rgb(0 0 0 / 20%);
  }
  .role {
    // width: auto;
    // height: 24px;

    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
    margin-bottom: -3px;
  }
  .subText {
    width: 372px;
    height: 16px;

    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    color: #526179;
  }
  .iconClass {
    font-size: 15px;
    margin-left: -8px;
    margin-top: 2px;
  }
  .moreButton {
    border-width: thin;
    margin-top: 13px;
    box-sizing: border-box;
    /* position: absolute; */
    width: 24px;
    height: 24px;
    left: 0px;
    top: 0px;
    border: 2px solid #b9c0ca;
    border-radius: 8px;
  }
  .colName {
    width: auto;
    // height: 16px;
    height: auto;

    font-style: normal;
    font-weight: 400;
    font-size: 11px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    color: #b9c0ca;
    margin-bottom: -2px;
  }
  .colValue {
    width: auto;
    // height: 16px;
    height: auto;
    font-family: "Plus Jakarta Sans";// Zifo theme
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    /* identical to box height, or 133% */

    letter-spacing: 0.02em;
    text-transform: capitalize;
    display: flex;

    /* Blue Grey/90 */

    color: #526179;

    .overflow-text{
      overflow: hidden;
      max-width: 99%;
      // display: flex;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  hr {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .iconClose {
    color: black;
    font-size: 16px;
  }
  .closeButton {
    background: none;
    border: none;
  }
  .rmg-img {
    width: 24px;
    height: 24px;
    border-radius: 24px;
  }
  .content {
    padding-left: 31px;
  }
  .content-head {
    width: auto;
    height: 16px;
    font-family: "Plus Jakarta Sans";// Zifo theme
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    color: #b9c0ca;
    padding-bottom: 1px;
    margin-bottom: 0px;
    padding-top: 5px;
    margin-bottom: 4px;
  }
  .timeline {
    padding-left: 43px;
    height: 76%;
    width: 100%;
    position: absolute;
    overflow: scroll;

    .soft-booking-count{
      font-family: "Plus Jakarta Sans";// Zifo theme
      display: flex;
      gap: 4px;
      color: #7D838B;
      align-items: center;
      font-size: 12px;
    }
    .count-text{
      color: #377DFF;
      background-color: #E8F4FF;
      font-family: "Plus Jakarta Sans";// Zifo theme
      padding: 1px 6px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .content-item {
    display: block;
    width: auto;
    height: 24px;

    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #526179;
    margin-bottom: -9px;
  }
  .content-subItem {
    width: auto;
    height: 8px;

    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    /* line-height: 8px; */
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #8b95a5;
  }
  .Ocircle {
    left: 30px;
    margin-top: -2px;
    height: 29px;
    width: 27px;
    /* background: white; */
    border-radius: 50%;
    border-width: thick;
    display: flex;
    align-items: center;
    border: 10px solid white;
    position: absolute;
  }
  .circle {
    position: absolute;
    left: -4px;
    margin-top: 0px;
    height: 15px;
    width: 15px;
    background: white;
    border-radius: 50%;
    border-width: thick;
    display: inline-block;
  }
  .line {
    margin-left: 7px;
    position: absolute;
    width: 12px;
    left: 13px;
    height: 27px;
    display: inline-block;
    margin-top: -25px;
    border-left: 1.9px solid #dadce2;
  }
  .title {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    text-transform: capitalize;
    color: #45546e;
    margin-bottom: -5px;
    padding-top: 0px;
    width: 116px;
    font-family: "Plus Jakarta Sans";// Zifo theme
  }
  .sub-title {
    font-style: normal;
    font-weight: 500;
    font-size: 10px;
    line-height: 16px;
    text-transform: capitalize;
    padding-bottom: 15px;
    margin-bottom: 0px;
    font-family: "Plus Jakarta Sans";// Zifo theme
  }
  .circle-item {
    font-size: 11px;
    position: absolute;
    top: 1.9px;
    left: 2px;
    color: #fff;
    font-weight: bold;
  }
  .arrowIcon {
    color: #111434;
    font-size: 19px;
    padding-top: 4px;
    padding-left: 34px;
    cursor: pointer;
  }
  .status_badge {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 8px;
    gap: 8px;
    height: 22px;
    width: auto;
    background: #52c41a;
    border-radius: 4px;
    /* width: 51px; */
    /* height: 16px; */

    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #ffffff;
  }
  .res-img {
    width: 40px;
    height: 40px;
    border-radius: 24px;
    margin-left: -6px;
  }
  .res-item {
    display: block;
    width: auto;
    height: 24px;

    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
  }
  .res-subItem {
    width: 71px;
    height: 16px;

    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    color: #526179;
  }
  .colName {
    width: auto;
    // height: 16px;
    height: auto;
    font-family: "Plus Jakarta Sans";// Zifo theme
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    color: #b9c0ca;
    margin-bottom: 0px;
  }
  .colValue {
    width: auto;
    height: auto;
    // height: 16px;

    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #526179;
    margin-bottom: 0px;
  }
  .OuterCircle {
    padding-left: 18px;
    margin-bottom: 6px;
  }
  .confirmButton {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 8px;
    gap: 8px;
    border-radius: 4px;
    width: auto;
    height: 25px;
    margin-right: 8px;
    background: #52C41A; // Zifo theme
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    align-items: center;
    letter-spacing: -0.02em;
    text-transform: capitalize;
  }
  .circle-item-content {
    font-size: 11px;
    position: absolute;
    top: 2.9px;
    left: 2px;
    color: #fff;
    font-weight: bold;
  }
  .circle-content {
    position: absolute;
    left: 61px;
    margin-top: 1px;
    height: 15px;
    width: 15px;
    background: white;
    border-radius: 50%;
    border-width: thick;
    display: inline-block;
  }
  .colCriteria {
    margin-left: 24px;
    font-family: "Plus Jakarta Sans";// Zifo theme
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    /* identical to box height, or 133% */

    letter-spacing: 0.02em;
    text-transform: capitalize;

    /* Blue Grey/80 */

    color: #5f6c81;
  }
  .cancelButton {
    margin: 4px;
    padding: 4px;
    border-radius: 4px;
    display: inline;
    background-color: white;
    color: #526179;
    border-color: #526179;
    border-width: 1.5px;

    font-style: normal;
    font-weight: 700;
    font-size: 10px;
    line-height: 16px;
  }
  .activityButton{
    margin: 4px;
    padding: 4px;
    border-radius: 4px;
    display: inline;
    background-color: white;
    color: #526179;
    border-color: #526179;
    border-width: 1.5px;
    font-style: normal;
    font-weight: 700;
    font-size: 10px;
    line-height: 16px;
    /* padding-top: 13px; */
    margin-top: 18px;
    margin-left: 20px;
  }

  .notes {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 8px;
    gap: 8px;
    width: 536px;
    min-height: 80px;
    /* Palette/10/Red */
    // background: #FFEBEC;
    background: #f7f9fb;
    /* Palette/100/Red */
    border: 1px solid #ff3a46;
    border-radius: 4px;

    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    /* or 133% */

    /* Black/90 */

    color: #272a47;
    /* Inside auto layout */

    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    margin-top: 8px;
    border: 1px solid #e8e9ee;
    border-radius: 4px;
    margin-bottom: 12px;
  }
  .noteHead {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    /* identical to box height, or 133% */

    letter-spacing: 0.02em;
    text-transform: capitalize;

    /* Black/100 */

    color: #111434;
  }
  .noteContent {
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    max-width: 500px;
    /* or 133% */

    /* Black/90 */

    color: #272a47;
  }
  .itemHead {
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    color: #b9c0ca;
    display: block;
  }
  .itemSub {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #526179;
  }
  .infoOverlay {
    font-size: small;
    color: #b9c0ca;
    padding-top: 1px;
    margin-left: 3px;
    margin-top: 0px;
  }
  .material-symbols-outlined {
    font-variation-settings: "FILL" 0, "wght" 400, "GRAD" 0, "opsz" 48;
  }

  .content1 {
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #526179;
    display: inline-block;
    word-break: break-word;
    width: 98%;
    text-align: justify;
  }

  .report_text{
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #526179;
    display: block;
    padding-right: 7px;
    // margin-top: -12px;
    padding-bottom: 6px;
    font-weight: 500;

    &.header{
      text-transform: uppercase;
      color: #b9c0ca;
      font-size: 11px;
      font-weight: 400;
    }

    &.ellipsis-class{
      width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
  .search-class{
    width: 90%;
    display: flex;
  }
  .align-class{
    // align-items: center;
    font-size: 13px;
    color: #526179;
  }
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper {
    margin: .25em 0;
    width: 90% !important;
  }
}

::ng-deep .spinner-theme circle {
  stroke: #79BA44 !important;
}