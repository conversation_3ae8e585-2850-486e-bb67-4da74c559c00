<div class="container-fluid RequestForm formpage">
  <ng-contianer *ngIf="loaderObject.isComponentLoading">
    <div class="d-flex justify-content-center mt-3">
      <mat-spinner
        class="green-spinner"
        matTooltip="Please wait..."
        diameter="30"
      >
      </mat-spinner>
    </div>
  </ng-contianer>

  <ng-container *ngIf="!loaderObject.isComponentLoading">
    <form
      [formGroup]="contactForm"
      class="formarea"
      (keydown.enter)="$event.preventDefault()"
    >
      <button (click)="returnback()" class="backButton">
        <mat-icon class="arrow_icon_back">keyboard_arrow_left</mat-icon> Back
      </button>

      <p class="btns">
        <!-- <button class="cancelbtn">Cancel</button> -->
        <button class="cancelbtn" mat-stroked-button (click)="returnback()">
          Cancel
        </button>
        <button
          mat-raised-button
          type="submit"
          (click)="onSubmit()"
          [disabled]="disableSubmit"
          class="buttonOn"
        >
          Submit Request &nbsp;
          <mat-icon class="arrow_icon">keyboard_arrow_right</mat-icon>
        </button>
      </p>

      <p class="title">New People Allocation Request</p>
      <label class="sectionLabel" *ngIf="isQuoteDataAvailable"
        >Quote Id :
        {{
          this.quoteData
            ? this.quoteData.quote_id
              ? this.quoteData.quote_id
              : "-"
            : "-"
        }}</label
      >
      <label
        class="sectionLabel"
        *ngIf="isQuoteDataAvailable"
        style="display: block"
        >Quote Position Id :
        {{
          this.quoteData
            ? this.quoteData.quote_position_id
              ? this.quoteData.quote_position_id
              : "-"
            : "-"
        }}</label
      >
      <div class="formBody">
        <!----------------------------------Rate Card Details Section----------------------------------------->
        <ng-container *ngIf="tenantSpecificFeatures.hasRateCardDetails">
          <span style="margin-top: 12px" class="sectionLabel">{{
            rateCardHeader
          }}</span>
          <div class="rate-card" formGroupName="rate_details">
            <div class="row mt-4">
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Position
                  <span
                    class="required-star"
                    *ngIf="tenantSpecificFeatures.hasRateCardDetails"
                  >
                    &nbsp;*</span
                  >
                </label>
                <app-input-search
                  class="inputSearch"
                  [required]="tenantSpecificFeatures.hasRateCardDetails"
                  placeholder="Select Position"
                  [list]="
                    isQuoteDataAvailable
                      ? PositionListMaster
                      : rateCardPositions
                  "
                  style="width: 100%"
                  formControlName="position"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  [disabled]="isQuoteDataAvailable"
                  (change)="handleRateCard($event)"
                >
                </app-input-search>
              </div>
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Currency
                  <!-- <span class="required-star" *ngIf="tenantSpecificFeatures.hasRateCardDetails">
                    &nbsp;*</span> -->
                </label>
                <app-input-search
                  class="inputSearch"
                  placeholder="Select Currency"
                  [list]="currencyListMaster"
                  style="width: 100%"
                  formControlName="currency"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  disabled="true"
                >
                </app-input-search>
              </div>
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Location
                </label>
                <app-input-search
                  class="inputSearch"
                  required="false"
                  placeholder="Select Location"
                  [list]="WorkLocationMaster"
                  style="width: 100%"
                  formControlName="location"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  disabled="true"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row mt-4">
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Entity
                  <!-- <span
                    class="required-star"
                  >
                    &nbsp;*</span
                  > -->
                </label>
                <app-input-search
                  class="inputSearch"
                  placeholder="Select Entity"
                  [list]="EntityListMaster"
                  style="width: 100%"
                  formControlName="entity"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  disabled="true"
                >
                </app-input-search>
              </div>
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Division
                  <!-- <span
                    class="required-star"
                  >
                    &nbsp;*</span
                  > -->
                </label>
                <app-input-search
                  class="inputSearch"
                  placeholder="Select Division"
                  [list]="DivisionRateCardListMaster"
                  style="width: 100%"
                  formControlName="division"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  disabled="true"
                >
                </app-input-search>
              </div>
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Subdivision
                  <!-- <span
                    
                    class="required-star"
                  >
                    &nbsp;*</span
                  > -->
                </label>
                <app-input-search
                  class="inputSearch"
                  placeholder="Select Subdivision"
                  [list]="SubDivisionRateCardListMaster"
                  style="width: 100%"
                  formControlName="subdivision"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  disabled="true"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row mt-4">
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Revenue
                  <!-- <span class="required-star" *ngIf="tenantSpecificFeatures.hasRateCardDetails">
                    &nbsp;*</span> -->
                </label>
                <input
                  matInput
                  class="form-txt-disable"
                  type="number"
                  formControlName="rate_revenue"
                  placeholder="Enter here"
                  [readonly]="true"
                />
              </div>
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Rate Cost
                  <!-- <span class="required-star" *ngIf="tenantSpecificFeatures.hasRateCardDetails">
                    &nbsp;*</span> -->
                </label>
                <input
                  matInput
                  class="form-txt-disable"
                  type="number"
                  formControlName="rate_cost"
                  placeholder="Enter here"
                  [readonly]="true"
                />
              </div>
              <div style="display: inline-block; margin-right: 20px">
                <label
                  class="fieldLabel"
                  style="margin-bottom: 3px; display: block"
                  >Rate Unit
                  <!-- <span class="required-star" *ngIf="tenantSpecificFeatures.hasRateCardDetails">
                    &nbsp;*</span> -->
                </label>
                <app-input-search
                  class="inputSearch"
                  placeholder="Select Rate Unit"
                  [list]="quoteUnitListMaster"
                  style="width: 100%"
                  formControlName="rate_per_unit"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  disabled="true"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row mt-4">
              <div style="display: inline-block; margin-right: 20px">
                <div
                  style="display: inline-block;"
                  *ngIf="fieldConfig?.opportunity_id?.is_active_field && fieldConfig?.opportunity_id?.allowed_service_type?.includes(itemData?.service_type_id)"
                >
                  <label
                    class="fieldLabel"
                    style="margin-bottom: 3px; display: block"
                    for="opportunity_id"
                  >
                    {{
                      fieldConfig?.opportunity_id?.field_label
                        ? fieldConfig?.opportunity_id?.field_label
                        : "Opportunity"
                    }}
                    <span
                      *ngIf="fieldConfig?.opportunity_id?.is_mandatory"
                      class="required-star"
                    >
                      &nbsp;*</span
                    ></label
                  >
                  <div
                    style="display: flex"
                  >
                  <app-input-search
                  class="inputSearch"
                  placeholder="Select Opportunity"
                  [list]="opportunityMaster"
                  style="width: 100%"
                  formControlName="opportunity_id"
                  [disableNone]="true"
                  [hideMatLabel]="true"
                  disabled="false"
                >
                </app-input-search>
                  </div>
                  <div
                    *ngIf="
                      errtxt &&
                      contactForm.get('request_start_date').status == 'INVALID'
                    "
                  >
                  <ng-container *ngTemplateOutlet="datevalue; context: { label: fieldConfig?.request_start_date?.field_label || 'Start Date' }"></ng-container>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
        <!----------------------------------Rate Card Details Section End----------------------------------------->

        
        <p *ngIf="fieldConfig?.need_client_interview?.is_active_field">
          <label class="sectionLabel" style="display: block">
            {{
              fieldConfig?.need_client_interview?.field_label
                ? fieldConfig?.need_client_interview?.field_label
                : "Need Client Interview"
            }}<span
              *ngIf="fieldConfig?.need_client_interview?.is_mandatory"
              class="required-star"
            >
              &nbsp;*</span
            >
          </label>
          <mat-radio-group
            formControlName="need_client_interview"
            class="example-radio-group"
            style="color: #5f6c81"
          >
            <mat-radio-button
              class="example-radio-button"
              [checked]="contactForm.value.need_client_interview == 1"
              value="1"
              style="margin-right: 58px"
            >
              Yes
            </mat-radio-button>
            <mat-radio-button
              class="example-radio-button"
              [checked]="contactForm.value.need_client_interview == 0"
              value="0"
              style="margin-right: 50px"
            >
              No
            </mat-radio-button>
          </mat-radio-group>
        </p>
        <p class="sectionLabel">Assignment Duration</p>
        <div style="display: flex; height: 80px">
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.request_start_date?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="start_date"
            >
              {{
                fieldConfig?.request_start_date?.field_label
                  ? fieldConfig?.request_start_date?.field_label
                  : "Start Date"
              }}
              <span
                *ngIf="fieldConfig?.request_start_date?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              ></label
            >
            <div
              [ngClass]="
                errtxt &&
                contactForm.get('request_start_date').status == 'INVALID'
                  ? 'dateFieldBorderErr'
                  : 'dateFieldBorder'
              "
              style="padding-left: 8px; display: flex"
            >
              <input
                formControlName="request_start_date"
                [required]="fieldConfig?.request_start_date?.is_mandatory"
                matInput
                [matDatepicker]="picker1"
                placeholder="DD MMM YYYY"
                readonly
                style="border: none; outline: none; width: 80%"
                [min]="itemData['start_date']"
                [max]="getenddate ? getenddate : itemData['end_date']"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker1"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker1></mat-datepicker>
            </div>
            <div
              *ngIf="
                errtxt &&
                contactForm.get('request_start_date').status == 'INVALID'
              "
            >
            <ng-container *ngTemplateOutlet="datevalue; context: { label: fieldConfig?.request_start_date?.field_label || 'Start Date' }"></ng-container>
            </div>
          </div>
          <div
            style="display: inline-block; margin-right: 46px"
            *ngIf="fieldConfig?.request_end_date?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="end_date"
              >{{
                fieldConfig?.request_end_date?.field_label
                  ? fieldConfig?.request_end_date?.field_label
                  : "End Date"
              }}<span
                *ngIf="fieldConfig?.request_end_date?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              ></label
            >
            <div
              [ngClass]="
                errtxt &&
                contactForm.get('request_end_date').status == 'INVALID'
                  ? 'dateFieldBorderErr'
                  : 'dateFieldBorder'
              "
              style="padding-left: 8px; display: flex"
            >
              <input
                formControlName="request_end_date"
                [required]="fieldConfig?.request_end_date?.is_mandatory"
                matInput
                [matDatepicker]="picker2"
                placeholder="DD MMM YYYY"
                readonly
                style="border: none; outline: none; width: 80%"
                [min]="getstartdate ? getstartdate : itemData['start_date']"
                [max]="itemData['end_date']"
                (dateChange)="resetClosureDate()"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker2"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker2></mat-datepicker>
            </div>
            <div
              *ngIf="
                errtxt &&
                contactForm.get('request_end_date').status == 'INVALID'
              "
            >
              <ng-container *ngTemplateOutlet="datevalue; context: { label: fieldConfig?.request_end_date?.field_label || 'End Date' }"></ng-container>
            </div>
          </div>
        </div>

        <p style="margin-top: 10px" class="sectionLabel">Job Details</p>
        <div style="display: flex; height: 80px">
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.position?.is_active_field"
          >
            <label class="fieldLabel" style="margin-bottom: 3px; display: block"
              >{{
                fieldConfig?.position?.field_label
                  ? fieldConfig?.position?.field_label
                  : "Position"
              }}
              <span
                *ngIf="fieldConfig?.position?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>

            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.position?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.position?.field_label
                  ? fieldConfig?.position?.field_label
                  : 'Position'
              }}"
              [list]="PositionListMaster"
              style="width: 100%"
              formControlName="position"
              [disableNone]="true"
              [hideMatLabel]="true"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.position?.is_mandatory &&
                errtxt &&
                contactForm.get('position').status == 'INVALID'
              "
            >
              {{
                fieldConfig?.position?.field_label
                  ? fieldConfig?.position?.field_label
                  : "Position"
              }} is required
            </p>
          </div>
          <div
            style="display: inline-block; margin-right: 20px"
            *ngIf="fieldConfig?.no_of_positions?.is_active_field"
          >
            <label class="fieldLabel" style="margin-bottom: 3px; display: block"
              >{{
                fieldConfig?.no_of_positions?.field_label
                  ? fieldConfig?.no_of_positions?.field_label
                  : "No Of Positions"
              }}
              <span
                *ngIf="fieldConfig?.no_of_positions?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>

            <mat-form-field appearance="outline">
              <input
                matInput
                class="capacity"
                placeholder="1-100"
                type="number"
                min="1"
                max="100"
                digitOnly
                (input)="preventNoofPositions($event)"
                formControlName="no_of_positions"
                [required]="fieldConfig?.no_of_positions?.is_mandatory"
              />
              <mat-error
                style="margin-top: 6px"
                *ngIf="fieldConfig?.no_of_positions?.is_mandatory && errtxt"
                >{{fieldConfig?.no_of_positions?.field_label
                  ? fieldConfig?.no_of_positions?.field_label
                  : "No Of Positions"}} is required</mat-error
              >
            </mat-form-field>
          </div>
        </div>
        <div class="row" style="display: flex">
          <!--margin-bottom: -8px;-->
          <p
            style="
              display: inline-block;
              margin-right: 46px;
              margin-bottom: 0px;
            "
            *ngIf="
              fieldConfig?.skill_arr?.array_items[0]?.is_active_field &&
              fieldConfig?.skill_arr?.is_active_field
            "
          >
            <label class="fieldLabel" style="margin-bottom: 3px; display: block"
              >{{
                fieldConfig?.skill?.field_label
                  ? fieldConfig?.skill?.field_label
                  : "Skill"
              }}<span
                *ngIf="
                  fieldConfig?.skill_arr?.array_items[0]?.is_mandatory &&
                  fieldConfig?.skill_arr?.is_mandatory
                "
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
          </p>

          <!-- <p
          [ngClass]="{
            yoe: fieldConfig?.skill_arr?.array_items[0]?.is_active_field
          }"
          style="display: inline-block; margin-right: 20px;margin-left: 158px;"
          *ngIf="
            fieldConfig?.skill_arr?.array_items[1]?.is_active_field &&
            fieldConfig?.skill_arr?.is_active_field
          "
        >
          <label class="fieldLabel" style="margin-bottom: 3px; display: block;"
            >{{
              fieldConfig?.years_of_experience?.field_label
                ? fieldConfig?.years_of_experience?.field_label
                : "Years of Experience"
            }}
            <span
              *ngIf="
                fieldConfig?.skill_arr?.array_items[1]?.is_mandatory &&
                fieldConfig?.skill_arr?.is_mandatory
              "
              class="required-star"
            >
              &nbsp;*</span
            >
          </label>
        </p> -->

          <!-- Expertise -->
          <p
            [ngClass]="{
              exp: fieldConfig?.skill_arr?.array_items[1]?.is_active_field
            }"
            style="display: inline-block; margin-bottom: 0px"
            *ngIf="
              fieldConfig?.skill_arr?.array_items[1]?.is_active_field &&
              fieldConfig?.skill_arr?.is_active_field
            "
          >
            <label class="fieldLabel" style="margin-bottom: 3px; display: block"
              >{{
                fieldConfig?.skill_arr?.array_items[1]?.field_label
                  ? fieldConfig?.skill_arr?.array_items[1]?.field_label
                  : "Expertise"
              }}
              <span
                *ngIf="
                  fieldConfig?.skill_arr?.array_items[1]?.is_mandatory &&
                  fieldConfig?.skill_arr?.is_mandatory
                "
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
          </p>
        </div>
        <ng-container
          *ngIf="
            (fieldConfig?.skill_arr?.array_items[0]?.is_active_field &&
              fieldConfig?.skill_arr?.is_active_field) ||
            (fieldConfig?.skill_arr?.array_items[1]?.is_active_field &&
              fieldConfig?.skill_arr?.is_active_field)
          "
        >
          <div
            style="height: 55px"
            formArrayName="skill_arr"
            *ngFor="
              let item of contactForm.get('skill_arr')['controls'];
              let i = index
            "
          >
            <div [formGroupName]="i" style="display: flex">
              <span class="my-auto indexval">{{ i + 1 }}.</span>
              <div style="display: flex; margin-bottom: -10px">
                <div
                  style="display: inline-block; margin-right: 42px"
                  *ngIf="
                    fieldConfig?.skill_arr?.array_items[0]?.is_active_field &&
                    fieldConfig?.skill_arr?.is_active_field
                  "
                >
                  <app-input-search
                    class="inputSearch"
                    [required]="
                      fieldConfig?.skill_arr?.array_items[0]?.is_mandatory &&
                      fieldConfig?.skill_arr?.is_mandatory
                    "
                    placeholder="Select {{
                      fieldConfig?.skill?.field_label
                        ? fieldConfig?.skill?.field_label
                        : 'Skill'
                    }}"
                    [list]="SkillListMaster"
                    style="width: 100%"
                    formControlName="skill"
                    [disableNone]="true"
                    [hideMatLabel]="true"
                    (keyup)="handleSkillChange(i)"
                    (change)="handleSkillChange(i)"
                  >
                  </app-input-search>
                  <p
                    class="errMessage"
                    *ngIf="
                      fieldConfig?.skill_arr?.is_mandatory &&
                      fieldConfig?.skill_arr?.array_items[0]?.is_mandatory &&
                      errtxt &&
                      contactForm.controls['skill_arr'].controls[i].controls[
                        'skill'
                      ].status == 'INVALID'
                    "
                  >
                    
                    {{
                      fieldConfig?.skill?.field_label
                        ? fieldConfig?.skill?.field_label
                        : "Skill"
                    }} is required
                  </p>
                </div>

                <!-- <p
                style="display: inline-block; margin-right: 20px"
                *ngIf="
                  fieldConfig?.skill_arr?.array_items[1]?.is_active_field &&
                  fieldConfig?.skill_arr?.is_active_field
                "
              >
                <app-input-search
                  class="inputSearch"
                  [required]="
                    fieldConfig?.skill_arr?.array_items[1]?.is_mandatory &&
                    fieldConfig?.skill_arr?.is_mandatory
                  "
                  placeholder="Select One"
                  [list]="SkillExperienceMaster"
                  style="width: 100%"
                  formControlName="years_of_experience"
                >
                </app-input-search>
              </p> -->
                <!-- Expertise Container-->
                <div
                  style="display: inline-block; margin-right: 20px"
                  *ngIf="
                    fieldConfig?.skill_arr?.array_items[1]?.is_active_field &&
                    fieldConfig?.skill_arr?.is_active_field
                  "
                >
                  <app-input-search
                    class="inputSearch"
                    [required]="
                      fieldConfig?.skill_arr?.array_items[1]?.is_mandatory &&
                      fieldConfig?.skill_arr?.is_mandatory
                    "
                    placeholder="Select {{
                      fieldConfig?.skill_arr?.array_items[1]?.field_label
                        ? fieldConfig?.skill_arr?.array_items[1]?.field_label
                        : 'Expertise'
                    }}"
                    [list]="ExpertiseMaster"
                    style="width: 100%"
                    formControlName="expertise"
                    [disableNone]="true"
                    [hideMatLabel]="true"
                  >
                  </app-input-search>
                  <p
                    class="errMessage"
                    *ngIf="
                      fieldConfig?.skill_arr?.array_items[1]?.is_mandatory &&
                      fieldConfig?.skill_arr?.is_mandatory &&
                      errtxt &&
                      contactForm.controls['skill_arr'].controls[i].controls[
                        'expertise'
                      ].status == 'INVALID'
                    "
                  >
                    
                    {{
                      fieldConfig?.skill_arr?.array_items[1]?.field_label
                        ? fieldConfig?.skill_arr?.array_items[1]?.field_label
                        : "Expertise"
                    }} is required
                  </p>
                </div>
                <div
                  style="
                    display: inline-block;
                    color: #5f6c81;
                    margin-left: 10px;
                  "
                >
                  <button
                    mat-icon-button
                    matTooltip="Add Skill"
                    (click)="addItem()"
                  >
                    <mat-icon>add_circle_outline</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    matTooltip="Remove Skill"
                    (click)="removeskills(i)"
                  >
                    <mat-icon>remove_circle_outline</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <label
          class="fieldLabel"
          style="margin-bottom: 3px; display: block; margin-top: 10px"
          *ngIf="fieldConfig?.job_description?.is_active_field"
          >{{
            fieldConfig?.job_description?.field_label
              ? fieldConfig?.job_description?.field_label
              : "Job Description"
          }}
          <span
            *ngIf="fieldConfig?.job_description?.is_mandatory"
            class="required-star"
          >
            &nbsp;*</span
          >
        </label>

        <!-- <mat-form-field appearance="outline" style="width: 650px">
        <textarea
          class="txtArea"
          matInput
          formControlName="job_description"
          placeholder="Enter here"
          [required]="fieldConfig?.job_description?.is_mandatory"
          style="height: 80px"
        ></textarea>

        <mat-error
          style="margin-top: 6px"
          *ngIf="fieldConfig?.job_description?.is_mandatory && errtxt"
          >Enter a value</mat-error>
      </mat-form-field> -->
        <div style="height: 220px">
          <dx-html-editor
            [ngClass]="
              fieldConfig?.job_description?.is_mandatory &&
              errtxt &&
              contactForm.get('job_description').status == 'INVALID'
                ? 'dev-extreme-styles-err'
                : 'dev-extreme-styles'
            "
            formControlName="job_description"
            [placeholder]="fieldConfig?.job_description?.field_label 
               ? fieldConfig?.job_description?.field_label 
               : 'Job Description here'"
          >
            <dxo-toolbar>
              <dxi-item name="bold"></dxi-item>
              <dxi-item name="italic"></dxi-item>
              <dxi-item name="strike"></dxi-item>
              <dxi-item name="underline"></dxi-item>
              <dxi-item name="separator"></dxi-item>
              <dxi-item name="alignLeft"></dxi-item>
              <dxi-item name="alignCenter"></dxi-item>
              <dxi-item name="alignRight"></dxi-item>
              <dxi-item name="alignJustify"></dxi-item>
              <dxi-item name="separator"></dxi-item>
              <dxi-item name="orderedList"></dxi-item>
              <dxi-item name="bulletList"></dxi-item>
            </dxo-toolbar>
          </dx-html-editor>
          <p
            class="errMessage"
            style="margin-bottom: 20px"
            *ngIf="
              fieldConfig?.job_description?.is_mandatory &&
              errtxt &&
              contactForm.get('job_description').status == 'INVALID'
            "
          >
            
            {{
              fieldConfig?.job_description?.field_label
                ? fieldConfig?.job_description?.field_label
                : "Job Description"
            }} is required
          </p>
        </div>
        <label
          class="fieldLabel"
          style="margin-bottom: 3px; display: block"
          *ngIf="
            fieldConfig?.criteria?.is_active_field &&
            fieldConfig?.criteria?.array_items[0]?.is_active_field
          "
          >{{
            fieldConfig?.criteria?.field_label
              ? fieldConfig?.criteria?.field_label
              : "Acceptance Criteria"
          }}
          <span
            *ngIf="
              fieldConfig?.criteria?.is_mandatory &&
              fieldConfig?.criteria?.array_items[0]?.is_mandatory
            "
            class="required-star"
          >
            &nbsp;*</span
          >
        </label>

        <ng-container
          *ngIf="
            fieldConfig?.criteria?.is_active_field &&
            fieldConfig?.criteria?.array_items[0]?.is_active_field
          "
        >
          <div
            formArrayName="acceptance_criteria_arr"
            *ngFor="
              let item of contactForm.get('acceptance_criteria_arr')[
                'controls'
              ];
              let i = index
            "
          >
            <div [formGroupName]="i" class="row">
              <span class="my-auto indexval">{{ i + 1 }}.</span>
              <mat-form-field appearance="outline" class="criteriafield">
                <textarea
                  class="txtArea"
                  matInput
                  formControlName="criteria"
                  placeholder="Type your acceptance Checklist"
                  [required]="
                    fieldConfig?.criteria?.is_mandatory &&
                    fieldConfig?.criteria?.array_items[0]?.is_mandatory
                  "
                ></textarea>
                <mat-error
                  class="matErr"
                  *ngIf="
                    (fieldConfig?.criteria?.is_mandatory &&
                      fieldConfig?.criteria?.array_items[0]?.is_mandatory &&
                      errtxt) ||
                    contactForm.get('acceptance_criteria_arr').status ==
                      'INVALID'
                  "
                  >{{fieldConfig?.acceptance_criteria_arr?.field_label || 'Acceptance Criteria' }} is required with No space at Start and End</mat-error
                >
                <mat-error
                  class="matErr"
                  *ngIf="
                    contactForm.get('acceptance_criteria_arr').status ==
                      'INVALID' && !fieldConfig?.criteria?.is_mandatory
                  "
                  >{{fieldConfig?.acceptance_criteria_arr?.field_label || 'Acceptance Criteria' }} is required with No space at Start and End</mat-error
                >
              </mat-form-field>
              <div class="addsubIcons">
                <button
                  mat-icon-button
                  matTooltip="Add Criteria"
                  (click)="
                    addAcceptanceCriteria(); criteriaNoWhiteSpaceValidator()
                  "
                >
                  <mat-icon>add_circle_outline</mat-icon>
                </button>
                <button
                  mat-icon-button
                  matTooltip="Remove Criteria"
                  (click)="removeAcceptanceCriteria(i)"
                >
                  <mat-icon>remove_circle_outline</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </ng-container>
        <p class="sectionLabel mt-4">Delivery Organization</p>
        <div style="display: block">
          <div style="display: flex">
            <p
              style="display: inline-block; margin-right: 20px"
              *ngIf="fieldConfig?.region?.is_active_field"
            >
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                for="email"
                >{{
                  fieldConfig?.region?.field_label
                    ? fieldConfig?.region?.field_label
                    : "Region"
                }}
                <span
                  *ngIf="fieldConfig?.region?.is_mandatory"
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <!-- <span class="orgValue">{{ orgDetails.subdivision }}</span> -->
              <app-input-search
                class="inputSearch"
                [required]="fieldConfig?.region?.is_mandatory"
                placeholder="Select {{
                  fieldConfig?.region?.field_label
                    ? fieldConfig?.region?.field_label
                    : 'Region'
                }}"
                [list]="regionList"
                style="width: 89%; display: flex"
                formControlName="region"
                [disableNone]="true"
                [hideMatLabel]="true"
                (change)="getrmofficerstatus()"
              >
              </app-input-search>
              <mat-error
                class="errMessage"
                *ngIf="fieldConfig?.region?.is_mandatory && errtxt && contactForm.get('region').status == 'INVALID'"
                >{{ fieldConfig?.region?.field_label
                  ?  fieldConfig?.region?.field_label
                  : "Region"}} is required</mat-error
              >
            </p>
            <p
              style="display: inline-block; margin-right: 20px"
              *ngIf="RmFieldconfig?.isEntityVisible"
            >
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                for="firstname"
                >{{
                  fieldConfig?.entity?.field_label
                    ? fieldConfig?.entity?.field_label
                    : "Entity"
                }}
                <span
                  *ngIf="fieldConfig?.entity?.is_mandatory"
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <span class="orgValue">{{ orgDetails.entity }}</span>
              <!-- <app-input-search
              class="inputSearch"
              required="true"
              placeholder="Select One"
              [list]="EntityListMaster"
              style="width: 100%"
              formControlName="entity"
            >
            </app-input-search> -->
            </p>
            <p style="display: inline-block; margin-right: 20px">
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                for="email"
                >{{
                  fieldConfig?.division?.field_label
                    ? fieldConfig?.division?.field_label
                    : "Division"
                }}
                <span
                  *ngIf="fieldConfig?.division?.is_mandatory"
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <span class="orgValue">{{ orgDetails.division }}</span>
              <!-- <app-input-search
              class="inputSearch"
              required="true"
              placeholder="Select One"
              [list]="DivisionListMaster"
              style="width: 100%"
              formControlName="division"
            >
            </app-input-search> -->
            </p>
            <p style="display: inline-block; margin-right: 20px">
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                for="email"
                >{{
                  fieldConfig?.subdivision?.field_label
                    ? fieldConfig?.subdivision?.field_label
                    : "Subdivision"
                }}
                <span
                  *ngIf="fieldConfig?.subdivision?.is_mandatory"
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <span class="orgValue">{{ orgDetails.subdivision }}</span>
              <!-- <app-input-search
              class="inputSearch"
              required="true"
              placeholder="Select One"
              [list]="SubDivisionListMaster"
              style="width: 100%"
              formControlName="subdivision"
              (change)="getrmofficerstatus()"
            >
            </app-input-search> -->
            </p>
          </div>
          <div class="col-12 pl-0 pb-2" *ngIf="enableOrgDetails()">
            <ng-container
              *ngIf="
                getSubdivisionValue && getDivisionValue && getEntityValue;
                else addDetails
              "
            >
              <span
                class="add_new_color"
                (click)="addOrganisation('edit')"
                style="cursor: pointer"
                >+ Edit Organisation Details</span
              >
            </ng-container>
            <ng-template #addDetails>
              <span
                class="add_new_color"
                (click)="addOrganisation('create')"
                style="cursor: pointer"
                >+ Edit Organisation Details</span
              >
            </ng-template>
            <!-- <span
              class="add_new_color"
              *ngIf="!getSubdivisionValue && !getDivisionValue && !getEntityValue"
              (click)="addOrganisation('create')"
              style="cursor: pointer"
              >+ Edit Organisation Details</span
            > -->
            <!-- <span class="add_new_color" *ngIf="businessUnitAdded"  style="cursor:pointer;"> {{('addBusinessDivisionLabel' | checkLabel : this.formConfig: 'isa': 'Business Division')}}</span>  -->
            <!-- <span
              class="add_new_color"
              *ngIf="getSubdivisionValue && getDivisionValue && getEntityValue"
              (click)="addOrganisation('edit')"
              style="cursor: pointer"
              >+ Edit Organisation Details</span> -->
            <!-- <span  *ngIf="businessUnitAdded && ('edit_region' | checkActive : this.formConfig: 'isa')" class="pl-2" (click)="addBusinessEntity('edit')" style="cursor: pointer;">
            <mat-icon class="smallCardIcon">edit</mat-icon>
            </span> -->
            <!-- <span  *ngIf="businessUnitAdded && ('edit_region' | checkActive : this.formConfig: 'isa')" class="pl-2" (click)="clearBusinessEntity()" style="cursor: pointer;">
            <mat-icon class="smallCardIcon">close</mat-icon>
            </span> -->
          </div>
          <div
            style="display: block"
            *ngIf="fieldConfig?.resource_manager?.is_active_field"
          >
            <p style="display: block; margin-bottom: 0px">
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                >{{
                  fieldConfig?.resource_manager?.field_label
                    ? fieldConfig?.resource_manager?.field_label
                    : "Allocation Coordinator"
                }}
                <span
                  *ngIf="fieldConfig?.resource_manager?.is_mandatory"
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
            </p>
            <span
              [ngClass]="rmofficerallocated ? 'rmofficer' : 'underscoreval'"
              [matTooltip]="rmofficerallocated ? rmofficer_name : '-'"
              >{{ rmofficerallocated ? rmofficer_name : "-" }}</span
            >
          </div>
          <br />
          <div
            *ngIf="showcard"
            [ngClass]="rmofficerallocated ? 'successinfo' : 'warninginfo'"
          >
            <div *ngIf="rmofficerallocated" class="icndiv">
              <mat-icon class="icn">done</mat-icon>
              Allocation Coordinator is mapped for the above org combination.
            </div>
            <div *ngIf="!rmofficerallocated" class="icndiv">
              <mat-icon class="icn" style="color: #ff3a46">info</mat-icon>
              Allocation Coordinator is not mapped for the above org
              combination.
            </div>
          </div>
          <div
            *ngIf="
              contactForm.get('entity').status == 'INVALID' &&
              contactForm.get('division').status == 'INVALID' &&
              contactForm.get('subdivision').status == 'INVALID' &&
              !rmofficerallocated &&
              errtxt
            "
            class="warninginfo"
          >
            <div class="icndiv">
              <mat-icon class="icn" style="color: #ff3a46">info</mat-icon>
              Org combination is not selected.
            </div>
          </div>
        </div>

        <p class="sectionLabel" style="margin-top: 10px">Basic Details</p>
        <div style="display: flex; height: 85px">
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.project_role?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="firstname"
              >{{
                fieldConfig?.project_role?.field_label
                  ? fieldConfig?.project_role?.field_label
                  : "Project Role"
              }}<span
                *ngIf="fieldConfig?.project_role?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.project_role?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.project_role?.field_label
                  ? fieldConfig?.project_role?.field_label
                  : 'Project Role'
              }}"
              [list]="ProjectRoleMaster"
              style="width: 100%"
              formControlName="project_role"
              [disableNone]="true"
              [hideMatLabel]="true"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.project_role?.is_mandatory &&
                errtxt &&
                contactForm.get('project_role').status == 'INVALID'
              "
            >
              {{
                fieldConfig?.project_role?.field_label
                  ? fieldConfig?.project_role?.field_label
                  : "Project Role"
              }} is required
            </p>
          </div>
          <p
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.utilization_capacity?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="lastname"
              >{{
                fieldConfig?.utilization_capacity?.field_label
                  ? fieldConfig?.utilization_capacity?.field_label
                  : "Utilization capacity"
              }}<span
                *ngIf="fieldConfig?.utilization_capacity?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <mat-form-field appearance="outline">
              <input
                matInput
                class="capacity"
                placeholder=""
                type="number"
                min="1"
                max="100"
                digitOnly
                (input)="preventDefaultCapacity($event)"
                formControlName="utilization_capacity"
                [required]="fieldConfig?.utilization_capacity?.is_mandatory"
              />
              <mat-error
                style="margin-top: 6px"
                *ngIf="
                  fieldConfig?.utilization_capacity?.is_mandatory && errtxt
                "
                >{{
                  fieldConfig?.utilization_capacity?.field_label
                    ? fieldConfig?.utilization_capacity?.field_label
                    : "Utilization capacity"
                }} is required</mat-error
              >
            </mat-form-field>
          </p>
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.customer?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="customer"
              >{{
                fieldConfig?.customer?.field_label
                  ? fieldConfig?.customer?.field_label
                  : "Customer"
              }}<span
                *ngIf="fieldConfig?.customer?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.customer?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.customer?.field_label
                  ? fieldConfig?.customer?.field_label
                  : 'Customer'
              }}"
              [list]="customerRoleMaster"
              style="width: 100%"
              formControlName="customer"
              [disableNone]="true"
              [hideMatLabel]="true"
              disabled="{{fieldConfig?.customer?.is_disabled ? fieldConfig?.customer?.is_disabled : false}}"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.customer?.is_mandatory &&
                errtxt &&
                contactForm.get('customer').status == 'INVALID'
              "
            >
              {{
                fieldConfig?.customer?.field_label
                  ? fieldConfig?.customer?.field_label
                  : "Customer"
              }} is required
            </p>
          </div>
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.work_experience?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="email"
              >{{
                fieldConfig?.work_experience?.field_label
                  ? fieldConfig?.work_experience?.field_label
                  : "Work Experience"
              }}
              <span
                *ngIf="fieldConfig?.work_experience?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.work_experience?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.work_experience?.field_label
                  ? fieldConfig?.work_experience?.field_label
                  : 'Work Experience'
              }}"
              [list]="WorkExperienceMaster"
              style="width: 100%"
              formControlName="work_experience"
              [disableNone]="true"
              [hideMatLabel]="true"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.work_experience?.is_mandatory &&
                errtxt &&
                contactForm.get('work_experience').status == 'INVALID'
              "
            >
              
              {{
                fieldConfig?.work_experience?.field_label
                  ? fieldConfig?.work_experience?.field_label
                  : "Work Experience"
              }} is required
            </p>
          </div>
        </div>
        <div style="display: flex; height: 85px">
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.nationality?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="firstname"
              >{{
                fieldConfig?.nationality?.field_label
                  ? fieldConfig?.nationality?.field_label
                  : "Nationality"
              }}<span
                *ngIf="fieldConfig?.nationality?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.nationality?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.nationality?.field_label
                  ? fieldConfig?.nationality?.field_label
                  : 'Nationality'
              }}"
              [list]="NationalityListMaster"
              style="width: 100%"
              formControlName="nationality"
              [disableNone]="true"
              [hideMatLabel]="true"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.nationality?.is_mandatory &&
                errtxt &&
                contactForm.get('nationality').status == 'INVALID'
              "
            >
              
              {{
                fieldConfig?.nationality?.field_label
                  ? fieldConfig?.nationality?.field_label
                  : "Nationality"
              }} is required
            </p>
          </div>
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.employment_type?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="lastname"
              >{{
                fieldConfig?.employment_type?.field_label
                  ? fieldConfig?.employment_type?.field_label
                  : "Employment Type"
              }}<span
                *ngIf="fieldConfig?.employment_type?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.employment_type?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.employment_type?.field_label
                  ? fieldConfig?.employment_type?.field_label
                  : 'Employment Type'
              }}"
              [list]="EmploymentTypeMaster"
              style="width: 100%"
              formControlName="employment_type"
              [disableNone]="true"
              [hideMatLabel]="true"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.employment_type?.is_mandatory &&
                errtxt &&
                contactForm.get('employment_type').status == 'INVALID'
              "
            >
              
              {{
                fieldConfig?.employment_type?.field_label
                  ? fieldConfig?.employment_type?.field_label
                  : "Employment Type"
              }} is required
            </p>
          </div>
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.request_context?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="email"
              >{{
                fieldConfig?.request_context?.field_label
                  ? fieldConfig?.request_context?.field_label
                  : "Request Context"
              }}<span
                *ngIf="fieldConfig?.request_context?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.request_context?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.request_context?.field_label
                  ? fieldConfig?.request_context?.field_label
                  : 'Request Context'
              }}"
              [list]="RequestContextMaster"
              disabled="true"
              style="width: 100%"
              formControlName="request_context"
              [disableNone]="true"
              [hideMatLabel]="true"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.request_context?.is_mandatory &&
                errtxt &&
                contactForm.get('request_context').status == 'INVALID'
              "
            >
              
              {{
                fieldConfig?.request_context?.field_label
                  ? fieldConfig?.request_context?.field_label
                  : "Request Context"
              }} is required
            </p>
          </div>
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.request_type?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="email"
              >{{
                fieldConfig?.request_type?.field_label
                  ? fieldConfig?.request_type?.field_label
                  : "Request Context"
              }}<span
                *ngIf="fieldConfig?.request_type?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.request_type?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.request_type?.field_label
                  ? fieldConfig?.request_type?.field_label
                  : 'Request Context'
              }}"
              [list]="requestTypeMaster"
              style="width: 100%"
              formControlName="request_type"
              [disableNone]="true"
              [hideMatLabel]="true"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.request_type?.is_mandatory &&
                errtxt &&
                contactForm.get('request_type').status == 'INVALID'
              "
            >
              
              {{
                fieldConfig?.request_type?.field_label
                  ? fieldConfig?.request_type?.field_label
                  : "Request Context"
              }} is required
            </p>
          </div>
          <div
              style="display: inline-block; margin-right: 0px;"
              *ngIf="fieldConfig?.work_location?.is_active_field"
            >
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                >{{
                  fieldConfig?.work_location?.field_label
                    ? fieldConfig?.work_location?.field_label
                    : "Work Location"
                }}<span
                  *ngIf="fieldConfig?.work_location?.is_mandatory"
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <app-input-search
                class="worklocationfield"
                [required]="fieldConfig?.work_location?.is_mandatory"
                placeholder="Select {{
                  fieldConfig?.work_location?.field_label
                    ? fieldConfig?.work_location?.field_label
                    : 'Work Location'
                }}"
                [list]="WorkLocationMaster"
                formControlName="work_location"
                [disableNone]="true"
                [hideMatLabel]="true"
              >
              </app-input-search>
              <p
                class="errMessage"
                *ngIf="
                  fieldConfig?.work_location?.is_mandatory &&
                  errtxt &&
                  contactForm.get('work_location').status == 'INVALID'
                "
              >
                
                {{
                  fieldConfig?.work_location?.field_label
                    ? fieldConfig?.work_location?.field_label
                    : "Work Location"
                }} is required
              </p>
            </div>
        </div>
        <div class="row">
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.expected_closure_date?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="display: block; margin-bottom: 3px"
              for="expected_closure_date"
              >{{
                fieldConfig?.expected_closure_date?.field_label
                  ? fieldConfig?.expected_closure_date?.field_label
                  : "Expected Closure Date"
              }}<span
                *ngIf="fieldConfig?.expected_closure_date?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              ></label
            >
            <div
              [ngClass]="
                errtxt &&
                contactForm.get('expected_closure_date').status == 'INVALID' &&
                fieldConfig?.expected_closure_date?.is_mandatory
                  ? 'dateFieldBorderErr'
                  : 'dateFieldBorder'
              "
              style="width: 200px; padding-left: 8px; display: flex"
            >
              <input
                formControlName="expected_closure_date"
                [required]="fieldConfig?.expected_closure_date?.is_mandatory"
                matInput
                [matDatepicker]="picker"
                placeholder="DD MMM YYYY"
                style="border: none; outline: none; width: 80%"
                readonly
                [max]="getenddate"
                [min]="fieldConfig?.expected_closure_date?.disable_past_date ? (current_date) : null"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </div>
            <div
              *ngIf="
                errtxt &&
                contactForm.get('expected_closure_date').status == 'INVALID'
              "
            >
              <ng-container *ngTemplateOutlet="datevalue; context: { label: fieldConfig?.expected_closure_date?.field_label || 'Expected Closure Date' }"></ng-container>
            </div>
          </div>
          <div
            style="display: inline-block; margin-right: 42px"
            *ngIf="fieldConfig?.operation_model?.is_active_field"
          >
            <label class="fieldLabel" style="display: block; margin-bottom: 3px"
              >{{
                fieldConfig?.operation_model?.field_label
                  ? fieldConfig?.operation_model?.field_label
                  : "Operation Model"
              }}<span
                *ngIf="fieldConfig?.operation_model?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              ></label
            >
            <div class="row dateFieldBorder togglefield" style="width: 100%">
              <mat-button-toggle-group
                class="toggleborder"
                *ngFor="let item of OperationModelMaster"
                #operationmodetoggleBtn="matButtonToggleGroup"
              >
                <mat-button-toggle
                  value="item.id"
                  [ngClass]="{
                    changetoRed: operationModelFormValue === item.id,
                    changetowhite: operationModelFormValue != item.id
                  }"
                  (click)="OperationModelToggle(item.id)"
                  (change)="handleOperationModelChange()"
                >
                  {{ item.name }}
                </mat-button-toggle>
              </mat-button-toggle-group>
            </div>
          </div>
          <div
            style="display: inline-block"
            *ngIf="fieldConfig?.booking_type?.is_active_field"
          >
            <label class="fieldLabel" style="display: block; margin-bottom: 3px"
              >{{
                fieldConfig?.booking_type?.field_label
                  ? fieldConfig?.booking_type?.field_label
                  : "Booking Type"
              }}<span
                *ngIf="fieldConfig?.booking_type?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              ></label
            >
            <div class="row dateFieldBorder togglefield" style="width: 100%">
              <mat-button-toggle-group
                class="toggleborder"
                #bookingtypetoggleBtn="matButtonToggleGroup"
                [disabled]="isBookingTypeDisabled(fieldConfig?.booking_type?.is_disabled)"
                (change)="bookingType(bookingtypetoggleBtn.value)"
              >
                <mat-button-toggle
                  *ngFor="let item of BookingTypeMaster"
                  [value]="item.id"
                  [ngClass]="{
                    changetoRed: bookingTypeFormValue === item.id,
                    changetowhite: bookingTypeFormValue !== item.id
                  }"
                >
                  {{ item.booking_type }}
                </mat-button-toggle>
              </mat-button-toggle-group>
            </div>
          </div>
        </div>
        <div class="row" style="height: 85px">
          <div
            style="display: inline-block; margin-right: 42px;margin-top: 18px;"
            *ngIf="fieldConfig?.priority?.is_active_field"
          >
            <label
              class="fieldLabel"
              style="margin-bottom: 3px; display: block"
              for="priority"
              >{{
                fieldConfig?.priority?.field_label
                  ? fieldConfig?.priority?.field_label
                  : "Priority"
              }}<span
                *ngIf="fieldConfig?.priority?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              >
            </label>
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.priority?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.priority?.field_label
                  ? fieldConfig?.priority?.field_label
                  : 'Priority'
              }}"
              [list]="PriorityListMaster"
              style="width: 100%"
              formControlName="priority"
              [disableNone]="true"
              [hideMatLabel]="true"
              [disabled]="fieldConfig?.priority?.is_disabled"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.priority?.is_mandatory &&
                errtxt &&
                contactForm.get('priority').status == 'INVALID'
              "
            >
              
              {{
                fieldConfig?.priority?.field_label
                  ? fieldConfig?.priority?.field_label
                  : "Priority"
              }} is required
            </p>
          </div>
          <div
            style="display: inline-block; margin-top: 18px; margin-right: 42px"
            *ngIf="fieldConfig?.commercial?.is_active_field"
          >
            <label class="fieldLabel" style="display: block; margin-bottom: 3px"
              >{{
                fieldConfig?.commercial?.field_label
                  ? fieldConfig?.commercial?.field_label
                  : "Commercial"
              }}<span
                *ngIf="fieldConfig?.commercial?.is_mandatory"
                class="required-star"
              >
                &nbsp;*</span
              ></label
            >
            <app-input-search
              class="inputSearch"
              [required]="fieldConfig?.commercial?.is_mandatory"
              placeholder="Select {{
                fieldConfig?.commercial?.field_label
                  ? fieldConfig?.commercial?.field_label
                  : 'Commercial'
              }}"
              [list]="CommercialListMaster"
              style="width: 100%"
              formControlName="commercial"
              [disableNone]="true"
              [hideMatLabel]="true"
              [disabled]="fieldConfig?.commercial?.is_disabled"
            >
            </app-input-search>
            <p
              class="errMessage"
              *ngIf="
                fieldConfig?.commercial?.is_mandatory &&
                errtxt &&
                contactForm.get('commercial').status == 'INVALID'
              "
            >
              
              {{
                fieldConfig?.commercial?.field_label
                  ? fieldConfig?.commercial?.field_label
                  : "Commercial"
              }} is required
            </p>
          </div>
          <div
            style="
              display: inline-block;
              margin-left: 20px;
              margin-top: 3px;
              margin-right: 30px;
            "
            *ngIf="
              isCommercialIdentity &&
              fieldConfig?.project_commercial_identity?.is_active_field
            "
          >
            <div
              style="display: inline-block; margin-right: 0px; margin-top: 12px"
            >
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                >{{
                  fieldConfig?.project_commercial_identity?.field_label
                    ? fieldConfig?.project_commercial_identity?.field_label
                    : "Identity"
                }}<span
                  *ngIf="
                    fieldConfig?.project_commercial_identity?.is_mandatory &&
                    isCommercialIdentity
                  "
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <app-input-search
                class="worklocationfield"
                [required]="
                  fieldConfig?.project_commercial_identity?.is_mandatory &&
                  isCommercialIdentity
                "
                placeholder="Select {{
                  fieldConfig?.project_commercial_identity?.field_label
                    ? fieldConfig?.project_commercial_identity?.field_label
                    : 'Identity'
                }}"
                [list]="projectIdentityList"
                formControlName="project_commercial_identity"
                [disableNone]="true"
                [hideMatLabel]="true"
              >
              </app-input-search>
              <p
                class="errMessage"
                *ngIf="
                  fieldConfig?.project_commercial_identity?.is_mandatory &&
                  errtxt &&
                  contactForm.get('project_commercial_identity').status ==
                    'INVALID'
                "
              >
                
                {{
                  fieldConfig?.project_commercial_identity?.field_label
                    ? fieldConfig?.project_commercial_identity?.field_label
                    : "Identity"
                }} is required
              </p>
            </div>
          </div>
          <div
            style="
              display: inline-block;
              margin-top: 3px;
              margin-right: 30px;
            "
            *ngIf="
              isCommercialIdentity &&
              fieldConfig?.commercial_category?.is_active_field
            "
          >
            <div
              style="display: inline-block; margin-right: 0px; margin-top: 12px"
            >
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                >{{
                  fieldConfig?.commercial_category?.field_label
                    ? fieldConfig?.commercial_category?.field_label
                    : "Non Billable Category"
                }}<span
                  *ngIf="
                    fieldConfig?.commercial_category?.is_mandatory &&
                    isCommercialIdentity
                  "
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <app-input-search
                class="worklocationfield"
                [required]="
                  fieldConfig?.commercial_category?.is_mandatory &&
                  isCommercialIdentity
                "
                placeholder="Select {{
                  fieldConfig?.commercial_category?.field_label
                    ? fieldConfig?.commercial_category?.field_label
                    : 'Non Billable Category'
                }}"
                [list]="commercial_catageroies_list"
                formControlName="commercial_category"
                [disableNone]="true"
                [hideMatLabel]="true"
              >
              </app-input-search>
              <p
                class="errMessage"
                *ngIf="
                  fieldConfig?.commercial_category?.is_mandatory &&
                  errtxt &&
                  contactForm.get('commercial_category').status ==
                    'INVALID'
                "
              >
                
                {{
                  fieldConfig?.commercial_category?.field_label
                    ? fieldConfig?.commercial_category?.field_label
                    : "Non Billable Category"
                }} is required
              </p>
            </div>
          </div>
          <div
            style="
              display: inline-block;
              margin-left: 20px;
              margin-top: 8px;
              margin-right: 30px;
            "
          >
          </div>
          <div
            style="
              display: inline-block;
              margin-left: 20px;
              margin-top: 3px;
              margin-right: 30px;
            "
            *ngIf="
              isHybridWorkLocationActive &&
              fieldConfig?.hybrid_work_location?.is_active_field
            "
          >
            <div
              style="display: inline-block; margin-right: 0px; margin-top: 12px"
            >
              <label
                class="fieldLabel"
                style="margin-bottom: 3px; display: block"
                >{{
                  fieldConfig?.hybrid_work_location?.field_label
                    ? fieldConfig?.hybrid_work_location?.field_label
                    : "Work Location"
                }}<span
                  *ngIf="fieldConfig?.hybrid_work_location?.is_mandatory"
                  class="required-star"
                >
                  &nbsp;*</span
                >
              </label>
              <app-input-search
                class="worklocationfield"
                [required]="fieldConfig?.hybrid_work_location?.is_mandatory"
                placeholder="Select {{
                  fieldConfig?.hybrid_work_location?.field_label
                    ? fieldConfig?.hybrid_work_location?.field_label
                    : 'Hybrid Work Location'
                }}"
                [list]="WorkLocationMaster"
                formControlName="hybrid_work_location"
                [disableNone]="true"
                [hideMatLabel]="true"
              >
              </app-input-search>
              <p
                class="errMessage"
                *ngIf="
                  fieldConfig?.hybrid_work_location?.is_mandatory &&
                  errtxt &&
                  contactForm.get('hybrid_work_location').status == 'INVALID'
                "
              >
                
                {{
                  fieldConfig?.hybrid_work_location?.field_label
                    ? fieldConfig?.hybrid_work_location?.field_label
                    : "Hybrid Work Location"
                }} is required
              </p>
            </div>
          </div>
        </div>

        <!-- <div *ngIf="fieldConfig?.need_client_interview?.is_active_field">
        <mat-slide-toggle [checked]="toggle" (change)="toggleChanges($event)">
          Need client interview
        </mat-slide-toggle>
      </div> -->
        <div *ngIf="tenantSpecificFeatures.hasCostDetails">
          <button
            mat-stroked-button
            (click)="openCostDetailDialog()"
            [ngClass]="
              isCostDetailsBtnActive()
                ? 'costdetailbtn-active'
                : 'costdetailbtn-disabled'
            "
          >
            Cost Details
          </button>
        </div>
      </div>
    </form>
  </ng-container>
</div>
<ng-template #datevalue let-label="label" >
  <div class="dateErrtxt">{{label}} is required</div>
</ng-template>
