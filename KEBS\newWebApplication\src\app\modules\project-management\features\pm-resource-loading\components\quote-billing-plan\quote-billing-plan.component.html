<div class="quote-billing-card">
    <div class="content-card-class" *ngIf="quoteData">
        <div class="row content-long-text">
          <div class="content-id">
            <div class="d-flex expand-col">
              <mat-icon class="arrow-icn" *ngIf="!quoteData?.expand" (click)="onExpandClick()" 
                >keyboard_arrow_right</mat-icon
              >
              <mat-icon class="arrow-icn" *ngIf="quoteData?.expand" (click)="onExpandClick()"
                >keyboard_arrow_down</mat-icon
              >
              <span>#{{ quoteData?.milestone_id ? quoteData.milestone_id  : "-" }}</span>
            </div>
            <div class="content-button" *ngIf="quoteData && quoteData['status_id'] == 2">
              <div class="circle"></div>
              <span class="active-text">Active</span>
            </div>
            <div class="content-button-save" *ngIf="quoteData && quoteData['status_id'] == 1">
              <div class="circle-save"></div>
              <span class="save-text">Saved</span>
            </div>

          </div>
          <div></div>
          <div class="content-long-header">
            <span>{{
              quoteData?.milestone_name ? quoteData.milestone_name : "-"
              }}
            </span>
            <span class="edit-click" (click)="onEditClick()" *ngIf="quoteData && quoteData['expand']">Edit</span>
          </div>
        </div>
        <mat-divider [vertical]="true"></mat-divider>
        <div class="row content-text">
          <div class="icon-class">
            <svg
              width="19"
              height="18"
              viewBox="0 0 19 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2.30775 17.5C1.80908 17.5 1.38308 17.3234 1.02975 16.9703C0.676583 16.6169 0.5 16.1909 0.5 15.6923V2.30775C0.5 1.80908 0.676583 1.38308 1.02975 1.02975C1.38308 0.676583 1.80908 0.5 2.30775 0.5H15.6923C16.1909 0.5 16.6169 0.676583 16.9703 1.02975C17.3234 1.38308 17.5 1.80908 17.5 2.30775V5.029H16V2.30775C16 2.21792 15.9712 2.14417 15.9135 2.0865C15.8558 2.02883 15.7821 2 15.6923 2H2.30775C2.21792 2 2.14417 2.02883 2.0865 2.0865C2.02883 2.14417 2 2.21792 2 2.30775V15.6923C2 15.7821 2.02883 15.8558 2.0865 15.9135C2.14417 15.9712 2.21792 16 2.30775 16H15.6923C15.7821 16 15.8558 15.9712 15.9135 15.9135C15.9712 15.8558 16 15.7821 16 15.6923V12.971H17.5V15.6923C17.5 16.1909 17.3234 16.6169 16.9703 16.9703C16.6169 17.3234 16.1909 17.5 15.6923 17.5H2.30775ZM10.3077 13.5C9.80908 13.5 9.38308 13.3234 9.02975 12.9703C8.67658 12.6169 8.5 12.1909 8.5 11.6923V6.30775C8.5 5.80908 8.67658 5.38308 9.02975 5.02975C9.38308 4.67658 9.80908 4.5 10.3077 4.5H16.6923C17.1909 4.5 17.6169 4.67658 17.9703 5.02975C18.3234 5.38308 18.5 5.80908 18.5 6.30775V11.6923C18.5 12.1909 18.3234 12.6169 17.9703 12.9703C17.6169 13.3234 17.1909 13.5 16.6923 13.5H10.3077ZM16.6923 12C16.7821 12 16.8558 11.9712 16.9135 11.9135C16.9712 11.8558 17 11.7821 17 11.6923V6.30775C17 6.21792 16.9712 6.14417 16.9135 6.0865C16.8558 6.02883 16.7821 6 16.6923 6H10.3077C10.2179 6 10.1442 6.02883 10.0865 6.0865C10.0288 6.14417 10 6.21792 10 6.30775V11.6923C10 11.7821 10.0288 11.8558 10.0865 11.9135C10.1442 11.9712 10.2179 12 10.3077 12H16.6923ZM13 10.5C13.4167 10.5 13.7708 10.3542 14.0625 10.0625C14.3542 9.77083 14.5 9.41667 14.5 9C14.5 8.58333 14.3542 8.22917 14.0625 7.9375C13.7708 7.64583 13.4167 7.5 13 7.5C12.5833 7.5 12.2292 7.64583 11.9375 7.9375C11.6458 8.22917 11.5 8.58333 11.5 9C11.5 9.41667 11.6458 9.77083 11.9375 10.0625C12.2292 10.3542 12.5833 10.5 13 10.5Z"
                fill="#F27A6C"
              />
            </svg>
          </div>
          <div class="text-content-class">
            <span class="content-head">Milestone Value</span>
            <span class="content-value"
              >{{ quoteData?.currency ? quoteData.currency : "-" }} {{ (quoteData?.revenue ? quoteData.revenue : 0) | number : "1.2-2" }}
              </span
            >
          </div>
        </div>
        <mat-divider [vertical]="true"></mat-divider>

        <div class="row content-text">
          <div class="icon-class">
            <svg
              width="20"
              height="21"
              viewBox="0 0 20 21"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2 6.80581H16V4.30581C16 4.22881 15.9679 4.15831 15.9038 4.09431C15.8398 4.03014 15.7693 3.99806 15.6923 3.99806H2.30775C2.23075 3.99806 2.16025 4.03014 2.09625 4.09431C2.03208 4.15831 2 4.22881 2 4.30581V6.80581ZM2.30775 19.4981C1.80258 19.4981 1.375 19.3231 1.025 18.9731C0.675 18.6231 0.5 18.1955 0.5 17.6903V4.30581C0.5 3.80065 0.675 3.37306 1.025 3.02306C1.375 2.67306 1.80258 2.49806 2.30775 2.49806H3.69225V0.382812H5.23075V2.49806H12.8078V0.382812H14.3078V2.49806H15.6923C16.1974 2.49806 16.625 2.67306 16.975 3.02306C17.325 3.37306 17.5 3.80065 17.5 4.30581V9.76931C17.2602 9.66415 17.0153 9.57915 16.7653 9.51431C16.5153 9.44965 16.2602 9.39873 16 9.36156V8.30581H2V17.6903C2 17.7673 2.03208 17.8378 2.09625 17.9018C2.16025 17.966 2.23075 17.9981 2.30775 17.9981H8.80975C8.89425 18.2749 8.9965 18.5367 9.1165 18.7836C9.23633 19.0304 9.3725 19.2686 9.525 19.4981H2.30775ZM15.1923 20.4981C13.9436 20.4981 12.8814 20.0602 12.0058 19.1846C11.1301 18.3089 10.6923 17.2467 10.6923 15.9981C10.6923 14.7494 11.1301 13.6872 12.0058 12.8116C12.8814 11.9359 13.9436 11.4981 15.1923 11.4981C16.4411 11.4981 17.5033 11.9359 18.3788 12.8116C19.2544 13.6872 19.6923 14.7494 19.6923 15.9981C19.6923 17.2467 19.2544 18.3089 18.3788 19.1846C17.5033 20.0602 16.4411 20.4981 15.1923 20.4981ZM16.8578 18.2866L17.4808 17.6636L15.6348 15.8173V13.0558H14.75V16.1788L16.8578 18.2866Z"
                fill="#F27A6C"
              />
            </svg>
          </div>
          <div class="text-content-class">
            <span class="content-head">Planned Quantity</span>
            <span class="content-value">{{
              (quoteData?.original_balance ? quoteData.original_balance : 0) | decimal : decimalPlaces 
            }} Hrs</span>
          </div>
        </div>
        <mat-divider [vertical]="true" *ngIf="showRemainingQuantity"></mat-divider>
        <div class="row content-text" *ngIf="showRemainingQuantity">
          <div class="icon-class">
            <svg
              width="18"
              height="20"
              viewBox="0 0 18 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 15.6326C7.96417 15.6326 6.96675 15.8105 6.00775 16.1663C5.04875 16.5221 4.16475 17.0622 3.35575 17.7866V17.9403C3.38142 17.9596 3.40708 17.9741 3.43275 17.9836C3.45825 17.9932 3.48708 17.9981 3.51925 17.9981H14.4615C14.4937 17.9981 14.5209 17.9932 14.5433 17.9836C14.5658 17.9741 14.5898 17.9596 14.6155 17.9403V17.7866C13.8193 17.0622 12.9449 16.5221 11.9923 16.1663C11.0398 15.8105 10.0423 15.6326 9 15.6326ZM2 16.9828C2.9 16.0995 3.94583 15.4036 5.1375 14.8953C6.32917 14.387 7.61667 14.1328 9 14.1328C10.3833 14.1328 11.6708 14.387 12.8625 14.8953C14.0542 15.4036 15.1 16.0995 16 16.9828V4.30581C16 4.22881 15.9679 4.15831 15.9038 4.09431C15.8398 4.03014 15.7692 3.99806 15.6923 3.99806H2.30775C2.23075 3.99806 2.16025 4.03014 2.09625 4.09431C2.03208 4.15831 2 4.22881 2 4.30581V16.9828ZM9 11.7481C8.0975 11.7481 7.33017 11.4321 6.698 10.8001C6.066 10.1679 5.75 9.40056 5.75 8.49806C5.75 7.59556 6.066 6.82823 6.698 6.19606C7.33017 5.56406 8.0975 5.24806 9 5.24806C9.9025 5.24806 10.6698 5.56406 11.302 6.19606C11.934 6.82823 12.25 7.59556 12.25 8.49806C12.25 9.40056 11.934 10.1679 11.302 10.8001C10.6698 11.4321 9.9025 11.7481 9 11.7481ZM9 10.2481C9.48083 10.2481 9.89267 10.0766 10.2355 9.73356C10.5785 9.39073 10.75 8.9789 10.75 8.49806C10.75 8.01723 10.5785 7.6054 10.2355 7.26256C9.89267 6.91956 9.48083 6.74806 9 6.74806C8.51917 6.74806 8.10733 6.91956 7.7645 7.26256C7.4215 7.6054 7.25 8.01723 7.25 8.49806C7.25 8.9789 7.4215 9.39073 7.7645 9.73356C8.10733 10.0766 8.51917 10.2481 9 10.2481ZM2.30775 19.4981C1.80258 19.4981 1.375 19.3231 1.025 18.9731C0.675 18.6231 0.5 18.1955 0.5 17.6903V4.30581C0.5 3.80065 0.675 3.37306 1.025 3.02306C1.375 2.67306 1.80258 2.49806 2.30775 2.49806H3.69225V0.382812H5.23075V2.49806H12.8077V0.382812H14.3077V2.49806H15.6923C16.1974 2.49806 16.625 2.67306 16.975 3.02306C17.325 3.37306 17.5 3.80065 17.5 4.30581V17.6903C17.5 18.1955 17.325 18.6231 16.975 18.9731C16.625 19.3231 16.1974 19.4981 15.6923 19.4981H2.30775Z"
                fill="#F27A6C"
              />
            </svg>
          </div>
          <div class="text-content-class">
            <span class="content-head">Remaining Quantity</span>
            <span class="content-value"
              >{{
                resourceLoadingComponent?.getRemainingBalance()
                  ? (resourceLoadingComponent.getRemainingBalance() | decimal : decimalPlaces)
                  : 0
              }}
              Hrs</span
            >
          </div>
        </div>
        <mat-divider [vertical]="true" *ngIf="showAddDependencies"></mat-divider>
        <div class="row content-text" *ngIf="showAddDependencies">
          <div class="icon-class">
            <svg width="16" height="19" viewBox="0 0 16 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0.107422 18.3301V0.330078H15.1074L13.0594 4.96483L15.1074 9.59933H1.60742V18.3301H0.107422ZM1.60742 8.09933H12.8402L11.4287 4.96483L12.8402 1.83008H1.60742V8.09933Z" fill="#F27A6C"/>
            </svg>  
          </div>
          <div class="text-content-class">
            <span class="content-head">Add Dependencies</span>
            <div class="content-value edit">
              <span>{{ quoteData?.dependencies ? quoteData.dependencies : 0 }}</span>
              <span class="edit-class" (click)="addDependency()">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1.5 15.5H2.7615L12.998 5.2635L11.7365 4.002L1.5 14.2385V15.5ZM0 17V13.6155L13.1905 0.43075C13.3417 0.293416 13.5086 0.187333 13.6913 0.1125C13.8741 0.0375 14.0658 0 14.2663 0C14.4668 0 14.6609 0.0355838 14.8488 0.10675C15.0367 0.177917 15.2032 0.291083 15.348 0.446249L16.5693 1.68275C16.7244 1.82758 16.835 1.99425 16.901 2.18275C16.967 2.37125 17 2.55975 17 2.74825C17 2.94942 16.9657 3.14133 16.897 3.324C16.8283 3.50683 16.7191 3.67383 16.5693 3.825L3.3845 17H0ZM12.3562 4.64375L11.7365 4.002L12.998 5.2635L12.3562 4.64375Z" fill="#45546E"/>
                  </svg>
                  
              </span>
            </div>
          </div>
        </div>
      </div>
    <div *ngIf="quoteData && quoteData['expand']" class="resource-plan">
        <app-resource-loading-landing-page #resourceLoadingComponent [height]="tableHeight" [deliverable_id]="quoteData?.deliverable_id" [start_date]="quoteData?.start_date" [end_date]="quoteData?.end_date"></app-resource-loading-landing-page>
    </div>
</div>