import { Injectable } from '@angular/core';
import * as _ from 'underscore';
import { HttpClient } from '@angular/common/http';
import { Subject, throwError } from 'rxjs';
import { Resolve, ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, map } from 'rxjs/operators';
import { UtilityService } from '../utility/utility.service';
import { LoginService } from '../login/login.service';

@Injectable({
  providedIn: 'root',
})
export class RolesService implements Resolve<any> {
  public roles: any = [];
  label: any = [];
  currency_info: any;
  roles$ = new Subject();
  userProfile: any = {};
  roleAccessList: any = [];

  constructor(
    private $http: HttpClient,
    private _loginService: LoginService,
    private _utilityService: UtilityService
  ) {}

  getAccessListForProject() {
    const user = this._loginService.getProfile().profile;
    this.getAccessList(user.oid, 'project').subscribe((res: any) => {
      console.log(res);
      this.roleAccessList = res;
    });
  }

  resolve() {
    const user = this._loginService.getProfile().profile;
    this.userProfile = user;
    this.getAccessList(user.oid, 'project').subscribe((res: any) => {
      this.roleAccessList = res;
    });

    this.getUserLabel().subscribe((res: any) => {
      console.log(res);
      this.label = res['data'];
      this.currency_info = res['currency_info'];
    });

    return this.getAllUserRoles(user).pipe(
      map((result: any) => {
        if (result.length == 0) {
          this._utilityService.showMessage(
            'Access Denied. Contact KEBS team',
            'OK'
          );

          setTimeout(() => this._loginService.logout(), 500);
          return null;
        }

        this.roles = result;
        this.roles$.next(this.roles);

        return result;
      }),

      catchError((error) => {
        this._utilityService.showMessage(
          'Something went wrong try again',
          'OK'
        );
        setTimeout(() => this._loginService.logout(), 5000);
        return throwError(error);
      })
    );
  }

  checkApplicationForRoles(application_name) {
    var aclApplicationRoleExists = _.where(this.roles, {
      application_name: application_name,
    });

    if (aclApplicationRoleExists.length > 0)
      return this.isApplicationVisibleForUser(
        aclApplicationRoleExists[0].application_id
      );
    else return false;
  }

  isApplicationVisibleForUser(applicationId) {
    let displayObjects = _.where(this.roles, {
      application_id: applicationId,
      object_id: 6,
    });

    if (displayObjects.length > 0) {
      if (
        displayObjects[0].object_value == '*' ||
        displayObjects[0].object_value == 'True'
      )
        return true;
      else if (
        displayObjects[0].object_value == 'User' &&
        displayObjects[0].object_id_values &&
        (displayObjects[0].object_id_values == 'ALL' ||
          JSON.parse(displayObjects[0].object_id_values)[0] == 'ALL' ||
          _.contains(
            JSON.parse(displayObjects[0].object_id_values),
            applicationId
          ))
      )
        return true;
      else return false;
    } else return false;
  }

  getFirstApplicationForRolesPath() {
    var first_application_for_role_path = '/main/';

    var dashboardRoleExists = _.where(this.roles, {
      application_name: 'Dashboard',
    });
    var reportsRoleExists = _.where(this.roles, {
      application_name: 'Reports',
    });
    var projectRoleExists = _.where(this.roles, {
      application_name: 'Project',
    });
    var preSalesRoleExists = _.where(this.roles, {
      application_name: 'PreSales',
    });
    var invoiceRoleExists = _.where(this.roles, {
      application_name: 'Invoice',
    });
    var ctaRoleExists = _.where(this.roles, { application_name: 'CTA' });

    if (dashboardRoleExists.length > 0)
      first_application_for_role_path += 'dashboard';
    else if (reportsRoleExists.length > 0)
      first_application_for_role_path += 'reports';
    else if (projectRoleExists.length > 0)
      first_application_for_role_path += 'project';
    else if (preSalesRoleExists.length > 0)
      first_application_for_role_path += 'presales';
    else if (invoiceRoleExists.length > 0)
      first_application_for_role_path += 'invoice';
    else if (ctaRoleExists.length > 0) first_application_for_role_path += 'cta';

    return first_application_for_role_path;
  }

  getUserRoleOrgCodes(applicationName) {
    let userRoleOrgCodeList = _.where(this.roles, {
      object_id: 13,
      application_name: applicationName,
    });

    return this.getUserRoleOrgCodesFn(userRoleOrgCodeList);
  }

  getUserRoleOrgCodesFn(userRoleOrgCodeList) {
    let orgCodes;

    if (userRoleOrgCodeList.length > 0) {
      if (
        userRoleOrgCodeList[0].object_value === '*' ||
        userRoleOrgCodeList[0].object_value === 'True'
      )
        orgCodes = 'ALL';
      else if (userRoleOrgCodeList[0].object_value === 'User') {
        orgCodes = userRoleOrgCodeList[0].object_id_values
          ? JSON.parse(userRoleOrgCodeList[0].object_id_values)
          : [];

        if (orgCodes.length > 0 && orgCodes[0] == 'ALL') orgCodes = 'ALL';
      } else orgCodes = [];
    } else orgCodes = [];

    return orgCodes;
  }

  getUserRoleNgrOrgCodesByAppId(applicationId, objectIds) {
    let userRoleNgrOrgCodes = [];

    for (let objectId of objectIds)
      userRoleNgrOrgCodes.push({
        objectId: objectId,
        roles: this.getUserRoleDetails(objectId, applicationId),
      });

    return userRoleNgrOrgCodes;
  }

  getUserRolePostingTypes(applicationId) {
    return this.getUserRoleDetails(166, applicationId);
  }

  getUserRoleDetails(objectId, applicationId) {
    let userRoleOrgCodeList = _.where(this.roles, {
      object_id: objectId,
      application_id: applicationId,
    });

    return this.getUserRoleOrgCodesFn(userRoleOrgCodeList);
  }

  getAllUserRoles(user) {
    return this.$http.post('/api/acl/getUserRole', { oid: user.oid });
  }

  getAccessList(userOid, type) {
    return this.$http.post('/api/acl/getAccessFor', {
      oid: userOid,
      type: type,
    });
  }

  getUserLabel() {
    return this.$http.post('/api/general/getLabelForUser', {});
  }
}
