import { Component, EventEmitter, NgModule, OnInit, Output, OnDestroy } from '@angular/core';
import { FormGroup, FormControl, FormArray, Validators, FormBuilder } from '@angular/forms';
import { SubSink } from 'subsink';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { EmployeeDirectoryService } from 'src/app/modules/employee-directory/services/employee-directory.service';
import * as _ from 'underscore';
import Swal from "sweetalert2";

@Component({
  selector: 'app-forward-request',
  templateUrl: './forward-request.component.html',
  styleUrls: ['./forward-request.component.scss'],
})
export class ForwardRequestComponent implements OnInit,OnDestroy {
  orgDetails: any = {
    entity:'-',
    division:'-',
    subdivision:'-',
    region:'-'
  };
  orgPreDetails:any={
    entity:'-',
    division:'-',
    subdivision:'-',
    region:'-',
    rm_officer:'-'
  };
  rmofficer_name : any;
  rmOfficerId:string;
  //Entity Edit Component
  formConfig: any=[];
  orgMappingList: any=[];
  selectedBusinessEntity: any;
  loaderObject : any = {
    isPageLoading: false,
    isForwardReqOrgLoading: false,
  };
  fieldConfig={};
  constructor(
    private formBuilder: FormBuilder,
    private _router: Router,
    private _route: ActivatedRoute,
    public dialog: MatDialog,
    private _edService: EmployeeDirectoryService,
    private _rmService: RmService,
    private _toaster: ToasterService,
    private _projectService: ProjectService,
    private _reqDetailService:RmRequestDetailService
  ) {}
  subs = new SubSink();
  @Output() goback = new EventEmitter();

  EntityListMaster: any= [];
  DivisionListMaster: any= [];
  SubDivisionListMaster: any= [];
  regionListMaster:any = [];
  requestData:any;
  fieldList:any = [];

  rmofficerparams = {};
  rmofficerallocated = false;
  showcard = false;
  contactForm = new FormGroup({
    entity: new FormControl('', Validators.required),
    division: new FormControl('', Validators.required),
    subdivision: new FormControl('', Validators.required),
    region: new FormControl('')
  });
  requestId : number;
  rmFieldconfig : any;

  // recruitmentDetails : any;
  // tenantSpecificFeatures = {
  //   hasForwardToRecruitment: false
  // }

  async ngOnInit() {
    this.loaderObject.isPageLoading = true;
    await this.handleFieldConfig();
    this.requestId = parseInt(this._router.url.split('/')[5]);
    this.getRequestData();
    await this.handleRmConfig()
    await this._projectService.getProjectFormFieldConfig().then((res: any)=>{
      if(res['messType']=="S"){
        this.formConfig=res['data']
      }
    })
    await this._projectService.getOrgMapping().then(async(res: any)=>{
      if(res['messType']=="S"){
        this.orgMappingList = res['data']
      }

    })
    this.EntityListMaster = await this.getEntities();
    this.regionListMaster = await this.getRegionList();
    await this.organizationDetailsFormListener();
    await this.organizationDetailsFormValueListener();
    await this.prePatchValues();
    // await this.fetchRecruitmentData()
    this.loaderObject.isPageLoading = false;
  }


  checkIfSameOrg() {
    return (
      this.requestData.region == this.contactForm.get('region').value &&
      this.requestData.entity == this.contactForm.get('entity').value &&
      this.requestData.division == this.contactForm.get('division').value &&
      this.requestData.sub_division == this.contactForm.get('subdivision').value
    )
  }

  async handleRmConfig() {
    this.rmFieldconfig = await this.retrieveRmConfig()
  }

  // async fetchRecruitmentData() {
  //   if(this.tenantSpecificFeatures.hasForwardToRecruitment && 
  //     this.requestData.recruitment_request_id) {
  //     this.subs.sink = await this._rmService.fetchRecruitmentData(this.requestData.recruitment_request_id)
  //     .subscribe((res: any) => {
  //       if(res['messType'] == 'S') {
  //         this.recruitmentDetails = res['data'][0]
  //       }
  //     },(err) => {
  //       console.log(err)
  //       this._toaster.showError("Error","Failed to fetch recruitment details",3000)
  //     })
  //   }
  // }

  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._rmService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err) {
          resolve(res.data)
        }
      },(err) => {
        console.log(err)
      })
    })
  }
 
  
  organizationDetailsFormListener() {
    this.contactForm
      .get('entity')
      .valueChanges.subscribe(async (res: any) => {
        this.contactForm.get('division').reset();
        this.contactForm.get('subdivision').reset();
        this.DivisionListMaster = []
        this.SubDivisionListMaster = []
        if(res){
          this.DivisionListMaster = await this.getDivision(res);
        }
      });
    this.contactForm
      .get('division')
      .valueChanges.subscribe(async (res: any) => {
        let entity_id = this.contactForm.get('entity').value;
        this.SubDivisionListMaster = []
        if(res && entity_id){
          this.SubDivisionListMaster = await this.getSubDivision(entity_id, res);
        }
      });
      console.log(this.contactForm);
  }


  getEntities() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService.getEntities().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getDivision(entity) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService.getDivision(entity).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getSubDivision(entity, division) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService
        .getSubDivision(entity, division)
        .subscribe(
          (res: any) => {
            resolve(res.data);
          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });
  }

  getrmofficerstatus(){
    if((this.contactForm.get('entity').status &&
        this.contactForm.get('division').status &&
        this.contactForm.get('subdivision').status) === 'VALID'){
          this.rmofficerparams['entity']=this.contactForm.get('entity').value
          this.rmofficerparams['division']=this.contactForm.get('division').value
          this.rmofficerparams['sub_division']=this.contactForm.get('subdivision').value
          this.rmofficerparams['region'] = this.contactForm.get('region').value
          return new Promise((resolve, reject) => {
            this.subs.sink = this._rmService.getrmofficerstatus(this.rmofficerparams).subscribe(
              (res: any) => {
                this.showcard = true
                console.log("rmofficer status",res.has_rm_officier)
                this.rmofficerallocated = res.has_rm_officier
                if(this.rmofficerallocated){
                  console.log(res.data[0]);
                  this.rmofficer_name = res.data[0].rmofficer_name
                  this.rmOfficerId = res.data[0].oid
             }
                resolve(res);
              },
              (err) => {
                console.log(err);
                reject(err);
              }
            );
          });
        }
  }

  submitForwardRequest() {

    if(this.contactForm.status === 'VALID'  && this.rmofficerallocated){

      if(!this.checkIfSameOrg()) {
        this.loaderObject.isForwardReqOrgLoading = true
        let params = {
          request_id: this.requestId,
          entity: this.contactForm.get('entity').value,
          division: this.contactForm.get('division').value,
          sub_division: this.contactForm.get('subdivision').value,
          region:this.contactForm.get('region').value
        }
        this.subs.sink = this._rmService.forwardRequest(params)
        .subscribe((res: any) => {
          this.loaderObject.isForwardReqOrgLoading = false
          if(!res.err) {
            this._toaster.showSuccess("Success","Request forwarded successfully",3000)
            // this._router.navigateByUrl('/main/resource-management/home/<USER>')
            // console.log(this._rmService.getRouterLink("requests",null));
            this._router.navigateByUrl(this._rmService.getRouterLink("requests",null))
          }
        },(err) => {
          this.loaderObject.isForwardReqOrgLoading = false
          this._toaster.showError("Failed","Failed to forward request",2000)
          console.log(err)
        })
      }
      else {
        this._toaster.showWarning("Same organisation selected","Kindly choose different Organisation to forward the request")
      }
    }
    else if(this.contactForm.status === 'INVALID'){
      let regionValue = this.contactForm.get('region').value;
      
      if(this.isMandate('region') && (regionValue == null || regionValue == '' || regionValue == undefined ))
      this._toaster.showError("Failed","Region is not selected",2000)
      else this._toaster.showError("Failed","Organisation is not selected",2000)
    }
  }

  async prePatchValues(){
    if(this.requestData.entity){
      this.DivisionListMaster = await this.getDivision(this.requestData.entity);
      console.log(this.DivisionListMaster);
     }
    this.SubDivisionListMaster = []
    if(this.requestData.division && this.requestData.entity){
      this.SubDivisionListMaster = await this.getSubDivision(this.requestData.entity, this.requestData.division);
    }
    console.log("Under PrePatchValues",this.requestData.entity)
    _.find(this.EntityListMaster, x=>{ 
      if(x.id === this.requestData.entity){
        this.orgPreDetails.entity =  x.name;
      }
     });

     console.log(this.DivisionListMaster);
    _.find(this.DivisionListMaster, x=>{ 
      if(x.id === this.requestData.division){
        this.orgPreDetails.division =  x.name;
      }
     });
     console.log(this.SubDivisionListMaster);
    _.find(this.SubDivisionListMaster, x=>{ 
      if(x.id === this.requestData.sub_division){
        this.orgPreDetails.subdivision =  x.name;
      }
     });
     _.find(this.regionListMaster, x=>{ 
      if(x.id === this.requestData.region){
        this.orgPreDetails.region =  x.name;
      }
     });
     this.contactForm.get('region').patchValue(this.requestData.region)
      if(this.requestData){
          let params = {
            entity:this.requestData.entity,
            division:this.requestData.division,
            sub_division:this.requestData.sub_division,
            region: this.requestData.region ? this.requestData.region : null
          }
          console.log("Params:",params)
            return new Promise((resolve, reject) => {
              this.subs.sink = this._rmService.getrmofficerstatus(params).subscribe(
                (res: any) => {
                  this.rmofficerallocated = res.has_rm_officier
                  console.log(this.rmofficerallocated)
                  if(this.rmofficerallocated){
                    this.orgPreDetails.rm_officer = res.data[0].rmofficer_name
               }
                  resolve(res);
                },
                (err) => {
                  console.log(err);
                  reject(err);
                }
              );
            });
          }
  }

  async organizationDetailsFormValueListener() {
    console.log(this.contactForm);
    let entityRes = this.contactForm.get('entity').value;
    this.rmofficer_name = '-'
    this.showcard = false
    this.rmofficerallocated = false
    this.DivisionListMaster = []
    this.SubDivisionListMaster = []
    if(entityRes){
        this.DivisionListMaster = await this.getDivision(entityRes);
        console.log(this.DivisionListMaster);
    }
    let divisionRes = this.contactForm.get('division').value;
    let entity_id = this.contactForm.get('entity').value;
    this.SubDivisionListMaster = []
    if(divisionRes && entity_id){
      this.SubDivisionListMaster = await this.getSubDivision(entity_id, divisionRes);
    }
    _.find(this.EntityListMaster, x=>{ 
        if(x.id === entityRes){
          this.orgDetails.entity =  x.name;
        }
       });

       console.log(this.DivisionListMaster);
      _.find(this.DivisionListMaster, x=>{ 
        if(x.id === divisionRes){
          this.orgDetails.division =  x.name;
        }
       });
       console.log(this.SubDivisionListMaster);
      _.find(this.SubDivisionListMaster, x=>{ 
        if(x.id === this.contactForm.get('subdivision').value){
          this.orgDetails.subdivision =  x.name;
        }
       });
      console.log(this.contactForm);
      console.log(this.orgDetails);
      this.getrmofficerstatus();
  }

  get getEntityValue(){
    // console.log(this.contactForm.get('entity'));
    return this.contactForm.get('entity').value;
  }
  get getDivisionValue(){
    return this.contactForm.get('division').value;
  }
  get getSubdivisionValue(){
    return this.contactForm.get('subdivision').value;
  }

  async addOrganisation(mode){
    console.log("Entity Value:",this.getEntityValue);
    console.log("FormConfig:",this.formConfig);
    console.log("orgMappingList:",this.orgMappingList);
    console.log("selectedEntity:",this.selectedBusinessEntity);

    if(mode === 'edit'){
      let val={
        "entity_id":this.getEntityValue,
        "entity_name":this.orgDetails.entity,
        "division_id":this.getDivisionValue,
        "division_name":this.orgDetails.division,
        "sub_division_id":this.getSubdivisionValue,
        "sub_division_name":this.orgDetails.subdivision
      }
      this.selectedBusinessEntity= val
    }

    const { BusinessDivisionEntityComponent } = await import(
      'src/app/modules/projects/features/project-item/components/business-division-entity/business-division-entity.component'
    );
    const dialogRef = this.dialog.open(BusinessDivisionEntityComponent, {
      height: '75%',
      width: '75%',
      data: {
        mode: mode,
        formConfig:this.formConfig,
        data:this.orgMappingList,
        selectedEntity:this.selectedBusinessEntity
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log(result);
      if (result['messType']=="S") {
        this.selectedBusinessEntity= result['data']
        console.log("Result Data:", result['data']);
        console.log("Entity:",result['data'].entity_id)
        this.contactForm.get('entity').patchValue(result['data'].entity_id);
        this.contactForm.get('division').patchValue(result['data'].division_id) 
        this.contactForm.get('subdivision').patchValue(result['data'].sub_division_id)
        this.orgDetails.entity = result['data'].entity_name;
        this.orgDetails.division = result['data'].division_name;
        this.orgDetails.subdivision= result['data'].sub_division_name;
        console.log(this.contactForm);
        this.getrmofficerstatus();
      }
    })
  }

  getRequestData() {
    this.subs.sink = this._reqDetailService.getRequestData().subscribe((res: any) => {
      // console.log('resources component',res)
      if(res)  {
        console.log('forward request sub',this.requestData)
        this.requestData = res
      }
    },(err) => {
      console.log(err)
    })
  }

  retrieveFieldConfig(view_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.retrieveFieldConfig(view_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  async handleFieldConfig() {
    console.log("Handle field Config Triggered !!!!!!!!");
    this.fieldList  = await this.retrieveFieldConfig('create_request')
    console.log("Field List",this.fieldList)
    if(this.fieldList.length > 0) {
      this.fieldList.forEach((val,i) => {
        this.fieldConfig[val.field_key] = val
      })
    }
    console.log("FIeld config",this.fieldConfig)
  }

  getRegionList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getRegionList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  /**
   * @description Checking Is Mandatory
   */
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, { field_key: field, is_active: true });
    console.log('Mandate:',mandate)
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
    return false;
  }

  ngOnDestroy() {
    this.subs.unsubscribe()
  }

}

import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import {
  MatDatepickerModule,
  MatDatepickerToggle,
} from '@angular/material/datepicker';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { CostDetailsDialogComponent } from 'src/app/modules/shared-lazy-loaded-components/team-members/pages/cost-details-dialog/cost-details-dialog/cost-details-dialog.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ProjectService } from 'src/app/modules/projects/services/project.service';
import { RmRequestDetailService } from '../../../services/rm-request-detail.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';

// import { RmService } from 'src/app/modules/resource-management/services/rm.service';
@NgModule({
  declarations: [ForwardRequestComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    SharedComponentsModule,
    MatFormFieldModule,
    MatRadioModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatTooltipModule
  ],
  exports: [],
})
export class ForwardRequestModule {}
