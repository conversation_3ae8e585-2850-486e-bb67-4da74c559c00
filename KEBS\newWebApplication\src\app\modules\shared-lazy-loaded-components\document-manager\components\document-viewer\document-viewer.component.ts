import { Component, Inject, OnInit, ViewChild, ElementRef } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { HttpClient } from '@angular/common/http';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { DocumentManagerService } from '../../service/document-manager.service';

@Component({
  selector: 'app-document-viewer',
  templateUrl: './document-viewer.component.html',
  styleUrls: ['./document-viewer.component.scss'],
})
export class DocumentViewerComponent implements OnInit {
  isLoaded: boolean = false;
  isGoogleViewerBlocked: boolean = false;
  expHeaderId: any;
  previewUrl: any;
  fileFormat: string;
  @ViewChild('pdfCanvas', { static: true }) pdfCanvas: ElementRef<HTMLCanvasElement>;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<DocumentViewerComponent>,
    private http: HttpClient,
    private sanitizer: DomSanitizer,
    private documentService: DocumentManagerService
  ) {
    console.log('Data:', data);
    this.expHeaderId = data.expHeaderId;
    this.fileFormat = data.fileFormat.toLowerCase();
  }

  ngOnInit(): void {
    this.documentService.isGoogleViewerBlocked().subscribe((blocked) => {
      this.isGoogleViewerBlocked = blocked;
      console.log('Google Viewer Blocked Check Local: ', this.isGoogleViewerBlocked);
    });
    this.isLoaded = true;
  }


  /**
  * @description Creates Blob (POC for Opening a file locally after download)
  */
  loadFileForPreview(url: string): void {
    if (this.isGoogleViewerBlocked) {
      this.fetchFileBlob(url);
    } else {
      this.previewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
        `https://docs.google.com/gview?url=${encodeURIComponent(url)}&embedded=true`
      );
      this.isLoaded = true;
    }
  }

  fetchFileBlob(url: string): void {
    this.http.get(url, { responseType: 'blob' }).subscribe(
      (blob) => {
        const blobUrl = URL.createObjectURL(blob);

        this.previewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(blobUrl);
        console.log('File fetched successfully:', this.previewUrl);

        this.previewUrl = this.getSafeUrl(this.previewUrl);
        console.log('Safe Conversion successfully:', this.previewUrl);

        this.isLoaded = true;
      },
      (error) => {
        console.error('Error fetching file:', error);
      }
    );
  }

  isGoogleDocsSupportedFormat(): boolean {
    return ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt'].includes(this.fileFormat);
  }

  isSupportedFormat(fileFormat: string): boolean {
    const supportedFormats = ['pdf', 'png', 'jpg', 'jpeg', 'txt'];
    return supportedFormats.includes(fileFormat.toLowerCase());
  }

  isImageFormat(): boolean {
    return ['png', 'jpg', 'jpeg'].includes(this.fileFormat);
  }

  getSafeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  downloadFile(fileUrl: any): void {
    let fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1, fileUrl.indexOf('?'));
    this.http.get(fileUrl, { responseType: 'blob' }).subscribe((blob) => {
      let blobUrl = URL.createObjectURL(blob);
      console.log
      let element = document.createElement('a');
      element.href = blobUrl;
      element.download = `EX${this.expHeaderId}-${decodeURIComponent(fileName.replace(/%20/g, ' '))}`;
      element.click();
      window.open(blobUrl, '_blank');
    });
  }
}
