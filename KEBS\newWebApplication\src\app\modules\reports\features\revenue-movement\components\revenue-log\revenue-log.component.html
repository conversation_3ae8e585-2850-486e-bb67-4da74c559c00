<div class="container-fluid log-history mb-3">
  <div class="row p-1 pt-3 pb-3">
      <div class="col">
          <mat-icon style="font-size: 21px; color:#cf0001; vertical-align: middle;">history</mat-icon>
          <span class="pl-1 heading-top">Log History</span>
      </div>
      <div class="col-1">
          <span class="vertical-align:middle" (click)="closeLogDialog()">
              <mat-icon class="icon-cancel">close</mat-icon>
          </span>
      </div>
  </div>
  <hr class="m-0">
  <div class="spinner-class">
      <div *ngIf="logSpinner == true" class="pt-4 pb-4 text-center">
          <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
          </div>
      </div>
  </div>
  <div>
      <div class="row">
          <div class="col p-0">
              <div class="mt-4" *ngFor="let item of logTemplate;">
                <mat-card>
                  <div class="row">
                    <div class="col-5">
                      <div class="margin">
                        <strong>Project Code</strong>
                      </div>
                      <div class="margin">
                        <strong>Milestone ID</strong>
                      </div>
                      <div class="margin">
                        <strong>From Month</strong>
                      </div>
                      <div class="margin">
                        <strong>To Month</strong>
                      </div>
                      <div class="margin">
                        <strong>Revenue Type</strong>
                      </div>
                      <div class="margin">
                        <strong>Revenue Value</strong>
                      </div>
                      <div class="margin">
                        <strong>Currency</strong>
                      </div>
                    </div>
                    <div class="col-1">
                      <div class="margin">
                        <span>:</span>
                      </div>
                      <div class="margin">
                        <span>:</span>
                      </div>
                      <div class="margin">
                        <span>:</span>
                      </div>
                      <div class="margin">
                        <span>:</span>
                      </div>
                      <div class="margin">
                        <span>:</span>
                      </div>
                      <div class="margin">
                        <span>:</span>
                      </div>
                      <div class="margin">
                        <span>:</span>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="ellipsis">
                        {{ item.project_code }}
                      </div>
                      <div class="ellipsis">
                       {{ item.milestone_id }}
                      </div>                        
                      <div class="ellipsis">
                        {{ item.from_date }}
                      </div>
                      <div class="ellipsis">
                       {{ item.to_date }}
                      </div>
                      <div class="ellipsis">
                        {{ item.revenue_type }}
                       </div>
                       <div class="ellipsis">
                        {{ item.revenue_value }}
                       </div>
                       <div class="ellipsis">
                        {{ item.currency }}
                       </div>
                    </div>

                  </div>
                  <div class="row d-flex justify-content-end" style="color:#6E7B8F; margin-top:10px;">
                    <span class="pr-1">By </span>
                    <span class="pr-1">{{item?.program_run_by}}</span>
                    <span class="pr-1">on</span>
                    <span class="pr-1">{{ formatDate(item?.created_on) }}</span>
                  </div>
                </mat-card>
              </div>
            </div>
            
      </div>
  </div>
  <div *ngIf="logHistoryFound==false">
      <div class="row mt-5 d-flex justify-content-center">
          <mat-icon class="no-history-icon">running_with_errors</mat-icon>
          <span class="pl-2 no-history">No History Found!</span>
      </div>
  </div>
</div>

