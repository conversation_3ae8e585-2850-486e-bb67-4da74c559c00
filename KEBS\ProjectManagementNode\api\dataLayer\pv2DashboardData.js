const { json } = require("body-parser");
const { logger } = require("../../logger")
const pool = require("../../databaseCon").pool;
const rpool = require("../../databaseCon").rpool;
let mongo = require("../../mongo_conn_native").Connection;
const moment = require("moment");
const mysql = require("mysql");
const _ = require("underscore");
const { ObjectId, MongoDBNamespace } = require('mongodb');

/**
 * 
 * @param {*} key
 * @param {*} db 
 * @param {*} user
 * @param {*} authorization 
 * <AUTHOR>
 * @description Get dashboard ui configurations
 * @returns 
 */
module.exports.getDashboardUiConfiguration = async(key, db, user, authorization)=>{
    try{

        let configuration = await mongo.client
        .db(db)
        .collection("m_project_dashboard_ui_configuration")
        .find({ key: key, is_active: true })
        .toArray();
  
      if (configuration && configuration.length > 0) {
        return Promise.resolve({
          err: false,
          msg: "Configurations Retrieved Successfully!",
          data: configuration[0]["configuration"],
        });
      } else {
        return Promise.resolve({
          err: true,
          msg: "Configurations Not Found!",
        });
      }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * 
 * @param {*} report_id
 * @param {*} db 
 * @param {*} user
 * @param {*} authorization 
 * <AUTHOR> Rajendran
 * @description Get dashboard date filter configurations
 * @returns 
 */
module.exports.getDashboardDateFilterConfiguration = async(report_id, db, user, authorization)=>{
    try{

        let configuration = await mongo.client
        .db(db)
        .collection("m_project_dashboard_date_filter_config")
        .find({ report_id: report_id, is_active: true })
        .toArray();
  
      if (configuration && configuration.length > 0) {
        return Promise.resolve({
          err: false,
          msg: "Configurations Retrieved Successfully!",
          data: configuration[0],
        });
      } else {
        return Promise.resolve({
          err: true,
          msg: "Configurations Not Found!",
        });
      }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}

/**
 * 
 * @param {*} report_id
 * @param {*} db 
 * @param {*} user
 * @param {*} authorization 
 * <AUTHOR> Rajendran
 * @description Get dashboard configurations
 * @returns 
 */
module.exports.getDashboardMainConfiguration = async(report_id, db, user, authorization)=>{
    try{
        
        let configuration = await mongo.client
        .db(db)
        .collection("m_project_dashboard_details")
        .find({ report_id: report_id, is_active: true })
        .toArray();
  
      if (configuration && configuration.length > 0) {
        return Promise.resolve({
          err: false,
          msg: "Configurations Retrieved Successfully!",
          data: configuration[0],
        });
      } else {
        return Promise.resolve({
          err: true,
          msg: "Configurations Not Found!",
        });
      }
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
}


/**
 * 
 * @param {*} report_id
 * @param {*} sub_tab_id
 * @param {*} db 
 * @param {*} user
 * @param {*} authorization 
 * <AUTHOR> Rajendran
 * @description Get dashboard configurations
 * @returns 
 */
module.exports.getDashboardWidgets = async(report_id, sub_tab_id,db, user, authorization)=>{
  try{
      
      let configuration = await mongo.client
      .db(db)
      .collection("m_project_dashboard_widgets")
      .find({ report_id: report_id, sub_tab_id: sub_tab_id,is_active: true })
      .toArray();

    if (configuration && configuration.length > 0) {
      return Promise.resolve({
        err: false,
        msg: "Widgets Data Retrived",
        data: configuration,
      });
    } else {
      return Promise.resolve({
        err: true,
        msg: "No Widgets Found",
      });
    }
  }
  catch(err){
      logger.info(err)
      return Promise.resolve([])
  }
}

/**
 * @description Fetching Priority Master Data
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getPriorityMasterData = async(db, user, authorization)=>{
  try{
      
      let data = await rpool(`SELECT id, priority AS label, color FROM ${db}.m_rm_request_priority WHERE is_active = 1; `);

      for(let item of data){
        item['key'] = item['label']
      }

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Get Request Data
 * @param {*} db 
 * @returns 
 */
module.exports.getRequestData = async(db,start_date,end_date,status,filterQuery)=>{
  try{

      let filter_query = ''
      if(filterQuery && filterQuery != ''){
        filter_query = filterQuery
      }

      let status_query = '';
      if(status && status.length > 0){
        status_query = ` AND trr.status IN (?) `
      }
      
      let data = await rpool(`SELECT trr.*, tpi.service_type_id, trjd.need_client_interview
        FROM ${db}.t_rm_request AS trr
        INNER JOIN ${db}.t_project_item AS tpi ON tpi.id = trr.project_id AND tpi.is_active = 1
        INNER JOIN ${db}.t_project_header AS tph ON tph.id = tpi.project_id AND tph.is_active = 1
        INNER JOIN ${db}.t_rm_job_detail AS trjd ON trjd.request_id = trr.request_id AND trjd.is_active = 1
        LEFT JOIN ${db}.m_legal_entity AS mle ON mle.entity_id = trjd.entity AND mle.is_active = 1
        LEFT JOIN ${db}.m_e360_org_region AS meor ON meor.id = trjd.region AND meor.is_active = 1
        LEFT JOIN ${db}.m_e360_division AS med ON med.id = trjd.division AND med.is_active = 1
        LEFT JOIN ${db}.m_e360_sub_division AS mesd ON mesd.id = trjd.sub_division AND mesd.is_active = 1
        WHERE trr.is_active = 1 AND DATE(trr.created_on) BETWEEN ? AND  ? ${status_query} ${filter_query} `,
        [start_date,end_date, status]
      );
      
      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Fetching the Request Skill Data
 * @param {*} db 
 * @param {*} start_date 
 * @param {*} end_date 
 * @param {*} status 
 * @returns 
 */
module.exports.getRequestSkillData = async(db,start_date,end_date,status)=>{
  try{
      
      let data = await rpool(`SELECT trsd.request_id,trr.priority,trsd.skill AS skill_id,ms.long_description AS skill_name
        FROM ${db}.t_rm_request AS trr
        LEFT JOIN ${db}.t_rm_skill_detail AS trsd ON trsd.request_id = trr.request_id
        INNER JOIN ${db}.m_skill AS ms ON trsd.skill = ms.skill_id AND ms.is_active = 1
        WHERE trr.is_active = 1 AND DATE(trr.created_on) BETWEEN ? AND  ? AND STATUS IN (?) `,[start_date,end_date, status]);
      
      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Get Skill Master Data
 * @param {*} db 
 * @returns 
 */
module.exports.getSkillMasterData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT skill_id AS skill_id, long_description AS skill_name FROM ${db}.m_skill WHERE is_active = 1; `);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * 
 * @param {*} db 
 * @returns 
 */
module.exports.getRequestStatusData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT id, status AS name, color FROM ${db}.m_rm_request_status WHERE is_active = 1; `);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Master Request Status  Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Master Request Status Data Found'
      })
  }
}

/**
 * @description Get All Request Data
 * @param {*} db 
 * @returns 
 */
module.exports.getAllRequestData = async(db,start_date,end_date,filterQuery)=>{
  try{

      let filter_query = ''
      if(filterQuery && filterQuery != ''){
        filter_query = filterQuery
      }
      
      let data = await rpool(`SELECT trr.request_id, trr.status 
        FROM ${db}.t_rm_request AS trr
        INNER JOIN ${db}.t_project_item AS tpi ON tpi.id = trr.project_id AND tpi.is_active = 1
        INNER JOIN ${db}.t_project_header AS tph ON tph.id = tpi.project_id AND tph.is_active = 1
        INNER JOIN ${db}.t_rm_job_detail AS trjd ON trjd.request_id = trr.request_id AND trjd.is_active = 1
        LEFT JOIN ${db}.m_legal_entity AS mle ON mle.entity_id = trjd.entity AND mle.is_active = 1
        LEFT JOIN ${db}.m_e360_org_region AS meor ON meor.id = trjd.region AND meor.is_active = 1
        LEFT JOIN ${db}.m_e360_division AS med ON med.id = trjd.division AND med.is_active = 1
        LEFT JOIN ${db}.m_e360_sub_division AS mesd ON mesd.id = trjd.sub_division AND mesd.is_active = 1
        WHERE trr.is_active = 1 AND DATE(trr.created_on) BETWEEN ? AND  ? ${filter_query} ; `,[start_date,end_date]);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Fetching Customer Data
 * @param {*} db 
 * @returns 
 */
module.exports.getCustomerData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT customer_id AS id, customer_name AS name FROM ${db}.m_customer_master WHERE is_active = 1; `,[]);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Fetching Project Type Data
 * @param {*} db 
 * @returns 
 */
module.exports.getProjectTypeData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT service_type_id AS id, service_type_name AS name FROM ${db}.m_service_type WHERE is_active = 1; `,[]);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Fetching the Project Role Data
 * @param {*} db 
 * @returns 
 */
module.exports.getProjectRoleData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT id, name FROM ${db}.m_project_role_master WHERE is_active = 1; `,[]);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Getting Total Request Data
 * @param {*} db 
 * @param {*} start_date 
 * @param {*} end_date  
 * @returns 
 */
module.exports.getTotalRequestData = async(db,start_date,end_date)=>{
  try{
      
      let data = await rpool(`SELECT trr.*
        FROM ${db}.t_rm_request AS trr
        INNER JOIN ${db}.m_rm_request_status AS mrrs ON mrrs.id = trr.status AND mrrs.is_active = 1
        WHERE trr.is_active = 1 AND DATE(trr.created_on) BETWEEN ? AND  ? `,
        [start_date,end_date]
      );
      
      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Operational Model Master Data
 * @param {*} db 
 * @returns 
 */
module.exports.getOperationalModelData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT id,location AS name FROM ${db}.m_e360_project_location WHERE is_active = 1; `,[]);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}


/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getDeployedHC = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT COUNT(DISTINCT associate_id) AS head_count
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        WHERE tis.is_active = 1 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result[0]?.head_count
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getProjectEmployeesCount = async(db)=>{
  try{
      
      let data = `
        SELECT COUNT(DISTINCT associate_id) AS employee_count FROM ${db}.t_e360_time_details WHERE is_active = 1 AND  
        project_available_hours IS NOT NULL AND project_available_hours>0
        AND start_date <= "${moment().format("YYYY-MM-DD")}" AND end_date >= "${moment().format("YYYY-MM-DD")}"
        `;

        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result[0]?.employee_count
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getAllocatedEmployeeStatus = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT 
        tted.employment_status, COUNT(DISTINCT tis.associate_id) AS value, mees.status_name AS label
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        LEFT JOIN ${db}.t_e360_employment_details tted ON tted.associate_id = tis.associate_id AND tted.start_date <= "${moment().format("YYYY-MM-DD")}" AND tted.end_date >= "${moment().format("YYYY-MM-DD")}" 
        LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = tted.employment_status
        WHERE tis.is_active = 1 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        data = data + ` GROUP BY tted.employment_status`


        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}


/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.deployedHeadCountBasedOnEntity = async (db, filter_query, start_date, end_date) => {
  try {
    let data = `
    WITH RankedAllocations AS (
    SELECT 
      tis.associate_id,
      tis.entity_id,
      tis.billable,
      tis.split_percentage,
      ROW_NUMBER() OVER (
          PARTITION BY tis.associate_id
          ORDER BY 
              tis.billable DESC,    
              tis.identity DESC,         
              tis.split_percentage DESC,     
              tis.entity_id ASC             
      ) AS rank_row
    FROM ${db}.t_internal_stakeholders tis
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
    LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
    WHERE tis.is_active = 1
    AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
    AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
    AND tis.end_date >= '${start_date}' 
    AND tis.start_date <= '${end_date}'
`;

    if (filter_query) {
      data = data + filter_query; // Append additional filter conditions if any
    }

    // Add the closing parenthesis for the CTE
    data = data + `) `;

    let final_query = `
    SELECT 
    ra.entity_id AS id,
    CASE WHEN mle.entity_name IS NULL THEN 'Unassigned' ELSE mle.entity_name END AS label,
    COUNT(DISTINCT ra.associate_id) AS count
    FROM RankedAllocations ra
    LEFT JOIN ${db}.m_legal_entity mle ON ra.entity_id = mle.entity_id
    WHERE ra.rank_row = 1 
    GROUP BY ra.entity_id;
    `;

    // Combine CTE with the main query
    data = data + final_query;

    let result = await rpool(data);


    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch (err) {
    logger.info(err)
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    })
  }
}



/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getBillability = async (db, filter_query, start_date, end_date) => {
  try {
    let data = `
    WITH RankedAllocations AS (
    SELECT 
      tis.associate_id,
      tis.identity,
      tis.billable,
      tis.split_percentage,
      ROW_NUMBER() OVER (
          PARTITION BY tis.associate_id
          ORDER BY 
              tis.billable DESC,    
              tis.identity DESC,         
              tis.split_percentage DESC,     
              tis.entity_id ASC             
      ) AS rank_row
    FROM ${db}.t_internal_stakeholders tis
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
    LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
    WHERE tis.is_active = 1
    AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
    AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
    AND tis.end_date >= '${start_date}' 
    AND tis.start_date <= '${end_date}'
`;

    if (filter_query) {
      data = data + filter_query; // Append additional filter conditions if any
    }

    // Add the closing parenthesis for the CTE
    data = data + `) `;

    let final_query = `
    SELECT 
    COUNT(*) AS head_count,
    COUNT(DISTINCT CASE WHEN ra.billable = 1 THEN ra.associate_id END) AS billable,
    COUNT(DISTINCT CASE WHEN ra.billable = 0 AND ra.identity = 2 THEN ra.associate_id END) AS shadow,
    COUNT(DISTINCT CASE WHEN ra.billable = 0 AND ra.identity != 2 THEN ra.associate_id END) AS non_billable
    FROM RankedAllocations ra
    WHERE ra.rank_row = 1;`;

    // Combine CTE with the main query
    data = data + final_query;

    let result = await rpool(data);


    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result[0]
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch (err) {
    logger.info(err)
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    })
  }
}

/*
* @description Deployment Head Count
* @param {*} db 
* @returns 
*/
module.exports.getBillabilityBasedOnRegion = async (db, filter_query, start_date, end_date) => {
 try {
   let data = `
   WITH RankedAllocations AS (
   SELECT 
        tis.associate_id,
        tpi.p_and_l_id,
        tis.billable,
        tis.split_percentage,
     ROW_NUMBER() OVER (
         PARTITION BY tis.associate_id
         ORDER BY 
             tis.billable DESC,    
             tis.identity DESC,         
             tis.split_percentage DESC,     
             tis.entity_id ASC             
     ) AS rank_row
   FROM ${db}.t_internal_stakeholders tis
   LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
   LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
   WHERE tis.is_active = 1
  AND tpi.p_and_l_id IN 
  (SELECT mpal.pl_id AS id
    FROM ${db}.m_p_and_l mpal 
    WHERE mpal.pl_id NOT IN (SELECT DISTINCT mpal1.rollup_id FROM ${db}.m_p_and_l mpal1 WHERE mpal1.is_active = 1 AND mpal1.rollup_id IS NOT NULL) 
    AND mpal.is_active = 1)
   AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
   AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
   AND tis.end_date >= '${start_date}' 
   AND tis.start_date <= '${end_date}'
`;

   if (filter_query) {
     data = data + filter_query; // Append additional filter conditions if any
   }

   // Add the closing parenthesis for the CTE
   data = data + `) `;

   let final_query = `
    SELECT 
    ra.p_and_l_id AS id,
    mpal.description AS label,
    COUNT(DISTINCT ra.associate_id) AS value
    FROM RankedAllocations ra
    LEFT JOIN ${db}.m_p_and_l mpal ON ra.p_and_l_id = mpal.pl_id
    WHERE ra.rank_row = 1 
    GROUP BY ra.p_and_l_id;
   `;

   // Combine CTE with the main query
   data = data + final_query;

   let result = await rpool(data);


   if (result && result.length > 0) {
     return Promise.resolve({
       err: false,
       messType: 'S',
       data: result
     });
   }
   else {
     return Promise.resolve({
       err: false,
       messType: 'S',
       data: [],
       message: 'No Data Found'
     });
   }
 }
 catch (err) {
   logger.info(err)
   return Promise.reject({
     err: true,
     messType: 'E',
     data: [],
     message: 'No Data Found'
   })
 }
}

module.exports.getBillabilityBasedOnLocation = async (db, filter_query, start_date, end_date) => {
 try {
   let data = `
   WITH RankedAllocations AS (
   SELECT 
        tis.associate_id,
        pmc.name AS work_city,
        tis.billable,
        tis.split_percentage,
     ROW_NUMBER() OVER (
         PARTITION BY tis.associate_id
         ORDER BY 
             tis.billable DESC,    
             tis.identity DESC,         
             tis.split_percentage DESC,     
             tis.entity_id ASC             
     ) AS rank_row
   FROM ${db}.t_internal_stakeholders tis
   LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
   LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
   LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
   WHERE tis.is_active = 1
  
   AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
   AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
   AND tis.end_date >= '${start_date}' 
   AND tis.start_date <= '${end_date}'
`;

   if (filter_query) {
     data = data + filter_query; // Append additional filter conditions if any
   }

   // Add the closing parenthesis for the CTE
   data = data + `) `;

   let final_query = `
    SELECT 
    COALESCE(ra.work_city, 'Unassigned') AS label,
    COUNT(DISTINCT ra.associate_id) AS value
    FROM RankedAllocations ra
    WHERE ra.rank_row = 1
    GROUP BY ra.work_city;
   `;

   // Combine CTE with the main query
   data = data + final_query;

   let result = await rpool(data);

   logger.info("bar2",result)


   if (result && result.length > 0) {
     return Promise.resolve({
       err: false,
       messType: 'S',
       data: result
     });
   }
   else {
     return Promise.resolve({
       err: false,
       messType: 'S',
       data: [],
       message: 'No Data Found'
     });
   }
 }
 catch (err) {
   logger.info(err)
   return Promise.reject({
     err: true,
     messType: 'E',
     data: [],
     message: 'No Data Found'
   })
 }
}


/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getNonBillableStatus = async (db, filter_query, start_date, end_date) => {
  try {
    let data = `
      WITH RankedAllocations AS (
        SELECT 
          tis.associate_id,
          tis.non_billable_category,
          tis.billable,
          tis.identity,
          ROW_NUMBER() OVER (
            PARTITION BY tis.associate_id
            ORDER BY 
              tis.billable DESC,    
              tis.identity DESC,         
              tis.split_percentage DESC,     
              tis.entity_id ASC             
          ) AS rank_row
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        WHERE tis.is_active = 1
          AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
          AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
          AND tis.end_date >= '${start_date}' 
          AND tis.start_date <= '${end_date}'
    `;

    if (filter_query) {
      data += filter_query;
    }

    data += `),
    PrimaryRanked AS (
      SELECT * FROM RankedAllocations WHERE rank_row = 1
    )

    SELECT 
      CASE 
        WHEN mpnbc.name IS NULL THEN 'Unassigned'
        ELSE mpnbc.name
      END AS label,
      COUNT(DISTINCT pr.associate_id) AS count,
      pr.non_billable_category
    FROM PrimaryRanked pr
    LEFT JOIN ${db}.m_project_non_billable_category mpnbc 
      ON mpnbc.id = pr.non_billable_category
    WHERE pr.billable = 0 AND pr.identity != 2
    GROUP BY label
    ORDER BY label;
    `;

    let result = await rpool(data);

    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    } else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  } catch (err) {
    logger.info(err);
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    });
  }
}


/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getAllocatedEmployeePractice = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT
        tted.department, COUNT(DISTINCT tis.associate_id) AS value, CASE WHEN mees.name IS NULL THEN 'Unassigned' ELSE mees.name END AS label
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        LEFT JOIN ${db}.t_e360_org_details tted ON tted.associate_id = tis.associate_id AND tted.start_date <= "${moment().format("YYYY-MM-DD")}" AND tted.end_date >= "${moment().format("YYYY-MM-DD")}" AND tted.is_active = 1
        LEFT JOIN ${db}.m_organization mees ON mees.id = tted.department
        WHERE tis.is_active = 1 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        data = data + ` GROUP BY label`


        let result = await rpool(data);

        logger.info("bar1",result)

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}


/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getAllocatedEmployeeLevel = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT
        tted.level, COUNT(DISTINCT tis.associate_id) AS value, CASE WHEN mees.level IS NULL THEN 'Unassigned' ELSE mees.level END AS label
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        LEFT JOIN ${db}.t_e360_org_details tted ON tted.associate_id = tis.associate_id AND tted.start_date <= "${moment().format("YYYY-MM-DD")}" AND tted.end_date >= "${moment().format("YYYY-MM-DD")}" AND tted.is_active = 1
        LEFT JOIN ${db}.m_e360_org_level mees ON mees.id = tted.level
        WHERE tis.is_active = 1 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        data = data + ` GROUP BY label`


        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}



/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getSkillCountBasedOnAllocation = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT
        tesd.name AS id, ms.skill_description AS label,COUNT(DISTINCT tis.associate_id) AS value  
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        LEFT JOIN ${db}.t_e360_skill_details tesd ON tesd.associate_id = tis.associate_id AND tesd.is_active = 1 AND tesd.start_date <= "${moment().format("YYYY-MM-DD")}" AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}"
	      LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name 
        WHERE tis.is_active = 1  AND tesd.name IS NOT NULL 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        data = data + ` GROUP BY tesd.name`


        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description get Status Master Data
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getStatusMasterData = async(db, user, authorization)=>{
  try{
      
      let data = await rpool(`SELECT id, status AS label, color FROM ${db}.m_rm_request_status WHERE is_active = 1; `);

      for(let item of data){
        item['key'] = item['label']
      }

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Booking Type Master Data
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getBookingTypeData = async(db, user, authorization)=>{
  try{
      
      let data = await rpool(`SELECT id, booking_type AS label, color FROM ${db}.m_rm_booking_type WHERE is_active = 1; `);

      for(let item of data){
        item['key'] = item['label']
      }

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Request Context Master Data
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @returns 
 */
module.exports.getRequestContextData = async(db, user, authorization)=>{
  try{
      
      let data = await rpool(`SELECT id, request_context AS label, color FROM ${db}.m_rm_request_context WHERE is_active = 1; `);

      for(let item of data){
        item['key'] = item['label']
      }

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}


/**
 * 
 * @param {*} db 
 * @param {*} filter_query 
 * @param {*} start_date 
 * @param {*} end_date 
 * @description Get Non Billable Deployment Aging Request
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getNonBillableAging = async(db, filter_query, start_date, end_date)=>{
  try{
    let resultOutput = [
      {
        "count": 0,
        "label": "<90 days",
        
      },
      {
        "count": 0,
        "label": "90 - 120 days",
      
      },
      {
        "count": 0,
        "label": "120 - 180 days",
      
      },
      {
        "count": 0,
        "label": ">180 days",
      }
    ]

    let data = `

    SELECT 
     CASE 
        WHEN DATEDIFF(CURRENT_DATE, tis.start_date) > 180 THEN '>180 days'
        WHEN DATEDIFF(CURRENT_DATE, tis.start_date) BETWEEN 120 AND 180 THEN '120 - 180 days'
        WHEN DATEDIFF(CURRENT_DATE, tis.start_date) BETWEEN 90 AND 119 THEN '90 - 120 days'
        ELSE '<90 days'
    END AS label,
    COUNT(*) AS count
    FROM ${db}.t_internal_stakeholders tis
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
    LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
    WHERE tis.is_active = 1 
    AND tis.billable = 0 AND (tis.identity IS NULL OR tis.identity = 1)
    AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
    AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
    AND tis.end_date >= '${start_date}' 
    AND tis.start_date <= '${end_date}'
  
     `;

    if(filter_query){
      data = data + filter_query
    }

    data = data + ` GROUP BY label ORDER BY label DESC`

    let result = await rpool(data);


    const resultMap = resultOutput.reduce((acc, data) => 
    {
      acc[data.label] = data.count; 
      return acc;
    }, {});

    
    logger.info(resultMap)
    for (let data of resultOutput) 
    {
        if (resultMap[data.label] !== undefined) 
        {
          data['count'] = resultMap[data.label];
        }
    }

  if(result && result.length > 0){
    return Promise.resolve({
      err: false,
      messType: 'S',
      data: result
    });
  }
  else{
    return Promise.resolve({
      err: false,
      messType: 'S',
      data: [],
      message: 'No Data Found'
    });
  }
  }
  catch(err){
    logger.info(err)
    return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
    })
  }
}

/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getEmpoloyeeDiversity = async(db, filter_query, startDate, endDate)=>{
  try{
      let ACTUAL_TIMESTAMP = moment().format("YYYY-MM-DD");
       let count_query = `SELECT
			COUNT(teec.associate_id) AS value,COALESCE(et.gender_name, 'Unassigned') AS label,
			RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
			teec.oid AS associate_oid,
			tecd.work_email AS email_id,
			tecd.contact_number AS phone_number,
			teed.date_of_joining,
			mees.status_name AS employee_status,
			mle.entity_name,
			med.division_name,
			mesd.sub_division_name,
			mle2.entity_name AS allocated_entity,
			med2.division_name AS allocated_division,
			mesd2.sub_division_name AS allocated_sub_division,
			meor.region AS region_name,
			mr.name AS position_name,
			mtol.location AS employee_work_location,
			mews.schedule_description AS employee_work_schedule,
			pmews.name AS project_shift,
			mehc.name AS employee_holiday_calendar,
			pmehc.name AS project_holiday_calendar,
			mo.name AS department_name,
			tpi.item_name AS project_name,
			tpi.sow_reference_number,
			tpi.profit_center,
			tis.start_date AS start_date,
			tis.end_date AS end_date,
			mprm.name AS project_role,
			tis.id,
			tetd.project_available_hours,
			mr2.name AS quote_position,
			tesd.skill_descriptions,
			IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
			ELSE 'Unassigned'
			END AS STATUS,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
			ELSE 2
			END AS status_id,
			CASE
			 WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            IFNULL(
                (SELECT MAX(end_date) 
                 FROM ${db}.t_internal_stakeholders
                 WHERE associate_id = teec.associate_id
                 AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
        ELSE NULL
    END AS last_allocation_date,
    CASE
        WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            DATEDIFF(CURDATE(), 
                IFNULL(
                    (SELECT MAX(end_date) 
                     FROM ${db}.t_internal_stakeholders
                     WHERE associate_id = teec.associate_id
                     AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
		)
        ELSE NULL
    END AS aging,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
			ELSE '#FFBD3D'
			END AS bg_color,
			'#FFFFFF' AS font_color,
			pms.shift_name AS work_mode,
			mrwp.name AS work_premisis,
			emd.name AS employee_job,
			pmc.name AS work_city
			FROM ${db}.t_e360_employee_central teec
			INNER JOIN ${db}.t_e360_employment_details teed
			ON teed.associate_id = teec.associate_id
			AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teed.is_active = 1
			AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
			LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
			LEFT JOIN ${db}.t_e360_personal_details tepd
			ON tepd.associate_id = teed.associate_id
			AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tepd.is_active = 1
			LEFT JOIN ${db}.t_e360_org_details teod
			ON teod.associate_id = teec.associate_id
			AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teod.is_active = 1
			LEFT JOIN ${db}.t_e360_contact_details tecd
			ON tecd.associate_id = teec.associate_id
			AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tecd.is_active = 1
			LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
			LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
			LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
      LEFT JOIN ${db}.m_e360_gender et ON et.id = tepd.gender
			LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
      LEFT JOIN ${db}.m_e360_org_level mde ON mde.id = teod.level
			LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
			LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
			LEFT JOIN ${db}.t_e360_time_details tetd
			ON tetd.associate_id = teec.associate_id
			AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tetd.is_active = 1
			LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
			LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
			LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
			LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
			LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
			AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
			LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
			LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
			LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
			LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
			LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
			LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
			LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
			LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
			LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
			LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
			LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
			LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
			LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
			LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
			LEFT JOIN (
					SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
					FROM ${db}.t_e360_skill_details tesd 
					LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
					WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.is_active = 1
					GROUP BY tesd.associate_id
					) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;
        if(filter_query){
          count_query = count_query + filter_query
        }

        count_query = count_query + ` GROUP BY et.id ORDER BY et.id DESC`
    
        let result = await rpool(count_query);
    
      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getEmployeeBasedOnElm = async (db, filter_query, startDate, endDate) => {
  try {
      let ACTUAL_TIMESTAMP = moment().format("YYYY-MM-DD");
      let count_query = `SELECT
			COUNT(teec.associate_id) AS value,COALESCE(emd.employee_class, 'Unassigned') AS label,
			RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
			teec.oid AS associate_oid,
			tecd.work_email AS email_id,
			tecd.contact_number AS phone_number,
			teed.date_of_joining,
			mees.status_name AS employee_status,
			mle.entity_name,
			med.division_name,
			mesd.sub_division_name,
			mle2.entity_name AS allocated_entity,
			med2.division_name AS allocated_division,
			mesd2.sub_division_name AS allocated_sub_division,
			meor.region AS region_name,
			mr.name AS position_name,
			mtol.location AS employee_work_location,
			mews.schedule_description AS employee_work_schedule,
			pmews.name AS project_shift,
			mehc.name AS employee_holiday_calendar,
			pmehc.name AS project_holiday_calendar,
			mo.name AS department_name,
			tpi.item_name AS project_name,
			tpi.sow_reference_number,
			tpi.profit_center,
			tis.start_date AS start_date,
			tis.end_date AS end_date,
			mprm.name AS project_role,
			tis.id,
			tetd.project_available_hours,
			mr2.name AS quote_position,
			tesd.skill_descriptions,
			IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
			ELSE 'Unassigned'
			END AS STATUS,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
			ELSE 2
			END AS status_id,
			CASE
			 WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            IFNULL(
                (SELECT MAX(end_date) 
                 FROM ${db}.t_internal_stakeholders
                 WHERE associate_id = teec.associate_id
                 AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
        ELSE NULL
    END AS last_allocation_date,
    CASE
        WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            DATEDIFF(CURDATE(), 
                IFNULL(
                    (SELECT MAX(end_date) 
                     FROM ${db}.t_internal_stakeholders
                     WHERE associate_id = teec.associate_id
                     AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
		)
        ELSE NULL
    END AS aging,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
			ELSE '#FFBD3D'
			END AS bg_color,
			'#FFFFFF' AS font_color,
			pms.shift_name AS work_mode,
			mrwp.name AS work_premisis,
			emd.employee_class AS employee_class,
			pmc.name AS work_city
			FROM ${db}.t_e360_employee_central teec
			INNER JOIN ${db}.t_e360_employment_details teed
			ON teed.associate_id = teec.associate_id
			AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teed.is_active = 1
			AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
			LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
			LEFT JOIN ${db}.t_e360_personal_details tepd
			ON tepd.associate_id = teed.associate_id
			AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tepd.is_active = 1
			LEFT JOIN ${db}.t_e360_org_details teod
			ON teod.associate_id = teec.associate_id
			AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teod.is_active = 1
			LEFT JOIN ${db}.t_e360_contact_details tecd
			ON tecd.associate_id = teec.associate_id
			AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tecd.is_active = 1
			LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
			LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
			LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
			LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
      LEFT JOIN ${db}.m_e360_org_level mde ON mde.id = teod.level
			LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
			LEFT JOIN ${db}.m_e360_employee_class emd ON emd.id = tepd.employee_class
			LEFT JOIN ${db}.t_e360_time_details tetd
			ON tetd.associate_id = teec.associate_id
			AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tetd.is_active = 1
			LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
			LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
			LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
			LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
			LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
			AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
			LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
			LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
			LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
			LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
			LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
			LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
			LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
			LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
			LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
			LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
			LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
			LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
			LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
			LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
			LEFT JOIN (
					SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
					FROM ${db}.t_e360_skill_details tesd 
					LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
					WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.is_active = 1
					GROUP BY tesd.associate_id
					) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;
        if(filter_query){
          count_query = count_query + filter_query
        }
    

        count_query = count_query + ` GROUP BY emd.id ORDER BY emd.id DESC`
    
        let result = await rpool(count_query);


    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch (err) {
    logger.info(err)
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    })
  }
}

/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getEmployeeBasedOnBench = async(db, filter_query, startDate, endDate)=>{
  try{      
    let query = `SELECT
			COUNT(teec.associate_id) AS head_count,
			RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
			teec.oid AS associate_oid,
			tecd.work_email AS email_id,
			tecd.contact_number AS phone_number,
			teed.date_of_joining,
			mees.status_name AS employee_status,
			mle.entity_name,
			med.division_name,
			mesd.sub_division_name,
			mle2.entity_name AS allocated_entity,
			med2.division_name AS allocated_division,
			mesd2.sub_division_name AS allocated_sub_division,
			meor.region AS region_name,
			mr.name AS position_name,
			mtol.location AS employee_work_location,
			mews.schedule_description AS employee_work_schedule,
			pmews.name AS project_shift,
			mehc.name AS employee_holiday_calendar,
			pmehc.name AS project_holiday_calendar,
			mo.name AS department_name,
			tpi.item_name AS project_name,
			tpi.sow_reference_number,
			tpi.profit_center,
			tis.start_date AS start_date,
			tis.end_date AS end_date,
			mprm.name AS project_role,
			tis.id,
			tetd.project_available_hours,
			mr2.name AS quote_position,
			tesd.skill_descriptions,
			IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
			ELSE 'Unassigned'
			END AS STATUS,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
			ELSE 2
			END AS status_id,
			CASE
			 WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            IFNULL(
                (SELECT MAX(end_date) 
                 FROM ${db}.t_internal_stakeholders
                 WHERE associate_id = teec.associate_id
                 AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
        ELSE NULL
    END AS last_allocation_date,
    CASE
        WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            DATEDIFF(CURDATE(), 
                IFNULL(
                    (SELECT MAX(end_date) 
                     FROM ${db}.t_internal_stakeholders
                     WHERE associate_id = teec.associate_id
                     AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
		)
        ELSE NULL
    END AS aging,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
			ELSE '#FFBD3D'
			END AS bg_color,
			'#FFFFFF' AS font_color,
			pms.shift_name AS work_mode,
			mrwp.name AS work_premisis,
			emd.name AS employee_job,
			pmc.name AS work_city
			FROM ${db}.t_e360_employee_central teec
			INNER JOIN ${db}.t_e360_employment_details teed
			ON teed.associate_id = teec.associate_id
			AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teed.is_active = 1
			AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
			LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
			LEFT JOIN ${db}.t_e360_personal_details tepd
			ON tepd.associate_id = teed.associate_id
			AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tepd.is_active = 1
			LEFT JOIN ${db}.t_e360_org_details teod
			ON teod.associate_id = teec.associate_id
			AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teod.is_active = 1
			LEFT JOIN ${db}.t_e360_contact_details tecd
			ON tecd.associate_id = teec.associate_id
			AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tecd.is_active = 1
			LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
			LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
			LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
			LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
			LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
			LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
			LEFT JOIN ${db}.t_e360_time_details tetd
			ON tetd.associate_id = teec.associate_id
			AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tetd.is_active = 1
			LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
			LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
			LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
			LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
			LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
			AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
			LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
			LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
			LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
			LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
			LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
			LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
			LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
			LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
			LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
			LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
			LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
			LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
			LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
			LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
			LEFT JOIN (
					SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
					FROM ${db}.t_e360_skill_details tesd 
					LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
					WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.is_active = 1
					GROUP BY tesd.associate_id
					) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;
       if(filter_query){
        query = query + filter_query
      }
       let result = await rpool(query);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result[0]?.head_count
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

module.exports.getEmployeeDeployableVsNonDeployable = async(db, filter_query, start_date, end_date)=>{
  try{
    let ACTUAL_TIMESTAMP = moment().format("YYYY-MM-DD");
    let count_query = `SELECT 
      COUNT(DISTINCT ec.associate_id) AS value,
      CASE 
          WHEN tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 THEN 'Deployable'
          ELSE 'Non-Deployable'
      END AS label
  FROM ${db}.t_e360_employee_central ec
      INNER JOIN ${db}.t_e360_employment_details ed1 ON ed1.is_active = 1 
          AND ed1.associate_id = ec.associate_id 
          AND ed1.start_date <= ? 
          AND ? <= ed1.end_date
      INNER JOIN ${db}.m_e360_employment_type emt ON emt.id = ed1.employment_type      
      INNER JOIN ${db}.t_e360_personal_details ed ON ed.is_active = 1 
          AND ed.associate_id = ec.associate_id 
          AND ed.start_date <= ? 
          AND ? <= ed.end_date
      INNER JOIN ${db}.t_e360_org_details teod ON teod.is_active = 1 
          AND teod.associate_id = ec.associate_id 
          AND teod.start_date <= ? 
          AND ? <= teod.end_date
      INNER JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
      INNER JOIN ${db}.m_e360_division med ON med.id = teod.division
      INNER JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
      INNER JOIN ${db}.t_e360_time_details tetd ON tetd.associate_id = ec.associate_id 
          AND tetd.is_active = 1 
          AND tetd.start_date <= ? 
          AND ? <= tetd.end_date
  WHERE 
      ec.is_active = 1 
      AND ec.start_date <= ? 
      AND ec.end_date >= ? 
      AND ed1.employment_status = 1
  `;

    if (filter_query) {
      count_query = count_query + filter_query;
    }

    count_query = count_query + ` GROUP BY label ORDER BY label DESC`;


    let result = await rpool(count_query, [
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP,
      ACTUAL_TIMESTAMP

    ]);

    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

module.exports.getEmployeeLevel = async (db, filter_query, startDate, endDate) => {
  try {
      let ACTUAL_TIMESTAMP = moment().format("YYYY-MM-DD");
      let count_query = `SELECT
			COUNT(teec.associate_id) AS value,COALESCE(mde.level, 'Unassigned') AS label,
			RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
			teec.oid AS associate_oid,
			tecd.work_email AS email_id,
			tecd.contact_number AS phone_number,
			teed.date_of_joining,
			mees.status_name AS employee_status,
			mle.entity_name,
			med.division_name,
			mesd.sub_division_name,
			mle2.entity_name AS allocated_entity,
			med2.division_name AS allocated_division,
			mesd2.sub_division_name AS allocated_sub_division,
			meor.region AS region_name,
			mr.name AS position_name,
			mtol.location AS employee_work_location,
			mews.schedule_description AS employee_work_schedule,
			pmews.name AS project_shift,
			mehc.name AS employee_holiday_calendar,
			pmehc.name AS project_holiday_calendar,
			mo.name AS department_name,
			tpi.item_name AS project_name,
			tpi.sow_reference_number,
			tpi.profit_center,
			tis.start_date AS start_date,
			tis.end_date AS end_date,
			mprm.name AS project_role,
			tis.id,
			tetd.project_available_hours,
			mr2.name AS quote_position,
			tesd.skill_descriptions,
			IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
			ELSE 'Unassigned'
			END AS STATUS,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
			ELSE 2
			END AS status_id,
			CASE
			 WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            IFNULL(
                (SELECT MAX(end_date) 
                 FROM ${db}.t_internal_stakeholders
                 WHERE associate_id = teec.associate_id
                 AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
        ELSE NULL
    END AS last_allocation_date,
    CASE
        WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
            DATEDIFF(CURDATE(), 
                IFNULL(
                    (SELECT MAX(end_date) 
                     FROM ${db}.t_internal_stakeholders
                     WHERE associate_id = teec.associate_id
                     AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
		)
        ELSE NULL
    END AS aging,
			CASE
			WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
			ELSE '#FFBD3D'
			END AS bg_color,
			'#FFFFFF' AS font_color,
			pms.shift_name AS work_mode,
			mrwp.name AS work_premisis,
			emd.name AS employee_job,
			pmc.name AS work_city
			FROM ${db}.t_e360_employee_central teec
			INNER JOIN ${db}.t_e360_employment_details teed
			ON teed.associate_id = teec.associate_id
			AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teed.is_active = 1
			AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
			LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
			LEFT JOIN ${db}.t_e360_personal_details tepd
			ON tepd.associate_id = teed.associate_id
			AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tepd.is_active = 1
			LEFT JOIN ${db}.t_e360_org_details teod
			ON teod.associate_id = teec.associate_id
			AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND teod.is_active = 1
			LEFT JOIN ${db}.t_e360_contact_details tecd
			ON tecd.associate_id = teec.associate_id
			AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tecd.is_active = 1
			LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
			LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
			LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
			LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
      LEFT JOIN ${db}.m_e360_org_level mde ON mde.id = teod.level
			LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
			LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
			LEFT JOIN ${db}.t_e360_time_details tetd
			ON tetd.associate_id = teec.associate_id
			AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tetd.is_active = 1
			LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
			LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
			LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
			LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
			LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
			AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
			LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
			LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
			LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
			LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
			LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
			LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
			LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
			LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
			LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
			LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
			LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
			LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
			LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
			LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
			LEFT JOIN (
					SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
					FROM ${db}.t_e360_skill_details tesd 
					LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
					WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
					AND tesd.is_active = 1
					GROUP BY tesd.associate_id
					) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;
        if(filter_query){
          count_query = count_query + filter_query
        }

        count_query = count_query + ` GROUP BY mde.id ORDER BY mde.id DESC`
    
        let result = await rpool(count_query)


    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch (err) {
    logger.info(err)
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    })
  }
}
module.exports.getEmployeeDepartment = async (db, filter_query, startDate, endDate) => {
  try {
    let count_query = `SELECT
    COUNT(teec.associate_id) AS value,COALESCE(mde.name, 'unassigned') AS label,
    RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
    teec.oid AS associate_oid,
    tecd.work_email AS email_id,
    tecd.contact_number AS phone_number,
    teed.date_of_joining,
    mees.status_name AS employee_status,
    mle.entity_name,
    med.division_name,
    mesd.sub_division_name,
    mle2.entity_name AS allocated_entity,
    med2.division_name AS allocated_division,
    mesd2.sub_division_name AS allocated_sub_division,
    meor.region AS region_name,
    mr.name AS position_name,
    mtol.location AS employee_work_location,
    mews.schedule_description AS employee_work_schedule,
    pmews.name AS project_shift,
    mehc.name AS employee_holiday_calendar,
    pmehc.name AS project_holiday_calendar,
    mo.name AS department_name,
    tpi.item_name AS project_name,
    tpi.sow_reference_number,
    tpi.profit_center,
    tis.start_date AS start_date,
    tis.end_date AS end_date,
    mprm.name AS project_role,
    tis.id,
    tetd.project_available_hours,
    mr2.name AS quote_position,
    tesd.skill_descriptions,
    IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
    ELSE 'Unassigned'
    END AS STATUS,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
    ELSE 2
    END AS status_id,
    CASE
     WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          IFNULL(
              (SELECT MAX(end_date) 
               FROM ${db}.t_internal_stakeholders
               WHERE associate_id = teec.associate_id
               AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
      ELSE NULL
  END AS last_allocation_date,
  CASE
      WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          DATEDIFF(CURDATE(), 
              IFNULL(
                  (SELECT MAX(end_date) 
                   FROM ${db}.t_internal_stakeholders
                   WHERE associate_id = teec.associate_id
                   AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
  )
      ELSE NULL
  END AS aging,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
    ELSE '#FFBD3D'
    END AS bg_color,
    '#FFFFFF' AS font_color,
    pms.shift_name AS work_mode,
    mrwp.name AS work_premisis,
    emd.name AS employee_job,
    pmc.name AS work_city
    FROM ${db}.t_e360_employee_central teec
    INNER JOIN ${db}.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teed.is_active = 1
    AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
    LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
    LEFT JOIN ${db}.t_e360_personal_details tepd
    ON tepd.associate_id = teed.associate_id
    AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tepd.is_active = 1
    LEFT JOIN ${db}.t_e360_org_details teod
    ON teod.associate_id = teec.associate_id
    AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teod.is_active = 1
    LEFT JOIN ${db}.t_e360_contact_details tecd
    ON tecd.associate_id = teec.associate_id
    AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tecd.is_active = 1
    LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
    LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
    LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
    LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
    LEFT JOIN ${db}.m_organization mde ON mde.id = teod.department
    LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
    LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
    LEFT JOIN ${db}.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tetd.is_active = 1
    LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
    LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
    LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
    LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
    LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
    AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
    LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
    LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
    LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
    LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
    LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
    LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
    LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
    LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
    LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
    LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
    LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
    LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
    LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
    LEFT JOIN (
        SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
        FROM ${db}.t_e360_skill_details tesd 
        LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
        WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.is_active = 1
        GROUP BY tesd.associate_id
        ) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;
        if(filter_query){
          count_query = count_query + filter_query
        }

        count_query = count_query + ` GROUP BY mde.id ORDER BY mde.id DESC`
    
        let result = await rpool(count_query)

    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch (err) {
    logger.info(err)
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    })
  }
}

module.exports.getSkillCountBasedOnEmployee = async(db, filter_query, startDate, endDate)=>{
  try{
    let ACTUAL_TIMESTAMP = moment().format("YYYY-MM-DD");
    let count_query = `
   SELECT COUNT(teec.associate_id) AS value,COALESCE(ms.skill_description, 'unassigned') AS label,
    RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
    teec.oid AS associate_oid,
    tecd.work_email AS email_id,
    tecd.contact_number AS phone_number,
    teed.date_of_joining,
    mees.status_name AS employee_status,
    mle.entity_name,
    med.division_name,
    mesd.sub_division_name,
    mle2.entity_name AS allocated_entity,
    med2.division_name AS allocated_division,
    mesd2.sub_division_name AS allocated_sub_division,
    meor.region AS region_name,
    mr.name AS position_name,
    mtol.location AS employee_work_location,
    mews.schedule_description AS employee_work_schedule,
    pmews.name AS project_shift,
    mehc.name AS employee_holiday_calendar,
    pmehc.name AS project_holiday_calendar,
    mo.name AS department_name,
    tpi.item_name AS project_name,
    tpi.sow_reference_number,
    tpi.profit_center,
    tis.start_date AS start_date,
    tis.end_date AS end_date,
    mprm.name AS project_role,
    tis.id,
    tetd.project_available_hours,
    mr2.name AS quote_position,
    tesd.skill_descriptions,
    IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
    ELSE 'Unassigned'
    END AS STATUS,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
    ELSE 2
    END AS status_id,
    CASE
     WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          IFNULL(
              (SELECT MAX(end_date) 
               FROM ${db}.t_internal_stakeholders
               WHERE associate_id = teec.associate_id
               AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
      ELSE NULL
  END AS last_allocation_date,
  CASE
      WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          DATEDIFF(CURDATE(), 
              IFNULL(
                  (SELECT MAX(end_date) 
                   FROM ${db}.t_internal_stakeholders
                   WHERE associate_id = teec.associate_id
                   AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
  )
      ELSE NULL
  END AS aging,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
    ELSE '#FFBD3D'
    END AS bg_color,
    '#FFFFFF' AS font_color,
    pms.shift_name AS work_mode,
    mrwp.name AS work_premisis,
    emd.name AS employee_job,
    pmc.name AS work_city
    FROM ${db}.t_e360_employee_central teec
    INNER JOIN ${db}.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teed.is_active = 1
    AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
    LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
    LEFT JOIN ${db}.t_e360_personal_details tepd
    ON tepd.associate_id = teed.associate_id
    AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tepd.is_active = 1
    LEFT JOIN ${db}.t_e360_org_details teod
    ON teod.associate_id = teec.associate_id
    AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teod.is_active = 1
    LEFT JOIN ${db}.t_e360_contact_details tecd
    ON tecd.associate_id = teec.associate_id
    AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tecd.is_active = 1
    LEFT JOIN ${db}.t_e360_skill_details tesd5 ON tesd5.associate_id = teec.associate_id AND tesd5.is_active = 1 AND tesd5.start_date <= "${moment().format("YYYY-MM-DD")}" AND tesd5.end_date >= "${moment().format("YYYY-MM-DD")}"
	      LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd5.name 
    LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
    LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
    LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
    LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
    LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
    LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
    LEFT JOIN ${db}.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tetd.is_active = 1
    LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
    LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
    LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
    LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
    LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
    AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
    LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
    LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
    LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
    LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
    LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
    LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
    LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
    LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
    LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
    LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
    LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
    LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
    LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
    LEFT JOIN (
        SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
        FROM ${db}.t_e360_skill_details tesd 
        LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
        WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.is_active = 1
        GROUP BY tesd.associate_id
        ) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;
        if(filter_query){
          count_query = count_query + filter_query
        }

        count_query = count_query + ` GROUP BY ms.skill_id ORDER BY ms.skill_id DESC`
    
        let result = await rpool(count_query);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}
module.exports.getEmployeeBasedOnRegion = async (db, filter_query, startDate, endDate) => {
  try {

    let count_query = `SELECT
    COUNT(teec.associate_id) AS value,
    COALESCE(meor.region, 'unassigned') AS label,
    COALESCE(meor.region, 'unassigned') AS region_name,
    RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
    teec.oid AS associate_oid,
    tecd.work_email AS email_id,
    tecd.contact_number AS phone_number,
    teed.date_of_joining,
    mees.status_name AS employee_status,
    mle.entity_name,
    med.division_name,
    mesd.sub_division_name,
    mle2.entity_name AS allocated_entity,
    med2.division_name AS allocated_division,
    mesd2.sub_division_name AS allocated_sub_division,
    meor.region AS region_name,
    mr.name AS position_name,
    mtol.location AS employee_work_location,
    mews.schedule_description AS employee_work_schedule,
    pmews.name AS project_shift,
    mehc.name AS employee_holiday_calendar,
    pmehc.name AS project_holiday_calendar,
    mo.name AS department_name,
    tpi.item_name AS project_name,
    tpi.sow_reference_number,
    tpi.profit_center,
    tis.start_date AS start_date,
    tis.end_date AS end_date,
    mprm.name AS project_role,
    tis.id,
    tetd.project_available_hours,
    mr2.name AS quote_position,
    tesd.skill_descriptions,
    IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
    ELSE 'Unassigned'
    END AS STATUS,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
    ELSE 2
    END AS status_id,
    CASE
     WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          IFNULL(
              (SELECT MAX(end_date) 
               FROM ${db}.t_internal_stakeholders
               WHERE associate_id = teec.associate_id
               AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
      ELSE NULL
  END AS last_allocation_date,
  CASE
      WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          DATEDIFF(CURDATE(), 
              IFNULL(
                  (SELECT MAX(end_date) 
                   FROM ${db}.t_internal_stakeholders
                   WHERE associate_id = teec.associate_id
                   AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
  )
      ELSE NULL
  END AS aging,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
    ELSE '#FFBD3D'
    END AS bg_color,
    '#FFFFFF' AS font_color,
    pms.shift_name AS work_mode,
    mrwp.name AS work_premisis,
    emd.name AS employee_job,
    pmc.name AS work_city
    FROM ${db}.t_e360_employee_central teec
    INNER JOIN ${db}.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teed.is_active = 1
    AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
    LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
    LEFT JOIN ${db}.t_e360_personal_details tepd
    ON tepd.associate_id = teed.associate_id
    AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tepd.is_active = 1
    LEFT JOIN ${db}.t_e360_org_details teod
    ON teod.associate_id = teec.associate_id
    AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teod.is_active = 1
    LEFT JOIN ${db}.t_e360_contact_details tecd
    ON tecd.associate_id = teec.associate_id
    AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tecd.is_active = 1
    LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
    LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
    LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
    LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
    LEFT JOIN ${db}.m_organization mde ON mde.id = teod.department
    LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
    LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
    LEFT JOIN ${db}.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tetd.is_active = 1
    LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
    LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
    LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
    LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
    LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
    AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
    LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
    LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
    LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
    LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
    LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
    LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
    LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
    LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
    LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
    LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
    LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
    LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
    LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
    LEFT JOIN (
        SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
        FROM ${db}.t_e360_skill_details tesd 
        LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
        WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.is_active = 1
        GROUP BY tesd.associate_id
        ) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;
        if(filter_query){
          count_query = count_query + filter_query
        }

        count_query = count_query + ` GROUP BY meor.id ORDER BY meor.id DESC`
    
        let result = await rpool(count_query)
 
    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch (err) {
    logger.info(err)
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    })
  }
 }

 module.exports.getNonEmployeeBillableAging = async (db, filter_query, startDate, endDate) => {
  try {
    let resultOutput = [
      { "count": 0, "label": "<90 days" },
      { "count": 0, "label": "90 - 120 days" },
      { "count": 0, "label": "120 - 180 days" },
      { "count": 0, "label": ">180 days" }
    ];

    let data = `SELECT
   COUNT(teec.associate_id) AS count,
     CASE 
        WHEN DATEDIFF(CURDATE(), 
            IFNULL(
                (SELECT MAX(end_date) 
                 FROM ${db}.t_internal_stakeholders
                 WHERE associate_id = teec.associate_id
                 AND end_date IS NOT NULL AND is_active = 1 AND end_date <= CURDATE()
                ), 
                DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d')
            )
        ) > 180 THEN '>180 days'
        WHEN DATEDIFF(CURDATE(), 
            IFNULL(
                (SELECT MAX(end_date) 
                 FROM ${db}.t_internal_stakeholders
                 WHERE associate_id = teec.associate_id
                 AND end_date IS NOT NULL AND is_active = 1 AND end_date <= CURDATE()
                ), 
                DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d')
            )
        ) BETWEEN 120 AND 180 THEN '120 - 180 days'
        WHEN DATEDIFF(CURDATE(), 
            IFNULL(
                (SELECT MAX(end_date) 
                 FROM ${db}.t_internal_stakeholders
                 WHERE associate_id = teec.associate_id
                 AND end_date IS NOT NULL AND is_active = 1 AND end_date <= CURDATE()
                ), 
                DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d')
            )
        ) BETWEEN 90 AND 119 THEN '90 - 120 days'
        ELSE '<90 days'
    END AS label
    FROM ${db}.t_e360_employee_central teec
    INNER JOIN ${db}.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teed.is_active = 1
    AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
    LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
    LEFT JOIN ${db}.t_e360_personal_details tepd
    ON tepd.associate_id = teed.associate_id
    AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tepd.is_active = 1
    LEFT JOIN ${db}.t_e360_org_details teod
    ON teod.associate_id = teec.associate_id
    AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teod.is_active = 1
    LEFT JOIN ${db}.t_e360_contact_details tecd
    ON tecd.associate_id = teec.associate_id
    AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tecd.is_active = 1
    LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
    LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
    LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
    LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
    LEFT JOIN ${db}.m_organization mde ON mde.id = teod.department
    LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
    LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
    LEFT JOIN ${db}.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tetd.is_active = 1
    LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
    LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
    LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
    LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
    LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
    AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
    LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
    LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
    LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
    LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
    LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
    LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
    LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
    LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
    LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
    LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
    LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
    LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
    LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
    LEFT JOIN (
        SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
        FROM ${db}.t_e360_skill_details tesd 
        LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
        WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.is_active = 1
        GROUP BY tesd.associate_id
        ) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2)  AND tetd.project_available_hours IS NOT NULL AND tetd.project_available_hours > 0 `;

    if (filter_query) {
      data += filter_query;
    }

    data += ` GROUP BY label ORDER BY label DESC`;

    let result = await rpool(data);

    const resultMap = result.reduce((acc, row) => {
      acc[row.label] = row.count;
      return acc;
    }, {});

    for (let data of resultOutput) {
      if (resultMap[data.label] !== undefined) {
        data['count'] = resultMap[data.label];
      }
    }

    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: resultOutput
      });
    } else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  } catch (err) {
    logger.info(err);
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'Error while fetching data'
    });
  }
};

/**
 * @description Get Request Data
 * @param {*} db 
 * @returns 
 */
module.exports.getRequestDataWithSkills = async(db,start_date,end_date,status,filterQuery)=>{
  try{

      let filter_query = ''
      if(filterQuery && filterQuery != ''){
        filter_query = filterQuery
      }

      let status_query = '';
      if(status && status.length > 0){
        status_query = ` AND trr.status IN (?) `
      }
      
      let data = await rpool(`SELECT trr.*, tpi.service_type_id, trjd.need_client_interview,trsd.skill AS skill_id,ms.long_description AS skill_name
        FROM ${db}.t_rm_request AS trr
        INNER JOIN ${db}.t_project_item AS tpi ON tpi.id = trr.project_id AND tpi.is_active = 1
        INNER JOIN ${db}.t_project_header AS tph ON tph.id = tpi.project_id AND tph.is_active = 1
        INNER JOIN ${db}.t_rm_job_detail AS trjd ON trjd.request_id = trr.request_id AND trjd.is_active = 1
        LEFT JOIN ${db}.m_legal_entity AS mle ON mle.entity_id = trjd.entity AND mle.is_active = 1
        LEFT JOIN ${db}.m_e360_org_region AS meor ON meor.id = trjd.region AND meor.is_active = 1
        LEFT JOIN ${db}.m_e360_division AS med ON med.id = trjd.division AND med.is_active = 1
        LEFT JOIN ${db}.m_e360_sub_division AS mesd ON mesd.id = trjd.sub_division AND mesd.is_active = 1
        LEFT JOIN ${db}.t_rm_skill_detail AS trsd ON trsd.request_id = trr.request_id
        LEFT JOIN ${db}.m_skill AS ms ON trsd.skill = ms.skill_id AND ms.is_active = 1
        WHERE trr.is_active = 1 AND DATE(trr.created_on) BETWEEN ? AND  ? ${status_query} ${filter_query} `,
        [start_date,end_date, status]
      );
      
      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}
/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getAllocatedEmployeeElm = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT
        tted.level, COUNT(DISTINCT tis.associate_id) AS value, CASE WHEN tepd.employee_class IS NULL THEN 'Unassigned' ELSE emd.employee_class END AS label
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        LEFT JOIN ${db}.t_e360_org_details tted ON tted.associate_id = tis.associate_id AND tted.start_date <= "${moment().format("YYYY-MM-DD")}" AND tted.end_date >= "${moment().format("YYYY-MM-DD")}" AND tted.is_active = 1
        LEFT JOIN ${db}.t_e360_personal_details tepd
			ON tepd.associate_id = tis.associate_id
			AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
			AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
			AND tepd.is_active = 1
        LEFT JOIN ${db}.m_e360_employee_class emd ON emd.id = tepd.employee_class
        WHERE tis.is_active = 1 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        data = data + ` GROUP BY label`


        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getAllocatedEmployeeCustomer = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT
         COUNT(DISTINCT tis.associate_id) AS value, CASE WHEN tph.end_customer_id IS NULL THEN 'Unassigned' ELSE mcm.customer_name END AS label
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
         LEFT JOIN ${db}.m_customer_master mcm ON mcm.customer_id = tph.end_customer_id
        WHERE tis.is_active = 1 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        data = data + ` GROUP BY label`


        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}
/**
 * @description Deployment Head Count
 * @param {*} db 
 * @returns 
 */
module.exports.getAllocatedEmployeeServiceType = async(db, filter_query, start_date, end_date)=>{
  try{
      
      let data = `
        SELECT
       COUNT(DISTINCT tis.associate_id) AS value, CASE WHEN tpi.service_type_id IS NULL THEN 'Unassigned' ELSE mst.service_type_name END AS label
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        LEFT JOIN ${db}.m_service_type mst ON mst.service_type_id = tpi.service_type_id
        WHERE tis.is_active = 1 
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'`;

        if(filter_query){
          data = data + filter_query
        }

        data = data + ` GROUP BY label`


        let result = await rpool(data);

      if(result && result.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: result
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}


module.exports.getDeployedDM = async(db, filter_query, start_date, end_date)=>{
  try{
    let count_query = `
        SELECT 
            COUNT(DISTINCT CASE 
                WHEN tis.project_role_id = 1 THEN tis.associate_id
                WHEN tes.project_role_id = 18 THEN tes.associate_id
            END) AS value,
            CASE 
                WHEN tis.project_role_id = 1 THEN 'Delivery Manager'
                WHEN tes.project_role_id = 18 THEN 'Delivery Partner'
            END AS label
        FROM ${db}.t_internal_stakeholders tis
        LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id
        LEFT JOIN ${db}.t_project_header tph ON tph.id = tis.project_id
        LEFT JOIN ${db}.t_project_external_stakeholders tes ON tes.project_item_id = tis.item_id AND tes.project_role_id = 18
        WHERE (
                (tis.is_active = 1 AND tis.project_role_id = 1)
                OR 
                (tes.is_active = 1 AND tes.project_role_id = 18)
              )
        AND (tis.start_date >= '${start_date}' OR tis.start_date <= '${start_date}') 
        AND (tis.end_date <= '${end_date}' OR tis.end_date >= '${end_date}') 
        AND tis.end_date >= '${start_date}' 
        AND tis.start_date <= '${end_date}'
    `;

    if (filter_query) {
      count_query = count_query + filter_query;
    }

    count_query = count_query + ` GROUP BY label`;

    let result = await rpool(count_query);

    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}

module.exports.getEmployeeReason = async (db, filter_query, startDate, endDate) => {
  try {
    let count_query = `SELECT
    COUNT(teec.associate_id) AS value,COALESCE(mrrs.status, 'unassigned') AS label,
    RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(tepd.first_name, ' '), ''), COALESCE(CONCAT(tepd.middle_name, ' '), ''), COALESCE(tepd.last_name, '')))) AS employee_name,
    teec.oid AS associate_oid,
    tecd.work_email AS email_id,
    tecd.contact_number AS phone_number,
    teed.date_of_joining,
    mees.status_name AS employee_status,
    mle.entity_name,
    med.division_name,
    mesd.sub_division_name,
    mle2.entity_name AS allocated_entity,
    med2.division_name AS allocated_division,
    mesd2.sub_division_name AS allocated_sub_division,
    meor.region AS region_name,
    mr.name AS position_name,
    mtol.location AS employee_work_location,
    mews.schedule_description AS employee_work_schedule,
    pmews.name AS project_shift,
    mehc.name AS employee_holiday_calendar,
    pmehc.name AS project_holiday_calendar,
    mo.name AS department_name,
    tpi.item_name AS project_name,
    tpi.sow_reference_number,
    tpi.profit_center,
    tis.start_date AS start_date,
    tis.end_date AS end_date,
    mprm.name AS project_role,
    tis.id,
    tetd.project_available_hours,
    mr2.name AS quote_position,
    tesd.skill_descriptions,
    IFNULL(DATE_ADD(tis.end_date, INTERVAL 1 DAY), CURDATE()) AS available_from_date,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 'Assigned'
    ELSE 'Unassigned'
    END AS STATUS,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1
    ELSE 2
    END AS status_id,
    CASE
     WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          IFNULL(
              (SELECT MAX(end_date) 
               FROM ${db}.t_internal_stakeholders
               WHERE associate_id = teec.associate_id
               AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
      ELSE NULL
  END AS last_allocation_date,
  CASE
      WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 
          DATEDIFF(CURDATE(), 
              IFNULL(
                  (SELECT MAX(end_date) 
                   FROM ${db}.t_internal_stakeholders
                   WHERE associate_id = teec.associate_id
                   AND end_date IS NOT NULL AND is_active=1 AND end_date<= CURDATE()),DATE_FORMAT(GREATEST(teed.date_of_joining, '2025-01-01'), '%Y-%m-%d'))
  )
      ELSE NULL
  END AS aging,
    CASE
    WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN '#52C41A'
    ELSE '#FFBD3D'
    END AS bg_color,
    '#FFFFFF' AS font_color,
    pms.shift_name AS work_mode,
    mrwp.name AS work_premisis,
    emd.name AS employee_job,
    pmc.name AS work_city
    FROM ${db}.t_e360_employee_central teec
    INNER JOIN ${db}.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teed.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teed.is_active = 1
    AND NOT (teed.employment_status = 3 AND teed.start_date < "${startDate}") AND teed.date_of_joining <= "${endDate}"
    LEFT JOIN ${db}.m_e360_employment_status mees ON mees.id = teed.employment_status
    LEFT JOIN ${db}.t_e360_personal_details tepd
    ON tepd.associate_id = teed.associate_id
    AND tepd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tepd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tepd.is_active = 1
    LEFT JOIN ${db}.t_e360_org_details teod
    ON teod.associate_id = teec.associate_id
    AND teod.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND teod.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND teod.is_active = 1
    LEFT JOIN ${db}.t_e360_contact_details tecd
    ON tecd.associate_id = teec.associate_id
    AND tecd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tecd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tecd.is_active = 1
    LEFT JOIN ${db}.m_role mr ON mr.id = teod.position
    LEFT JOIN ${db}.m_legal_entity mle ON mle.entity_id = teod.entity
    LEFT JOIN ${db}.m_e360_division med ON med.id = teod.division
    LEFT JOIN ${db}.m_e360_sub_division mesd ON mesd.id = teod.sub_division
    LEFT JOIN ${db}.m_organization mde ON mde.id = teod.department
    LEFT JOIN ${db}.m_e360_org_region meor ON meor.id = teod.region AND meor.is_active = 1
    LEFT JOIN ${db}.m_designation emd ON emd.id = teod.job
    LEFT JOIN ${db}.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= "${moment().format("YYYY-MM-DD")}"
    AND tetd.end_date >= "${moment().format("YYYY-MM-DD")}"
    AND tetd.is_active = 1
    LEFT JOIN ${db}.m_rm_resource_status AS mrrs ON mrrs.id = tetd.unavailability_reason AND mrrs.is_active = 1 
    LEFT JOIN ${db}.m_e360_holiday_calender mehc ON mehc.id = tetd.holiday_calender
    LEFT JOIN ${db}.m_e360_work_schedule mews ON mews.id = tetd.work_schedule
    LEFT JOIN ${db}.m_timesheet_office_location mtol ON mtol.id = tetd.work_location
    LEFT JOIN ${db}.m_organization mo ON mo.id = teod.department AND mo.is_active = 1
    LEFT JOIN ${db}.t_internal_stakeholders tis ON teec.associate_id = tis.associate_id AND tis.e360_end_date = '9999-12-31' AND tis.is_active = 1 
    AND (tis.end_date >= "${startDate}" AND tis.start_date <= "${endDate}")
    LEFT JOIN ${db}.t_project_item tpi ON tpi.id = tis.item_id AND tpi.gantt_type = 2
    LEFT JOIN ${db}.t_qb_quote_position tqqp ON tis.rate_card_id = tqqp.quote_position_id
    LEFT JOIN ${db}.m_role mr2 ON tqqp.position = mr2.id
    LEFT JOIN ${db}.m_shift pms ON pms.id = tis.shift
    LEFT JOIN ${db}.m_city pmc ON pmc.id = tis.work_city
    LEFT JOIN ${db}.m_rm_work_premisis mrwp ON mrwp.id = tis.work_premisis
    LEFT JOIN ${db}.t_project_general_settings tpgs ON tpgs.project_id = tis.project_id AND tpgs.item_id = tis.item_id AND tpgs.is_active = 1
    LEFT JOIN ${db}.t_project_work_location tpwl ON tpwl.portfolio_id = tis.project_id AND tpwl.project_id = tis.item_id AND tpwl.is_active = 1
    LEFT JOIN ${db}.m_project_work_shift pmews ON pmews.id = tpgs.shift
    LEFT JOIN ${db}.m_e360_holiday_calender pmehc ON pmehc.id = tpwl.holiday_calendar_id
    LEFT JOIN ${db}.m_project_role_master mprm ON mprm.id = tis.project_role_id
    LEFT JOIN ${db}.m_legal_entity mle2 ON mle2.entity_id = tis.entity_id
    LEFT JOIN ${db}.m_e360_division med2 ON med2.id = tis.division_id
    LEFT JOIN ${db}.m_e360_sub_division mesd2 ON mesd2.id = tis.sub_division_id
    LEFT JOIN (
        SELECT tesd.associate_id,GROUP_CONCAT(DISTINCT ms.skill_description ORDER BY ms.skill_description ASC SEPARATOR ', ') AS skill_descriptions
        FROM ${db}.t_e360_skill_details tesd 
        LEFT JOIN ${db}.m_skill ms ON ms.skill_id = tesd.name
        WHERE tesd.start_date <= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.end_date >= "${moment().format("YYYY-MM-DD")}" 
        AND tesd.is_active = 1
        GROUP BY tesd.associate_id
        ) tesd ON teec.associate_id = tesd.associate_id  WHERE teec.is_active = 1 AND (CASE WHEN tis.start_date IS NOT NULL AND tis.end_date IS NOT NULL THEN 1 ELSE 2 END) IN (2) AND (tetd.project_available_hours = 0)
`;
        if(filter_query){
          count_query = count_query + filter_query
        }

        count_query = count_query + ` GROUP BY mrrs.status ORDER BY mrrs.id DESC`
    
        let result = await rpool(count_query)

    if (result && result.length > 0) {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: result
      });
    }
    else {
      return Promise.resolve({
        err: false,
        messType: 'S',
        data: [],
        message: 'No Data Found'
      });
    }
  }
  catch (err) {
    logger.info(err)
    return Promise.reject({
      err: true,
      messType: 'E',
      data: [],
      message: 'No Data Found'
    })
  }
}
module.exports.getRequestDataForward = async(db,start_date,end_date,status,filterQuery)=>{
  try{

      let filter_query = ''
      if(filterQuery && filterQuery != ''){
        filter_query = filterQuery
      }

      let status_query = '';
      if(status && status.length > 0){
        status_query = ` AND trr.status IN (?) `
      }
      
      let data = await rpool(`SELECT trr.*, tpi.service_type_id, trjd.need_client_interview
        FROM ${db}.t_rm_request AS trr
        INNER JOIN ${db}.t_project_item AS tpi ON tpi.id = trr.project_id AND tpi.is_active = 1
        INNER JOIN ${db}.t_project_header AS tph ON tph.id = tpi.project_id AND tph.is_active = 1
        INNER JOIN ${db}.t_rm_job_detail AS trjd ON trjd.request_id = trr.request_id AND trjd.is_active = 1
        LEFT JOIN ${db}.m_legal_entity AS mle ON mle.entity_id = trjd.entity AND mle.is_active = 1
        LEFT JOIN ${db}.m_e360_org_region AS meor ON meor.id = trjd.region AND meor.is_active = 1
        LEFT JOIN ${db}.m_e360_division AS med ON med.id = trjd.division AND med.is_active = 1
        LEFT JOIN ${db}.m_e360_sub_division AS mesd ON mesd.id = trjd.sub_division AND mesd.is_active = 1
        WHERE trr.is_active = 1 AND DATE(trr.created_on) BETWEEN ? AND  ?   AND (MONTH(trr.created_on) != MONTH(CURDATE()) OR YEAR(trr.created_on) != YEAR(CURDATE())) ${status_query} ${filter_query} `,
        [start_date,end_date, status]
      );
      
      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}
module.exports.getProjectCategoryData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT id, name FROM ${db}.m_project_non_billable_category WHERE is_active = 1; `,[]);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}
module.exports.getProjectData = async(db)=>{
  try{
      
      let data = await rpool(`SELECT id,item_name as name
        FROM ${db}.t_project_item 
        WHERE is_active = 1 AND gantt_type = 2`,[]);

      if(data && data.length > 0){
        return Promise.resolve({
          err: false,
          messType: 'S',
          data: data
        });
      }
      else{
        return Promise.resolve({
          err: true,
          messType: 'E',
          data: [],
          message: 'No Data Found'
        });
      }
  }
  catch(err){
      logger.info(err)
      return Promise.reject({
        err: true,
        messType: 'E',
        data: [],
        message: 'No Data Found'
      })
  }
}