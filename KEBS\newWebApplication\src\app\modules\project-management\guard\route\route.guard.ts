import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router} from '@angular/router';
import { Observable } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { PmAuthorizationService } from '../../services/pm-authorization.service';
import { LoginService } from "src/app/services/login/login.service"

@Injectable({
  providedIn: 'root'
})
export class RouteGuard implements CanActivate {

  constructor(
    private loginService: LoginService,
    private toaster: ToasterService,
    private router:Router,
    private pmAuthService: PmAuthorizationService
  ) {}

  async canActivate(route: ActivatedRouteSnapshot){

        let hasAccess = <boolean>await this.canAccess()

        if(hasAccess == false)
        {
          console.log("project Application access")
          this.toaster.showError("You not having access to Project Management Application!","Access Restricted",10000)
          this.router.navigateByUrl("/main");
        };
  
        await this.pmAuthService.getProjectOverallAccess()
        this.pmAuthService.getUserRoleAccessProjectList()
        return hasAccess
  }


  canAccess(){
    return new Promise(async(resolve, reject)=>{
      await this.pmAuthService.checkProjectApplicationAccess().then(async(res: any)=>{
        resolve(res);
      },(err)=>{
        reject(false)
      });
    })
   
  }

}
