import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';

@Injectable({
  providedIn: 'root'
})
export class ProjectSettingGuard implements CanActivate {


  adminAccess: any;
  constructor(
    private pmAuthService: PmAuthorizationService,
    private toaster: ToasterService,
    private router: Router
  ) {}

  async canActivate(route: ActivatedRouteSnapshot){

        let hasAccess = <boolean>await this.canAccess()

        if(hasAccess == false)
        {
          console.log("project Application access")
          this.toaster.showError("You not having access to Project Management Application Settings!","Access Restricted",10000)
          this.router.navigateByUrl("/main/project-management");
        };
  
        

        this.pmAuthService.getUserRoleAccessProjectList()
        return hasAccess
  }


  canAccess(){
    return new Promise(async(resolve, reject)=>{
      await this.pmAuthService.getAdminAccess().then((res)=>{
          if(res['messType']=="S" || this.pmAuthService.getProjectObjectAccess(40))
          {
              resolve(true);
          }
          else
          {
              resolve(false)
          }
      })
    })
   
  }
  
}
