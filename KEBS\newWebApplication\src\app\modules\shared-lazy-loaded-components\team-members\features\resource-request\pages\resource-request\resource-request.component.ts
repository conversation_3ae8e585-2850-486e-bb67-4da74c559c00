import { Component, HostListener, NgModule, OnInit } from '@angular/core';
//underscore and moment operators
import * as _ from 'underscore';
import * as moment from 'moment';
import { TmService } from './../../../../services/tm.service';
@Component({
  selector: 'res-req-resource-request',
  templateUrl: './resource-request.component.html',
  styleUrls: ['./resource-request.component.scss'],
})
export class ResourceRequestComponent implements OnInit {
  protected _onDestroy = new Subject<void>();
  protected _onAppApiCalled = new Subject<void>();
  dataList = true;
  itemId: number;
  //Udrf
  applicationId = 417;
  isCardClicked = false;
  cardClicked: any;
  udrfItemStatusColor: any;
  dataTypeArray = [];
  udrfCurrentIndex = 0;
  categorisedDataTypeArray = [];
  itemDataCurrentIndex: any = 0;
  projectId: any;
  noDataCheck : boolean = false;
  udrfBodyColumns = [
    {
      item: 'display_request_id',
      header: 'Req ID',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      hasColumnClick: true,
      width: 120,
      sortOrder: 'I',
      filterId: 1,
    },
    {
      item: 'position',
      header: 'Position',
      isVisible: 'true',
      isActive: true,
      type: 'textHyphen',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 2,
      hasColumnClick: true,
      width: 300,
      sortOrder: 'I',
      filterId: 7,
    },
    {
      item: 'request_status',
      header: 'Status',
      isVisible: 'true',
      isActive: true,
      type: 'statusBadge',
      colSize: '4',
      textClass: 'zf-font',
      position: 3,
      width: 200,
      sortOrder: 'I',
      filterId: 8,
    },
    {
      item: 'requested_on',
      header: 'Requested On',
      status: '',
      isVisible: 'true',
      isActive: true,
      type: 'date',
      colSize: '4',
      textClass: 'zf-theme-light',
      position: 4,
      width: 150,
      sortOrder: 'I',
      filterId: 9,
    },
    {
      item: 'expected_closure_date',
      header: 'Expected Closure Date',
      isActive: true,
      isVisible: 'true',
      type: 'date',
      colSize: '4',
      position: 5,
      textClass: 'zf-theme-light',
      width: 200,
      sortOrder: 'I',
      filterId: 10,
    },
    {
      item: 'rm_officer',
      header: 'Allocation Coordinator',
      isActive: true,
      isVisible: 'true',
      type: 'profile',
      textClass: 'zf-theme-light',
      colSize: '4',
      position: 5,
      width: 300,
      sortOrder: 'N',
      filterId: 11
    },
    {
      item: 'priority',
      header: 'Priority',
      isActive: true,
      isVisible: 'true',
      type: 'text',
      textClass: 'zf-theme-light',
      colSize: '4',
      position: 6,
      width: 120,
      sortOrder: 'I',
      filterId: 12,
    },
    {
      item: 'total_cost',
      header: 'Total Cost',
      isActive: true,
      isVisible: 'true',
      type: 'text',
      textClass: 'zf-theme-light',
      colSize: '4',
      position: 7,
      width: 180,
      sortOrder: 'N',
      // filterId: 11
    },
    {
      item: 'action',
      header: 'Actions',
      isActive: true,
      isVisible: 'false',
      type: 'action',
      position: 8,
      colSize: 2,
      width: 120,
      sortOrder: 'N',
    },
    {
      item: 'requested_by',
      header: 'Requested By',
      isVisible: 'true',
      isActive: true,
      type: 'textHyphen',
      colSize: '4',
      textClass: 'zf-theme-light',
      position: 9,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'I',
      filterId: 1,
    },
    {
      item: 'rm_executive',
      header: 'Allocation Executive',
      isVisible: 'true',
      isActive: true,
      type: 'textHyphen',
      colSize: '4',
      textClass: 'zf-theme-light',
      position: 10,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'I',
      // filterId: 13,
    },
    {
      item: 'accepted_by',
      header: 'Accepted By',
      isVisible: 'true',
      isActive: true,
      type: 'textHyphen',
      colSize: '4',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'N',
      // filterId: 1,
    },
  ];
  // openComments: any;
  hasAccess = false;
  skip: any = 0;
  limit: any = 15;
  currentUser: any = {};
  defaultDataRetrievalCount: any = 15;
  resolveVisibleDataTypeArray: any;
  dataTypeCardSelected: any;
  // downloadEmployeeDirectoryReport: any;
  // openQuickActionItem: any;
  // quickActionData: any[];
  applicationRoleAccessList: any;
  subs = new SubSink();
  udrfColumnsTenant:any;

  opportunity_id:any;
  width:number;
  height:number;
  constructor(
    private utilityService: UtilityService,
    public udrfService: UdrfService,
    private _router: Router,
    private _route: ActivatedRoute,
    public dialog: MatDialog,
    private _rmService: RmService,
    private _tmService: TmService,
    private _help: KebsHelpService,
    private authService: LoginService,
    private _rmdService: RmDashboardService,
    private projectService: ProjectService,
    private pmAuthService: PmAuthorizationService

  ) {}

  
  retrieveFieldConfig(view_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.retrieveFieldConfig(view_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  async ngOnInit() {
    this.udrfService.resetUdrfData();
    this.itemId = (this._router.url.split('/')[2] == 'project-management') ? parseInt(this._router.url.split('/')[5]) : parseInt(this._router.url.split('/')[7]);
    this.projectId = parseInt(this._router.url.split('/')[3]);

    let data = this._route.snapshot.data;
    this.width = data['width'] ? data['width'] : 108
    this.height = data['height'] ? data['height'] : 247
    //For Calculating Dynamic Size
    this.calculateDynamicStyle();
    await this.handleReqCreationBtn()
    this.hasAccess = this._tmService.checkAccessForObject(
      accessObjectMaster.editformVisibility
    );
    // let statusMasterData = await this.getRequestStatusMasterData();
    // this.initStatusData(this.apiList);
    await this.handleTenantFieldLabel()
    let statusMasterData = await this.getRequestStatusMasterData();
    this.initStatusData(statusMasterData);
    this.itemId = (this._router.url.split('/')[2] == 'project-management') ? parseInt(this._router.url.split('/')[5]) : parseInt(this._router.url.split('/')[7]);
    this.opportunity_id = await this.getOpportunityData();
    this.configureUdrf();
    // console.log(this.itemId);
  }

  async handleReqCreationBtn() {
    // let projectRoleAccess = await this.projectService.projectCreationAccess()
    // if(projectRoleAccess && projectRoleAccess['messType']=="S" && projectRoleAccess['access']){
    //   let req_creation_access = _.where(projectRoleAccess['data'],{is_req_creation:1})
    //   this.udrfService.udrfUiData.showAllocationRequestBtn = req_creation_access.length>0
    // } 
    if(this._router.url.split('/')[2] == 'project-management')
    {
      await this.pmAuthService.getReadWriteAccess(this.projectId, this.itemId).then(async(res)=>{
        if(res){
          this.udrfService.udrfUiData.showAllocationRequestBtn = await this.pmAuthService.getProjectWiseObjectAccess(this.projectId, this.itemId, 354)
        }
      })
    }
    else
    {
      await this.projectService.allocateResourceAccess(this.projectId).then((res: any)=>{
        this.udrfService.udrfUiData.showAllocationRequestBtn = res
      })
    }
  }

  async handleTenantFieldLabel() {
    let rmConfig = await this.retrieveRmConfig()
    let costFlag = rmConfig['cost_details'] 
    ? rmConfig['cost_details']['is_active'] : false
    for(let l of this.udrfBodyColumns) {
        if(l['item']=='total_cost') {
            l['isActive'] = costFlag
        }
      }
    this.udrfColumnsTenant = this.udrfBodyColumns.filter(obj => obj.isActive !== false);
  }
  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._rmdService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err)
          resolve(res.data)
      }, (err) => {
        console.log(err)
      })
    })
  }
  initStatusData = (data) => {
    let udrfItemStatusColor = [];
    data.forEach((e, i) => {
      udrfItemStatusColor.push({
        status: e.status,
        color: e.color,
      });
    });

    this.udrfItemStatusColor = udrfItemStatusColor;
  };
  configureUdrf = () => {
    let requestedOnRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').startOf('month'),
          moment(moment().subtract(1, 'months').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').endOf('month'),
          moment(moment().subtract(1, 'months').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01').format('YYYY-MM-DD'),
        checkboxEndValue: moment('9999-12-31').format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: true,
      },
    ];
    let closureDateRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').startOf('month'),
          moment(moment().subtract(1, 'months').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').endOf('month'),
          moment(moment().subtract(1, 'months').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01').format('YYYY-MM-DD'),
        checkboxEndValue: moment('9999-12-31').format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: true,
      },
    ];
    this.udrfService.udrfFunctions.constructCustomRangeData(
      2,
      'date',
      requestedOnRanges
    );
    this.udrfService.udrfFunctions.constructCustomRangeData(
      6,
      'date',
      closureDateRanges
    );
    this.udrfService.udrfUiData.moreActionItemData = {
      moreActionItems: [
        {
          id: 1,
          name: 'Edit',
          icon: 'create',
        },
      ],
    };
    if (!this.hasAccess) {
      for (let item of this.udrfService.udrfUiData.moreActionItemData[
        'moreActionItems'
      ]) {
        if (item.name === 'Edit') {
          this.udrfService.udrfUiData.moreActionItemData[
            'moreActionItems'
          ].splice(
            this.udrfService.udrfUiData.moreActionItemData[
              'moreActionItems'
            ].indexOf(item),
            1
          );
          break;
        }
      }
    }
    this.udrfService.udrfData.applicationId = this.applicationId;
    this.udrfService.udrfUiData.showViewForRMG = 'list';
    this.udrfService.udrfUiData.showHelpButton = true;
    this.udrfService.udrfUiData.showItemDataCount = true;
    this.udrfService.udrfUiData.showSearchBar = true;
    this.udrfService.udrfUiData.showActionButtons = true;
    this.udrfService.udrfUiData.showUdrfModalButton = true;
    this.udrfService.udrfUiData.showColumnConfigButton = true;
    this.udrfService.udrfUiData.showSettingsModalButton = false;
    this.udrfService.udrfUiData.showNewReleasesButton = false;
    this.udrfService.udrfUiData.showReportDownloadButton = false;
    this.udrfService.udrfUiData.isReportDownloading = false;
    this.udrfService.udrfUiData.itemHasOpenInNewTab = false;
    this.udrfService.udrfUiData.horizontalScroll = true;
    this.udrfService.udrfUiData.itemHasQuickCta = false;
    this.udrfService.udrfUiData.collapseAll = false;
    this.udrfService.udrfUiData.showCollapseButton = false;
    this.udrfService.udrfUiData.countForOnlyThisReport = false;
    this.udrfService.udrfUiData.toggleChecked = false;
    this.udrfService.udrfUiData.countFlag = false;
    this.udrfService.udrfUiData.isMultipleView = false;
    this.udrfService.udrfUiData.itemHasDownloadButton = false;
    this.udrfService.udrfUiData.emailPluginVisible = false;
    this.udrfService.udrfUiData.itemHasDownloadButton = false;
    this.udrfService.udrfUiData.itemHasAttachFileButton = false;
    this.udrfService.udrfUiData.itemHasComments = true;
    this.udrfService.udrfUiData.isMoreOptionsNeeded = true;
    this.udrfService.udrfUiData.showAddMember = false
    this.udrfService.udrfUiData.isModalSortVisible = false;
    this.udrfService.udrfUiData.summaryCardsItem = {};
    this.udrfService.udrfUiData.summaryCards = [];
    // this.udrfService.udrfUiData.completeProfileBtn = true;
    this.udrfService.udrfUiData.variant = 0;
    this.udrfService.udrfUiData.itemDataType = '';
    this.udrfService.udrfUiData.searchPlaceholder = 'by Request ID, Position';
    this.udrfService.udrfUiData.totalItemDataCount = 0;
    this.udrfService.udrfUiData.itemCardSelecteditem = {};
    this.udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor;
    this.udrfService.udrfUiData.inlineEditData = {};
    this.udrfService.udrfUiData.udrfBodyColumns = this.udrfColumnsTenant;
    this.udrfService.udrfUiData.udrfVisibleBodyColumns =
      this.udrfService.udrfUiData.udrfVisibleBodyColumns;
    this.udrfService.udrfUiData.udrfInvisibleBodyColumns =
      this.udrfService.udrfUiData.udrfInvisibleBodyColumns;
    this.udrfService.udrfUiData.quickCTAInput = {};
    this.udrfService.udrfUiData.commentsInput = {};
    this.udrfService.udrfUiData.commentsContext = {};
    this.udrfService.udrfUiData.updateItemCard = () => {};
    this.udrfService.udrfUiData.attachFile = () => {};
    this.udrfService.udrfUiData.deleteFile = () => {};
    this.udrfService.udrfUiData.downloadFile = () => {};
    this.udrfService.udrfUiData.closeCTA = () => {};
    this.udrfService.udrfUiData.updateItemCardData = {};
    this.udrfService.udrfUiData.attachFileData = {};
    this.udrfService.udrfUiData.deleteFileData = {};
    this.udrfService.udrfUiData.downloadFileData = {};
    this.udrfService.udrfUiData.closeCTAData = {};
    this.udrfService.udrfUiData.onProgressItem = () => {};
    this.udrfService.udrfUiData.onCompleteItem = () => {};
    this.udrfService.udrfUiData.onCompleteItemResponse = '';
    this.udrfService.udrfUiData.onColumnClickItem = '';
    this.udrfService.udrfUiData.collapseAllCard = () => {};
    this.udrfService.udrfUiData.countResponseData = [];
    this.udrfService.udrfUiData.emailPluginData = {};
    this.udrfService.udrfUiData.fetchProfileNameFromE360 = true;
    // this.udrfService.udrfUiData.summaryCardsItem = {};
    this.udrfService.udrfUiData.summaryCards = [];
    this.udrfService.udrfUiData.itemHasMoreActions = false;
    this.udrfService.udrfUiData.moreActionItemClicked =
      this.moreActionItemClicked.bind(this);
    this.udrfService.udrfUiData.moreActionItemClickedData = {};
    this.udrfService.udrfUiData.itemDataScrollDown =
      this.onUdrfItemDataScrollDown.bind(this);
    this.udrfService.udrfUiData.openComments = this.openComments.bind(this);
    // this.udrfService.udrfUiData.showAllocationRequestBtn = true
    this.udrfService.udrfUiData.openAllocationRequest = this.openAllocationRequest.bind(this);

    // this.udrfService.udrfUiData.resolveVisibleSummaryCards =
    //   this.resolveVisibleDataTypeArray.bind(this);
    // this.udrfService.udrfUiData.summaryCardsSelected =
    //   this.dataTypeCardSelected.bind(this);

    //   this.udrfService.udrfUiData.downloadItemDataReport =
    //   this.downloadEmployeeDirectoryReport.bind(this);

    //   this.udrfService.udrfUiData.openQuickActionItem =
    //   this.openQuickActionItem.bind(this);

    // this.udrfService.udrfUiData.completeProfile =
    //   this.navigateEdCompleteProfile.bind(this);

    this.udrfService.udrfUiData.itemcardSelected =
      this.requestDetailPopUp.bind(this);

    // let quickActionAccess = _.where(this.applicationRoleAccessList,{object_id:206});

    // if(quickActionAccess.length>0){
    //   this.udrfService.udrfUiData.showQuickActionButton = true;
    //   this.udrfService.udrfUiData.showQuickActionItem = true;
    //   this.udrfService.udrfUiData.openQuickActionData = this.quickActionData;
    // }

    // console.log(this.udrfService.udrfUiData.openQuickActionData);

    this.udrfService.getAppUdrfConfig(
      this.applicationId,
      this.initReport.bind(this)
    );

    this.udrfService.getNotifyReleasesUDRF();
    this.udrfService.udrfFunctions.resolveVisibleColumnConfigItems();
    this.udrfService.udrfUiData.resolveColumnConfig();
    this.udrfService.udrfUiData.openHelpDialog = this.openHelpDialog.bind(this);
  };

  initReport = () => {
    // if(this.opportunity_id == null){
    //   this.udrfService.udrfUiData.showAllocationRequestBtn = true
    // }
    this._onAppApiCalled.next();
    this.itemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];
    this.isCardClicked = false;
    this.cardClicked = '';
    this.udrfService.udrfUiData.resolveColumnConfig();
    this.udrfService.udrfData.isItemDataLoading = true;
    this.skip = 0;
    this.limit = this.defaultDataRetrievalCount;
    this.getRequestData(false);
  };

  /**
   * @description get request data
   */

  getRequestData = (infiniteScrollFlag) => {
    this.udrfService.udrfData.isItemDataLoading = true;
    let mainFilterArray = JSON.parse(
      JSON.stringify(this.udrfService.udrfData.mainFilterArray)
    );

    let filterConfig = {
      item_id: this.itemId,
      request_context: 1,
      startIndex: this.skip,
      // endIndex: this.limit,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
    };

    this._tmService
      .getTeamMembersRequestResourceList(filterConfig)
      .pipe(takeUntil(this._onDestroy))
      .pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {
          if (!res['err'] && res['data'].length > 0) {
            res['data'] = this.utcDateFormat(res['data'])
            this.udrfService.udrfBodyData =
              this.udrfService.udrfBodyData.concat(res['data']);
          } else {
            this.udrfService.udrfBodyData =
              this.udrfService.udrfBodyData.concat([]);
          }
          if (this.skip == 0) await this.getTotalCount(filterConfig);
          if(this.udrfService.udrfBodyData.length == 0){
            this.noDataCheck = true;
          }
          this.udrfService.udrfData.isItemDataLoading = false;
        },
        (err) => {
          this.udrfService.udrfBodyData = [];
          this.udrfService.udrfData.isItemDataLoading = false;
          console.log(err);
        }
      );
  };

  utcDateFormat(data) {
    for(let l of data) {
      l['request_start_date'] = moment(l['request_start_date']).utc().format('DD-MMM-YYYY')
      l['request_end_date'] = moment(l['request_end_date']).utc().format('DD-MMM-YYYY')
      l['expected_closure_date'] = moment(l['expected_closure_date']).utc().format('DD-MMM-YYYY')
    }
    return data
  }

  async getTotalCount(filterConfig) {
    await this._tmService
      .getTeamMembersRequestResourceCount(filterConfig)
      .pipe(takeUntil(this._onDestroy))
      .pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res: any) => {
          if (!res.err)
            this.udrfService.udrfUiData.totalItemDataCount = res['total_count'];
        },
        (err) => {
          console.log(err);
        }
      );
  }

  /**
   * @description on udrf item scroll down
   */
  onUdrfItemDataScrollDown() {
    console.log("Scroll is called!!!");
    if (!this.udrfService.udrfData.noItemDataFound) {
      if (!this.udrfService.udrfData.isItemDataLoading) {
        this.skip += this.defaultDataRetrievalCount;

        // this.udrfService.udrfData.isItemDataLoading = true;

        this.getRequestData(true);
      }
    }
  }
  requestForm = () => {
    this._router.navigate(['request-form'], { relativeTo: this._route });
  };
  /**
   * @description item card selected dialog
   */
  requestDetailPopUp() {
    let resourceData = this.udrfService.udrfUiData.itemCardSelecteditem;
    // console.log('Resource Data', resourceData);
    const dialogRef = this.dialog.open(RequestedResourceDetailComponent, {
      disableClose: true,
      width: "90%",
      height: "100%",
      position: {top:'0px',right: '0%' },
      data: { modalParams: resourceData, status:this.udrfItemStatusColor },
    });
    dialogRef.afterClosed().subscribe((result:any) => {
      if(result) {
        //console.log('The dialog was closed',result);
        for(let i = 0;i<this.udrfService.udrfBodyData.length;i++){
          if(this.udrfService.udrfBodyData[i]['request_id'] === resourceData['request_id']){
            //console.log("Row status",this.udrfService.udrfBodyData[i]['request_status'])
            this.udrfService.udrfBodyData[i]['request_status'] = result['status']
            this.udrfService.udrfBodyData[i]['accepted_by'] = result['accepted_by']

            break;
          }
        }
      }
    });
  }

  /**
   * @description item card selected dialog
   */
  tileDetailPopUp(val) {
    console.log('value:', val);
    let resourceData = this.udrfService.udrfUiData.itemCardSelecteditem;
    console.log('Resource Data', resourceData);
    const dialogRef = this.dialog.open(RequestedResourceDetailComponent, {
      disableClose: true,
      width: "90%",
      height: "100%",
      position: {top:'0px',right: '0%' },
      data: { modalParams: val, status: this.udrfItemStatusColor },
    });
    dialogRef.afterClosed().subscribe((result:any) => {
      console.log('The dialog was closed',result);
      // this.getRequestData(false);
      // console.log(this.udrfService.udrfUiData.itemCardSelecteditem)
      // let resourceData = this.udrfService.udrfUiData.itemCardSelecteditem;
      // console.log(this.udrfService.udrfBodyData.length);
      for(let i = 0;i<this.udrfService.udrfBodyData.length;i++){
        // console.log("Going Inside Function !!!!");
        // console.log(this.udrfService.udrfBodyData[i]['request_id'],"udrf");
        // console.log(resourceData['request_id'],"request");
        if(this.udrfService.udrfBodyData[i]['request_id'] == val['request_id']){
          console.log("Row status",this.udrfService.udrfBodyData[i]['request_status'])
          this.udrfService.udrfBodyData[i]['request_status'] = result['status']
          break;
        }
      }
    });
  }

  async moreActionItemClicked() {
    let resourceData = this.udrfService.udrfUiData.itemCardSelecteditem;
    console.log(
      'item clicked',
      this.udrfService.udrfUiData.moreActionItemClickedData
    );
    let menuSelected =
      this.udrfService.udrfUiData.moreActionItemClickedData['selectedMenu'];
    let itemData =
      this.udrfService.udrfUiData.moreActionItemClickedData['itemData'];

    if (menuSelected.id == 1) {
      const openPaymentEntryModalComponent = this.dialog.open(
        EditRequestFormComponent,
        {
          width: '660px',
          height: ' 500px',
          // position: { right: '0px' },
          data: { modalParams: itemData },
        }
      );
    }
  }

  /**
   * @description get status master
   */
  getRequestStatusMasterData() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getRequestStatusList().subscribe(
        (res: any) => {
          if (!res.err) {
            resolve(res.data);
          } else {
            reject(res);
          }
        },
        (err) => {
          console.log(err);
        }
      );
    });
  }
  // getAllCardCount = () => {
  //   this.udrfService.udrfUiData.totalItemDataCount = 0;
  //   let mainFilterArray = JSON.parse(
  //     JSON.stringify(this.udrfService.udrfData.mainFilterArray)
  //   );

  //   let filters = this.getFilters(mainFilterArray);

  //   let filterConfig = {
  //     skip: this.skip,
  //     limit: this.limit,
  //     startDate: this.udrfService.udrfData.mainApiDateRangeStart,
  //     endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
  //     filter: filters,
  //     txTableDetails: this.udrfService.udrfData.txTableDetails,
  //     mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
  //     searchTableDetails: this.udrfService.udrfData.searchTableDetails,
  //   };

  //   if(this.udrfService.udrfData.customDateFilterId == null){
  //     console.log(_.where(filterConfig,{"filterName":'Date Of Joining'}))
  //     console.log(_.pluck(_.where(filterConfig,{"filterName":'Date Of Joining'}),'valueId'))
  //     if(filterConfig.filter.length==0 || (filterConfig.filter.length>0 &&
  //       _.pluck(_.where(filterConfig.filter,{"filterName":'Date Of Joining'}),'valueId')
  //       .length==0)){
  //         filterConfig.filter.push({
  //           filterName: 'Date Of Joining',
  //           valueId: ['All'],
  //         });
  //          filterConfig.startDate = moment('1920-01-01');
  //          filterConfig.endDate = moment('9999-12-31');
  //     }
  //   }

  //   this._rmService
  //     .getTeamMembersRequestResourceList(filterConfig)
  //     .pipe(takeUntil(this._onDestroy))
  //     .pipe(takeUntil(this._onAppApiCalled))
  //     .subscribe(async (res) => {
  //       if (!res['err']  && res['data'].length > 0) {
  //         for (let i = 0; i < this.dataTypeArray.length; i++) {
  //           this.dataTypeArray[i].dataTypeValue = '0';
  //         }

  //         let total = 0;
  //         for (let i = 0; i < res['data'].length; i++) {
  //           total += Number(res['data'][i]['dataTypeValue']);
  //         }

  //         this.udrfService.udrfUiData.totalItemDataCount = total;

  //         this.dataTypeArray = this.dataTypeArray.map((item, row) => {
  //           const found = res['data'].find(
  //             (element) => item.dataType == element.dataType
  //           );
  //           return { ...item, ...found };
  //         });

  //         this.udrfService.udrfUiData.summaryCards = this.dataTypeArray;
  //       } else {
  //         for (let i = 0; i < this.dataTypeArray.length; i++) {
  //           this.dataTypeArray[i].dataTypeValue = '0';
  //         }
  //         this.udrfService.udrfUiData.summaryCards = this.dataTypeArray;
  //       }
  //     },
  //     (err) => {
  //       console.log(err);
  //     });
  openHelpDialog() {
    let rmgTopicId = 56;
    this._help.openHelpDialog(
      this.authService.getToken(),
      rmgTopicId,
      this.currentUser
    );
  }

  async openComments() {
    let selectedRowData = this.udrfService.udrfUiData.openCommentsData['data'];
    console.log(selectedRowData)
    let inputData = {
      application_id: 460,
      unique_id_1: selectedRowData['request_id'],
      unique_id_2: '',
      application_name: 'Resource Management',
      title: `Request ID # ${selectedRowData['request_id']}`,
    };

    let modalParams = {
      inputData: inputData,
      context: {
        'Request ID': selectedRowData['request_id'],
        'Position': selectedRowData['position'],
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%',
    };

    const { ChatCommentContextModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
    );

    const openChatCommentContextModalComponent = this.dialog.open(
      ChatCommentContextModalComponent,
      {
        height: '100%',
        width: '75%',
        position: { right: '0px' },
        data: { modalParams: modalParams },
      }
    );
  }

  openAllocationRequest() {
    this._router.navigate(['request-form'], { relativeTo: this._route });
  }

  getOpportunityData = () =>{
    return new Promise((resolve, reject) => {
      this.subs.sink =  this._tmService.getOpportunityData(this.itemId).subscribe(
        (res: any) => {
          resolve(res.data ? res.data['opportunity_id'] : null);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle(){
    let headerWidth = window.innerWidth - this.width + 92 + 'px'
    let tableWidth = window.innerWidth - this.width + 'px'
    document.documentElement.style.setProperty(
      '--dynamicHeaderWidth',
      headerWidth
    )
    document.documentElement.style.setProperty(
      '--dynamicTableWidth',
      tableWidth
    )
    let tableHeight = window.innerHeight - this.height + 'px'
    let tableHeightShorted = window.innerHeight - this.height - 52 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicTableHeight',
      tableHeight
    )
    document.documentElement.style.setProperty(
      '--dynamicTableHeightShortened',
      tableHeightShorted
    )
  }

  ngOnDestroy() {
    // this._onDestroy.next();
    // this._onDestroy.complete();

    this.subs.unsubscribe();
    console.log('Destroy Called!!!');
    this.udrfService.resetUdrfData();
  }
}

import { CommonModule } from '@angular/common';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { RejectDialogComponent } from '../../components/reject-dialog/reject-dialog.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RequestedResourceDetailComponent } from '../../components/requested-resource-detail/requested-resource-detail.component';
import { SkillScoreComponent } from '../../components/skill-score/skill-score.component';

import {MatTableModule} from '@angular/material/table';

import { KebsProgressCircleModule } from 'kebs-progress-circle';
//Material Modules
import { MatDialog } from '@angular/material/dialog';
import { MatDialogModule } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule,ReactiveFormsModule } from '@angular/forms';
import { ResourceManagerDialogShowingEvent } from 'devextreme/ui/gantt';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { takeUntil } from 'rxjs/operators';
import { EditRequestFormComponent } from '../../components/edit-request-form/edit-request-form/edit-request-form.component';
import { TileViewComponent } from '../../components/tile-view/tile-view.component';
import { SubSink } from 'subsink';
import { accessObjectMaster } from '../../../../access-object.auth';
import { KebsHelpService } from 'kebs-help-application';
import { LoginService } from 'src/app/services/login/login.service';
import { MatButtonModule } from '@angular/material/button';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MAT_DATE_FORMATS,
} from '@angular/material/core';
import { lcTypeTxtContentModule } from 'src/app/modules/lms-learner/features/learner-course-content/components/c-type-txt/lc-type-txt.component';
import { RmDashboardService } from 'src/app/modules/reports/features/rm-dashboard/services/rm-dashboard.service';
import { ProjectService } from 'src/app/modules/projects/services/project.service';
import { SafehtmlPipe } from '../../../../pipes/safehtml.pipe';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import {MatSelectModule} from '@angular/material/select';
import {MatAutocompleteModule} from '@angular/material/autocomplete';
import { SharedLazyLoadedModule } from 'src/app/modules/project-management/shared-lazy-loaded/shared-lazy-loaded.module';

@NgModule({
  declarations: [
    ResourceRequestComponent,
    RequestedResourceDetailComponent,
    RejectDialogComponent,
    TileViewComponent,
    SkillScoreComponent,
    SafehtmlPipe
  ],
  imports: [
    CommonModule,
    SharedComponentsModule,
    MatDialogModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatTooltipModule,
    MatButtonModule,
    MatTableModule,
    KebsProgressCircleModule,
    InfiniteScrollModule,
    FormsModule,
    MatSelectModule,
    MatAutocompleteModule,
    SharedLazyLoadedModule
  ],
  exports: [],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MM-YYYY',
        },
        display: {
          dateInput: 'DD-MM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
      },
    },
  ],
})
export class ResourceRequestModule {}
