import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class UbrPostingService {

  constructor(private http: HttpClient) { }

  getUbrPostingDetailsDownload(filterConfig) {
    try {
      return this.http.post('api/invoice/getUbrPostingDetailsDownload', {
        filterConfig: filterConfig
      })
    }
    catch (err) {
      // this.spinnerService.hide();
      return err;
    }
  }

  getUbrPostingDetails(filterConfig) {
    try {
      return this.http.post('api/invoice/getUbrPostingDetails', {
        filterConfig: filterConfig
      })
    }
    catch (err) {
      return err;
    }
  }

  getBillingPostingPeriod() {
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/invoice/getBillingPostingPeriod',{})
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.showMessage("Your Search didn't match any questions!","dismiss", 2000);
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.showMessage("Error! Your Search didn't match any questions!","dismiss", 2000);
            return reject(err)}
        }
        )
      })
    }
    catch (err) {
      // this.spinnerService.hide();
      return err;
    }
  }

  updateBillingsPostingPeriod(posting_period){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/invoice/updateBillingsPostingPeriod',{posting_period})
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.showMessage("Your Search didn't match any questions!","dismiss", 2000);
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.showMessage("Error! Your Search didn't match any questions!","dismiss", 2000);
            return reject(err)}
        }
        )
      })
    }
    catch (err) {
      // this.spinnerService.hide();
      return err;
    }
  }

  getSummaryCard(filterConfig)
  {
    return this.http.post("/api/invoice/getUbrPostingSummaryCard",{
     filterConfig: filterConfig
    });
  }

  getUbrByCostCenter(profit_center){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/invoice/getUbrPostingDetailsByCostCenter',{profit_center})
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.showMessage("Your Search didn't match any questions!","dismiss", 2000);
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.showMessage("Error! Your Search didn't match any questions!","dismiss", 2000);
            return reject(err)}
        }
        )
      })
    }
    catch (err) {
      // this.spinnerService.hide();
      return err;
    }
  }

  updateUbrPostingData(ubr_posting_data) {
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/invoice/updateUbrPostingData',{ubr_posting_data})
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.showMessage("Your Search didn't match any questions!","dismiss", 2000);
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.showMessage("Error! Your Search didn't match any questions!","dismiss", 2000);
            return reject(err)}
        }
        )
      })
    }
    catch (err) {
      // this.spinnerService.hide();
      return err;
    }
  }

  getMisPostingPeriod() {
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/invoice/getMisPostingPeriod',{})
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.showMessage("Your Search didn't match any questions!","dismiss", 2000);
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.showMessage("Error! Your Search didn't match any questions!","dismiss", 2000);
            return reject(err)}
        }
        )
      })
    }
    catch (err) {
      // this.spinnerService.hide();
      return err;
    }
  }

  getBillingPostingUpdateConfirmation() {
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/invoice/getBillingPostingUpdateConfirmation',{})
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.showMessage("Your Search didn't match any questions!","dismiss", 2000);
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.showMessage("Error! Your Search didn't match any questions!","dismiss", 2000);
            return reject(err)}
        }
        )
      })
    }
    catch (err) {
      // this.spinnerService.hide();
      return err;
    }
  }

  getUBRConversionRate(){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/invoice/getUBRConversionRate',{})
        .subscribe({
          next: (res)=> {
            return resolve(res)
          },
          error: (err)=>{
            return reject(err)}
        }
        )
      })
    }
    catch (err) {
      return err;
    }
  }

  reportAuthorizationValidation() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/v2/reportAuthorizationValidation", {applicationId: 971}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }

}
