import { Component, Inject, OnInit } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { QuoteMainService } from '../../../../services/quote-main.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { OpportunityService } from "src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService";
@Component({
  selector: 'app-quote-activity-log',
  templateUrl: './quote-activity-log.component.html',
  styleUrls: ['./quote-activity-log.component.scss']
})
export class QuoteActivityLogComponent implements OnInit {

  activityList = [];

  protected _onDestroy = new Subject<void>();

  isActivityLoading: boolean = false;

  constructor(
    public matDialogRef: MatDialogRef<QuoteActivityLogComponent>,
    @Inject(MAT_DIALOG_DATA) public inData: any,
    private _quoteService: QuoteMainService,
    private _toaster: ToasterService,
    private opportunityService: OpportunityService,
  ) { }

  ngOnInit(): void {
    console.log(this.inData)
    if (this.inData.quoteId)
      this.getQuoteActivity(this.inData.quoteId);

    else if(this.inData.opportunityId && this.inData.fieldName)
      this.getOpportunityAuditChangeLogforField(this.inData.opportunityId,this.inData.fieldName)

    else if (this.inData.opportunityId && !this.inData?.fieldName)
      this.getOpportunityValueChangeLog(this.inData.opportunityId);


  }

  /**
   * @description Gets quote activity list
   * @param quoteId 
   */
  getQuoteActivity = (quoteId: number) => {

    this.isActivityLoading = true;

    this._quoteService.getQuoteActivity(quoteId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res && res['messType'] == 'S' && res['data'])
          this.activityList = res['data'];

        else
          this._toaster.showInfo("No Updates", "No Activity log Found !", this.opportunityService.mediumInterval);

        this.isActivityLoading = false;

      },
        err => {
          this.isActivityLoading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Quote activity logs !", 3000);
        });

  }

  /**
   * @description Gets opportunity value change log
   * @param quoteId 
   */
  getOpportunityValueChangeLog = (opportunityId: number) => {

    this.isActivityLoading = true;

    this._quoteService.getOpportunityValueChangeLog(opportunityId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res && res['messType'] == 'S' && res['data'])
          this.activityList = res['data'];

        else
          this._toaster.showInfo("No Updates", "No Activity log Found !", this.opportunityService.mediumInterval);

        this.isActivityLoading = false;

      },
        err => {
          this.isActivityLoading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Opportunity value change logs !", 3000);
        });

  }
  /**
   * @description Gets opportunity value change log
   * @param quoteId 
   */
  getOpportunityAuditChangeLogforField = (opportunityId: number,fieldName:String) => {

    this.isActivityLoading = true;

    this._quoteService.getOpportunityAuditChangeLogforField(opportunityId,fieldName)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res && res['messType'] == 'S' && res['data'])
          this.activityList = res['data'];

        else
          this._toaster.showInfo("No Updates", "No Activity log Found !", this.opportunityService.mediumInterval);

        this.isActivityLoading = false;

      },
        err => {
          this.isActivityLoading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Opportunity value change logs !", 3000);
        });

  }

  closeDialog = () => {

    this.matDialogRef.close({ event: 'close' });

  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { QuotePipesModule } from 'src/app/modules/quote-builder/quote-pipes/quote-pipes.module';
import { dynamicDatePipe } from 'src/app/modules/account-sales/features/account-sales-detail/pipes/accounts-pipes.module';

@NgModule({
  declarations: [
    QuoteActivityLogComponent
  ],
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    QuotePipesModule,
    dynamicDatePipe
  ],
  exports: [QuoteActivityLogComponent]
})

class QuoteActivityLogModule { }
