import { Component, OnInit,Output,Input,Inject, HostListener, ViewChild, ElementRef } from '@angular/core';
import { MatDialog,MatDialogRef} from '@angular/material/dialog';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { EventEmitter } from '@angular/core';
import{PmBillingService} from 'src/app/modules/project-management/features/pm-details/features/pm-billing/services/pm-billing.service'

import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';
import * as moment from 'moment';
import { DOCUMENT } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { AttachmentServiceService } from "src/app/modules/project-management/shared-lazy-loaded/services/attachment-service.service"
import { ChatCommentContextModalComponent } from 'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service'
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { Logger } from 'msal';
import Swal from 'sweetalert2';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { TOASTER_MESSAGE_SERVICE_TOKEN } from '../../../pm-details/features/pm-project-team/features/project-internal-stakeholders/component/pm-add-member/pm-add-member.component';
import { MatDatepicker } from '@angular/material/datepicker';
import { SubSink } from 'subsink';
import {VersionHistoryComponent} from "src/app/modules/project-management/shared-lazy-loaded/components/version-history/version-history.component";
import { BillingAdviceCommentComponent } from "../billing-advice-comment/billing-advice-comment.component";
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';
import { LoginService } from 'src/app/services/login/login.service';
import { RejectPopupComponent } from 'src/app/modules/project-management/shared-lazy-loaded/components/reject-popup/reject-popup.component'
import { IsAfterPipe } from 'ngx-moment';
import { ResourceLoadingLandingPageComponent } from '../../../pm-resource-loading/features/resource-loading-landing-page/resource-loading-landing-page.component';

@Component({
  selector: 'app-new-full-screen-billing-advice',
  templateUrl: './new-full-screen-billing-advice.component.html',
  styleUrls: ['./new-full-screen-billing-advice.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MMM-YYYY',
        },
        display: {
          dateInput: 'DD-MMM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
        useUtc: true,
      },
    },
    { provide: TOASTER_MESSAGE_SERVICE_TOKEN, useClass: ToasterMessageService },
    GetNameByIdPipe
  ],
})

export class NewFullScreenBillingAdviceComponent implements OnInit {
  @Output() closeDialog: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('resourceLoadingComponent') resourceLoadingComponent: ResourceLoadingLandingPageComponent;
  @Input() dialogData: any;
approvalForBillingAdvice: boolean = false;
approvalForRevertMilestone: boolean = false;
approvalForCreditNote: boolean = false;
approvalForValueDeviations: any;
workflowExist: boolean;
oldBillingValue: any;
blanketPo: any;
approvalForHoursDeviations: any;
highlightHoursDeviations: boolean;
deviationAllowedPercentage: any;
newBillingValue: any;
billing_advice_year: any;
billing_advice_month: any;
milestone_type: any;
previousMilestoneCheck: boolean=false;
isComponentLoading: boolean=true;
projectQuoteList: any=[];
actual_billable_hours_position =0
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick()
  }
  @ViewChild('fullScreen') divRef: ElementRef;
  @ViewChild('ivd') datepicker: MatDatepicker<Date>;
  constructor(
    private exceltoJson:JsonToExcelService, 
    private PmMasterService :PmMasterService, 
    private toasterService: ToasterMessageService,
    private PmBillingService: PmBillingService,
    @Inject(DOCUMENT) private document: any,
    private router: Router, 
    private attachmentService: AttachmentServiceService,
    public dialog: MatDialog, 
    private PmAuthorizationService: PmAuthorizationService,
    private _utilityService: UtilityService, 
    private authService: PmAuthorizationService,
    private _loginService: LoginService,
    private getNameByIdPipe: GetNameByIdPipe,
    private route: ActivatedRoute
  ) {
   }
   versionList: any[] = [];
   isLoading: boolean = false;
   actionData:any;
   currentVersionId: number;
   isVesrionHistory:any;
  items: Item[] = [];
  sortField: string | null = null;
  roundoff_sync: boolean=false;
  roundoff_sync_updated_on: any;
  sortDirection: 'asc' | 'desc' = 'asc';
  loading:boolean=true
  display:boolean
  datasaved:boolean=false
  billingStepper: boolean=false;
  invoiceStepper: boolean=false;
  checkBillingStepper: boolean=true;
  billingInfo:any
  formConfig:any
  minDate: Date | null = null;
  maxDate: Date | null = null;
  invoiceDateConfig:any;
  valid: boolean = true;
  submitProgress: boolean=false;
  creditNoteSelected: boolean=false;
  saveDisabled: boolean;
  billing_type:any
  mode:any='Create'
  billingInfoNew:any
  code:any
  project_end_date:any
  project_start_date:any
  quote:any=[]
  positionBased:boolean
  employeeBased:boolean
  PositionData:any=[]
  position_name: string;
  previousMilestoneValue: any=0;
  start_date: Date;
  end_date:Date;
  timesheet_hours:any=0
  planned_hours:any=0
  logged_hours:any=0
  billable_hours:any=0
  non_billable_hours:any=0
  total_hours:any=0
  per_hour_rate:any=0
  value:any=0
  actual_billable_hours:any=0
  previous_non_billable_hours:any=0
  previous_billable_hours:any=0
  previous_timesheet_hours:any=0
  current_month_per_hour_rate: any=0;
  current_month_revenue: any=0;
  cummulative_month_progress: any=0;
  current_month_progress: any=0;
  showToggle:boolean=false
  quote_position_list:any=[]
  withOpportunity:any
  button: any;
  rounded_hours:any=0
  retrieveMessages: any = [];
  project_daily_working_hours:number
  deliverableID: any;
  actual_billable_days:any=0
  confirmDisabled: boolean;
  fieldOutline: any;
  fullscreen = false;
  content_height:any='420px'
  resizing = false;
  resizeStartX: number;
  item_status_id: any;
  at_risk: any;
  startWidth: number;
  data:any
  noteHeader:any
  dynamicHeight: any;
  dynamicinnerHeight: any;
  ItemID:any
  projectID:any
  milestoneID:any
  item_value:any
  project_name:any
  item_name:any
  font_size_currency:any='9px'
  currency_font_size:any = '12px'
  quote_id:number;
  milestone_month: any;
  milestone_year: any;
  commentsInput = {
    application_id: null,
    unique_id_1: '',
    unique_id_2: '',
    application_name: null,
    title: '',
  }
  rateCardList:any=[]
  resourceTypeList: any=[];
  customer_id:any
  editPerhourrate:boolean=false
  downloadBillingAdvice:boolean=false
  actual_hours_config:boolean=false
  po_number:any
  actual_hours_entry:any
  zifo_hours_right:any
  zifo_hours_left:any
  zifo_previous_hours_right:any
  billingPlanData: any=[];
  zifo_previous_hours_left:any
  zifo_previous_timesheet_hours:any=0
  zifo_timesheet_hours:any=0
  based_on_rate_config:boolean=false
  actual_hours_entry_based_on_rate:any
  planned_hours_entry:any
  enable_financial:boolean=true
  calculationRoundOffBased: boolean=false;
  after_zifo_timesheet_hours: any=0;
  after_rounded_hours: any=0;
  value_error:boolean=false
  milestoneStartDate: any;
  milestoneEndDate: any;
  enable_actual_billable_hours_default:boolean=true
  roundOffSyncAccess: boolean=false;
  planned_hours_if_timesheet_not_present: boolean=false;
  monthly_working_hours: any=0;
  monthly_hours_flag: boolean=false;
  displayMilestoneStartDate: any;
  displayMilestoneEndDate: any;
  billingAdviceNewVersion:boolean = false;
  is_employee_level: boolean=false;
  is_disabled: boolean=false;
  milestoneFont:any;
  templateId:number = 1;
  milestone_status_id: any;
  opportunity_status_id:any;
  opportunity_status_name:any;
  isFinancialValuesHidden:boolean=false
  quote_position_location: any;
  oldItems: any=[];
  baseline_planned_effort: any=0;
  revised_per_hour_rate: any=0;
  total_revenue: any=0;
  future_revenue: any=0;
  revenue_till_previous_month: any=0;
  revenue_till_date: any=0;
  future_planned: any=0;
  current_planned: any=0;
  actual_till_previous_date: any=0;
  previousMilestoneCondition: boolean=false;
  invoiceGridConfig:any = [
    {
      id:1,
      label:'Description',
      key:'description',
      width:25,
      type:'inputTextArea',
      totalVisible:false,
      isVisible:true
    },
    {
      id:2,
      label:'Rate',
      key:'rate',
      width:12,
      type:'inputDecimal',
      totalVisible:true,
      isVisible:true
    },
    {
      id:3,
      label:'Quantity',
      key:'quantity',
      width:12,
      type:'inputNumber',
      totalVisible:false,
      isVisible:true
    },
    {
      id:4,
      label:'Unit',
      key:'unit',
      width:12,
      type:'dropdown-text',
      totalVisible:false,
      isVisible:true,
      dropdownList:'unitList'
    },
    {
      id:5,
      label:'Amount',
      key:'amount',
      width:13,
      type:'text-decimal',
      totalVisible:true,
      isVisible:true
    },
    {
      id:6,
      label:'Action',
      key:'action',
      width:12,
      type:'action',
      totalVisible:false,
      isVisible:true
    }
  ]
  gridConfig:any = [];
  invoiceList: Invoice[] = [];
  sectionList:any = [
    {
      key_id:1,
      label:'Billing Advice',
      is_active:true,
      is_visible:true,
      is_completed:false,
      stepperName:'billing_advice_stepper'
    }, 
    {
      key_id:2,
      label:'Invoice Details',
      is_active:true,
      is_visible:true,
      is_completed:false,
      stepperName:'invoice_details_stepper'
    },
    {
      key_id:3,
      label:'Billing Plan',
      is_active:true,
      is_visible:true,
      is_completed:false,
      stepperName:'billing_plan_stepper'
    }
  ]
  stepId: number = 1;
  unitList:any = []
  actionTypeList:any = [
    {
      id:true,
      name:'Employee'
    },
    {
      id:false,
      name:'Position'
    }
  ]
  editMode:boolean = true;
  subs = new SubSink();
  noOFDigits = 16;
  allowActualHrsDecimal : boolean = false;
  matrixConfig: any = null;
  saveButtonEnabled: boolean = false;
  cancelButtonEnabled: boolean=false;
  reversalButtonEnabled: boolean=false;
  creditNoteButtonEnabled: boolean=false;
  accruedButtonEnabled: boolean = false;
  invoiceButtonEnabled: boolean = false;
  partialInvoiceButtonEnabled: boolean=false;
  enableCheckBoxClass: boolean=false;
  service_type_id:any;
  invoiceLoader: boolean = false;
  po_value:any
  quoteData: any;
  decimalValue: number = 2;
  allowTimesheetInDecimal: boolean = false;
  default_hours_config :boolean=false;
  billing_advice_action: any="work"
  oldInvoiceDate : any;
  newInvoiceDate: any;
  oldStatus: any;
  newStatus: any;
  milestone_status_list: any;
  tolerance_value: number =0;
  opportunity_status_config:any;
  is_comment_present: boolean=false;
  is_attachment_present: boolean=false;
  approverView: boolean = false;
  workflowDetails: any;
  approvalForActualDeviations: any;
  actual_till_date: any=0;
  future_effort_required: any=0;
  total_quote_position_hours: any=0;
  total_quote_position_value: any=0;

  bill_enable: boolean = false;
  async ngOnInit() {
    this.invoiceLoader = true;
    this.ItemID = parseInt(this.router.url.split("/")[4])
    this.projectID = parseInt(this.router.url.split("/")[3])
    this.milestoneID = parseInt(this.router.url.split("/")[5])

    this.route.queryParams.subscribe(params => {
      this.billing_advice_action = params['action'] ? params['action'] :"work";
    });

    this.route.queryParams.subscribe(params => {
      let source = params['source'];
      console.log('Source:', source); // This will log 'bill' if source=bill in the URL
      if(source || source == 'bill'){
        this.bill_enable = true;
      }
      else{
        this.bill_enable = false;
      }
    });

    console.log("Actions", this.billing_advice_action)
   this.calculateDynamicContentHeight()  
  //  this.data={ "Mid": 1136378, "start_date": moment('2024-01-31').format('DD-MMM-YYYY'), "end_date": moment('2024-02-29').format('DD-MMM-YYYY'), "itemID": 81415, "projectID": 6509, "status_id": 4, "value": '890,989', "status_name": 'Execution', "background": "linear-gradient(0deg, #FFF8EC, #FFF8EC),linear-gradient(0deg, #FFBD3D, #FFBD3D)", "border": "1px solid #FFBD3D", "color": "#FFBD3D", "label": 'Test',"code":'USD' }
  //  this.dialogData={ "Mid": 1136378, "start_date": moment('2024-01-31').format('DD-MMM-YYYY'), "end_date": moment('2024-02-29').format('DD-MMM-YYYY'), "itemID": 81415, "projectID": 6509, "status_id": 4, "value": '890,989', "status_name": 'Execution', "background": "linear-gradient(0deg, #FFF8EC, #FFF8EC),linear-gradient(0deg, #FFBD3D, #FFBD3D)", "border": "1px solid #FFBD3D", "color": "#FFBD3D", "label": 'Test',"code":'USD' }
    await this.PmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });


    if(this.formConfig.length > 0){
      this.roundOffSyncAccess = _.findWhere(this.formConfig, {type:"new-billing-advice", field_name:"roundOffSync", is_active : true}) ? true: false
      this.retrieveMessages = _.where(this.formConfig, { type: "new-billing-advice", field_name: "messages", is_active: true });
      const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
      this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
      document.documentElement.style.setProperty('--adviceButton', this.button)
      this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
      document.documentElement.style.setProperty('--billingAdviceField', this.fieldOutline);
      this.noteHeader = retrieveStyles.length > 0 ? retrieveStyles[0].data.note_header ? retrieveStyles[0].data.note_header : "rgb(66, 191, 221)" : "rgb(66, 191, 221)";
      document.documentElement.style.setProperty('--milestoneNoteheader', this.noteHeader)
      this.milestoneFont = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
      document.documentElement.style.setProperty('--milestoneFont', this.milestoneFont)
      
    }
    this.invoiceDateConfig = _.where(this.formConfig, { type: 'new-billing-advice', field_name: 'invoice_date', is_active: true });
    this.opportunity_status_config= _.where(this.formConfig, { type: 'new-billing-advice', field_name: 'opportunity_status_config', is_active: true });
    // console.log('Form Config:', this.formConfig);
    this.gridConfig =  _.findWhere(this.formConfig, { type: "new-billing-advice", field_name: "invoice-grid", is_active: true });
    // console.log('Grid Config:',this.gridConfig)
    if(this.gridConfig && this.gridConfig['config'] && this.gridConfig['config'].length > 0){
      this.invoiceGridConfig = JSON.parse(JSON.stringify(this.gridConfig['config']));
    }

    await this.PmBillingService.getMilestoneDetails(this.projectID,this.ItemID,this.milestoneID).then((res=>{
      if(res['messType']=='S' && res['data'].length>0){
        let color=JSON.parse(res['data'][0].status_color)
        this.quote_id=parseInt(res['data'][0].quote_id)
        this.po_number=res['data'][0].po_number
        this.po_value=res['data'][0].po_value ? res['data'][0].po_value : null
        this.milestone_month = res['data'][0].milestone_month;
        this.milestone_year = res['data'][0].milestone_year;
        this.milestone_status_id = res['data'][0].milestone_status_id; 
        this.deliverableID = res['data'][0].deliverable_id;
        this.milestone_type = res['data'][0].milestone_type;
        this.oldStatus = {
          Status: res['data'][0].status_name
        } 
        this.data={ "Mid": this.milestoneID, "start_date": res['data'][0].planned_start_date, "end_date": res['data'][0].planned_end_date, "itemID": this.ItemID, "projectID": this.projectID, "status_id": res['data'][0].milestone_status_id, "value": JSON.parse(res['data'][0].milestone_value), "status_name": res['data'][0].status_name, "background": color[0].background, "border": color[0].border, "color":color[0].color, "label": res['data'][0].milestone_name,"invoice_date":res['data'][0].invoice_date!="" && res['data'][0].invoice_date!="null" && res['data'][0].invoice_date!=null ? res['data'][0].invoice_date : "","gantt_id":res['data'][0].gantt_id,"quote_id":parseInt(res['data'][0].quote_id),"milestone_type":res['data'][0]?.milestone_type,milestone_type_name:res['data'][0]?.milestone_type_name, "deliverableID": this.deliverableID, "quoteID": this.quote_id }
        this.dialogData={ "Mid": this.milestoneID, "start_date": res['data'][0].planned_start_date, "end_date": res['data'][0].planned_end_date, "itemID":this.ItemID, "projectID": this.projectID, "status_id": res['data'][0].milestone_status_id, "value": JSON.parse(res['data'][0].milestone_value), "status_name": res['data'][0].status_name, "background": color[0].background, "border": color[0].border, "color":color[0].color, "label":res['data'][0].milestone_name,"invoice_date":res['data'][0].invoice_date!="" && res['data'][0].invoice_date!="null" && res['data'][0].invoice_date!=null ? res['data'][0].invoice_date : "","gantt_id":res['data'][0].gantt_id,"quote_id":parseInt(res['data'][0].quote_id),"milestone_type":res['data'][0].milestone_type,milestone_type_name:res['data'][0]?.milestone_type_name}
        this.billing_advice_month = moment(res['data'][0].planned_start_date).format("MM")
        this.billing_advice_year = moment(res['data'][0].planned_start_date).format("YYYY")
        
        if(res['data'][0].milestone_status_id>=8)
        {
          this.roundOffSyncAccess = false;
        }
        this.displayMilestoneStartDate = res['data'][0].show_start_date;
        this.displayMilestoneEndDate = res['data'][0].show_end_date;
        this.milestoneStartDate = res['data'][0].planned_start_date;
        this.milestoneEndDate = res['data'][0].planned_end_date;
        this.roundoff_sync = res['data'][0].roundoff_resync_flag;
        this.roundoff_sync_updated_on = res['data'][0].roundoff_sync_updated_on ? moment(res['data'][0].roundoff_sync_updated_on).format("DD-MMM-YYYY hh:mm A") :""
      }
    })) 
    if(this.milestone_status_id == 17){
      await this.PmBillingService.getWorkflowDetailsBasedOnSource(this.projectID,this.ItemID,this.milestoneID).then((res=>{
        if(res['messType']=='S' && res['data']){
          this.workflowDetails = res['data']
          let oid = this._loginService.getProfile().profile.oid
          if (_.includes(this.workflowDetails['appr_oid_array'], oid)) {
            this.approverView = true;
          }
        }
      }))
    }
    this.milestone_status_list = this.PmMasterService.milestone_status_list
    await this.PmBillingService.getOpportunityStatusDetails(this.projectID,this.ItemID, this.quote_id).then((res=>{
      if(res['messType']=='S' && res['data'].length>0){
        this.opportunity_status_id=res['data'][0]['status_id']
        this.opportunity_status_name=res['data'][0]['status_name']
        
      }
    }))

    if(this.previousMilestoneCheck)
    {
      await this.PmBillingService.checkPreviousMilestoneStatus(this.milestoneID).then((res: any)=>{
        
        if(res['messType']=="S")
        {

          if(_.contains([8,9,10,11,12,15,6,18],res['data']['milestone_status_id']))
            this.previousMilestoneCondition = false
          else
            this.previousMilestoneCondition = true
        }
        
    
      })
    }

    await this.PmBillingService.checkCommentPresentForMilestones([this.milestoneID]).then((res: any)=>{
      if(res && res.length>0)
      {
          this.is_comment_present = true;
      }
      else
      {
          this.is_comment_present = false;
      }
    })

    await this.PmMasterService.getManPowerResourceType().then((res)=>{
      this.resourceTypeList = res;
    })

    await this.PmBillingService.checkAttachmentPresentForMilestones([this.milestoneID]).then((res: any)=>{
      if(res && res.length>0)
      {
          this.is_attachment_present =true;
      }
      else
      {
          this.is_attachment_present =false;
      }
    })

    if (this.invoiceDateConfig && this.invoiceDateConfig.length > 0) {
      let config = this.invoiceDateConfig[0];
      let negativeDuration = config.negative_duration - 1 || 0;
      let positiveDuration = config.positive_duration + 1 || 0;
      let endDate;
      if (config.current_date) {
          endDate = moment();
      } else {
          endDate = moment(this.displayMilestoneEndDate);
      }
      let minDate = endDate.clone().subtract(negativeDuration, 'days').format("YYYY-MM-DD")
      let maxDate = endDate.clone().add(positiveDuration, 'days').format("YYYY-MM-DD")
      if (config.milestone_start_date) {
        const startDate = moment(this.displayMilestoneStartDate);
        if (startDate.isBefore(minDate)) {
          minDate = startDate.add(1, 'days').format("YYYY-MM-DD")
        }
      }
      this.minDate = minDate;
      this.maxDate = maxDate;
    }
    else{
      this.minDate =null;
      this.maxDate =null;
    }
    await this.PmBillingService.getProjectQuote(this.projectID,this.ItemID).then((res)=>{
      if(res['messType']=='S' && res['data'].length>0){
        this.code=res['data'][0].currency_code
        this.project_end_date=moment(res['data'][0].planned_end_date).format('YYYY-MM-DD')
        this.project_start_date=moment(res['data'][0].planned_start_date).format('YYYY-MM-DD')
        this.item_status_id = res['data'][0].item_status_id
        this.at_risk = res['data'][0].at_risk
        this.service_type_id = res['data'][0]?.service_type_id ? res['data'][0].service_type_id : null;
        // this.quote=res['data'][0].quote_id
        if(res['data'][0].item_value!=null){
        this.item_value=JSON.parse(res['data'][0].item_value)
        }
        this.item_name=res['data'][0].item_name
        this.project_name=res['data'][0].project_name
        this.withOpportunity=res['data'][0].with_opportunity
        this.customer_id=res['data'][0].end_customer_id

        this.quoteData={
          "quote_id": this.quote_id,
          "project_id": this.projectID,
          "item_id": this.ItemID,
          "start_date": this.project_start_date,
          "end_date": this.project_end_date,
          "deliverable_id": this.deliverableID

        }
        // if(this.withOpportunity==0){
        // this.po_number=res['data'][0].po_number
        // }
        
      }
      })
      await this.PmBillingService.getProjectDailyWorkingHours(this.projectID,this.ItemID).then((res)=>{
        if(res['messType']=='S'){
          if(res && res['data'].length>0)
          {
            if(res['data'][0].daily_working_hours!=0)
            {
                    this.project_daily_working_hours=res['data'][0].daily_working_hours ? res['data'][0].daily_working_hours :8
                    this.monthly_working_hours = res['data'][0].monthly_hours
                    this.tolerance_value = res['data'][0].tolerance_value
            }
            else{
                this.project_daily_working_hours=8
            }
          }
        }
      })
      if(this.withOpportunity==1){
        await this.PmBillingService.getProjectFinnacialData(this.ItemID).then(async (res)=>{
          if(res['messType']=='S'){
            if(res['data'].length>0){
              this.projectQuoteList = res['data']
              let project_has_quote=0
              // this.po_number=res['data'][0].po_number
              for(let i=0;i<res['data'].length;i++){
                if(res['data'][i].quote_id!=null){
                  project_has_quote=1
                  // this.quote_id=res['data'][i].quote_id
                  this.quote.push(res['data'][i].quote_id)
                }
              }
              if(project_has_quote>0){
                this.employeeBased=false
                this.positionBased=true
                this.showToggle=true
                await this.PmBillingService.getQuotePosition(this.quote, this.resourceTypeList).then((res)=>{
                  if(res['messType']=='S'){
                   this.quote_position_list=res['data']
                  }
            })
              }
              else{
                this.employeeBased=true
                this.positionBased=false
                this.showToggle=false
              }
            }
          }
          else{
            this.employeeBased=true
            this.positionBased=false
            this.showToggle=false
          }
        })
      }
      else{
        await this.PmBillingService.getCustomerRateCard(this.customer_id, moment().format("YYYY-MM-DD")).then((res) => {
          if (res['messType'] == "S") {
            this.quote_position_list = res['data']
            this.employeeBased=false
            this.positionBased=true
            this.showToggle=true
          }
          else{
            this.employeeBased=true
            this.positionBased=false
            this.showToggle=false
          }
        })
    
      }
    //   if(this.quote!=null){
    //     this.employeeBased=false
    //     this.positionBased=true
    //     this.showToggle=true
    //     await this.PmBillingService.getQuotePosition(this.quote).then((res)=>{
    //       if(res['messType']=='S'){
    //        this.quote_position_list=res['data']
    //       }
    // })
    //   }
    //   else{
    //     this.employeeBased=true
    //     this.positionBased=false
    //     this.showToggle=false
    //   }
    // Initialize your items here (e.g., from a service or API).
    // this.items = [
    //   { id: 2, name: 'Item 1', description: 'Description 1' },
    //   { id: 1, name: 'Item 2', description: 'Description 2' },
    //   // Add more items as needed
    // ];
    this.employeeBased=await this.PmAuthorizationService.getProjectWiseObjectAccess(this.projectID,this.ItemID,89)
    this.positionBased=await this.PmAuthorizationService.getProjectWiseObjectAccess(this.projectID,this.ItemID,90)
    this.editPerhourrate=await this.PmAuthorizationService.getProjectWiseObjectAccess(this.projectID,this.ItemID,91)
    this.downloadBillingAdvice=await this.PmAuthorizationService.getProjectWiseObjectAccess(this.projectID,this.ItemID,92)
    this.isFinancialValuesHidden=await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 178)
    if(this.employeeBased && this.positionBased){
      this.showToggle=true
      this.employeeBased=false
      this.positionBased=true
    }
    else{
      this.showToggle=false
    }
    const billing = _.where(this.formConfig, { type:"new-billing-advice", field_name: "billing", is_active: true })
    this.billing_type = billing.length > 0 ? billing[0].billing : 1
    const actual_hours_config = _.where(this.formConfig, { type:"new-billing-advice", field_name: "actual_hours_config_based_on_activity"})
    this.actual_hours_config = actual_hours_config.length > 0 ? actual_hours_config[0].is_active : false
    const actual_hours_entry = _.where(this.formConfig, { type:"new-billing-advice", field_name: "actual_hours_entry",is_active:true})
    this.actual_hours_entry = actual_hours_entry.length > 0 ? actual_hours_entry[0].text : 'billable_hours'
    const zifo_hours_left = _.where(this.formConfig, { type:"new-billing-advice", field_name: "zifo_hours_left",is_active:true})
    this.zifo_hours_left = zifo_hours_left.length > 0 ? zifo_hours_left[0].text : 'billable_hours'
    const zifo_hours_right = _.where(this.formConfig, { type:"new-billing-advice", field_name: "zifo_hours_right",is_active:true})
    this.zifo_hours_right = zifo_hours_right.length > 0 ? zifo_hours_right[0].text : 'non_billable_hours_flexi'
    const zifo_previous_hours_left = _.where(this.formConfig, { type:"new-billing-advice", field_name: "zifo_previous_hours_left",is_active:true})
    this.zifo_previous_hours_left = zifo_previous_hours_left.length > 0 ? zifo_previous_hours_left[0].text : 'previous_billable_hours'
    const zifo_previous_hours_right = _.where(this.formConfig, { type:"new-billing-advice", field_name: "zifo_previous_hours_right",is_active:true})
    this.zifo_previous_hours_right = zifo_previous_hours_right.length > 0 ? zifo_previous_hours_right[0].text : 'previous_non_billable_hours_flexi'
    const based_on_rate_config = _.where(this.formConfig, { type:"new-billing-advice", field_name: "based_on_rate_config",is_active:true})
    this.based_on_rate_config = based_on_rate_config.length > 0 ? true: false
    const actual_hours_entry_based_on_rate = _.where(this.formConfig, { type:"new-billing-advice", field_name: "actual_hours_entry_based_on_rate",is_active:true})
    this.actual_hours_entry_based_on_rate = actual_hours_entry_based_on_rate.length > 0 ? actual_hours_entry_based_on_rate[0].text : 'billable_days'
    const planned_hours_entry = _.where(this.formConfig, { type:"new-billing-advice", field_name: "planned_hours_entry",is_active:true})
    this.planned_hours_entry = planned_hours_entry.length > 0 ? planned_hours_entry[0].text : 'planned_hours'
    const enable_financial = _.where(this.formConfig, { type:"new-billing-advice", field_name: "enable_financial",is_active:true})
    this.enable_financial = enable_financial.length > 0 ? true: false
    const calculationRoundOffBased = _.where(this.formConfig, { type:"new-billing-advice", field_name: "calculationRoundOffBased",is_active:true})
    this.calculationRoundOffBased = calculationRoundOffBased.length > 0 ? true: false
    const enable_value_error= _.where(this.formConfig, { type:"new-billing-advice", field_name: "enable_value_error",is_active:true})
    this.value_error = enable_value_error.length > 0 ? true: false
    const actual_billable_hours_default= _.where(this.formConfig, { type:"new-billing-advice", field_name: "actual_billable_hours_default",is_active:true})
    this.enable_actual_billable_hours_default = actual_billable_hours_default.length > 0 ? true: false
    const planned_hours_if_timesheet_not_present = _.where(this.formConfig,{ type:"new-billing-advice", field_name: "planned_hours_enable_timesheet_not_present",is_active:true})
    const checkVesrionHistory = _.where(this.formConfig,{ type:"new-billing-advice", field_name: "version-history",is_active:true})
    this.isVesrionHistory = checkVesrionHistory?.length > 0 ? true: false
    const actual_billable_hours= _.where(this.formConfig, { type:"new-billing-advice", field_name: "default-hours-prj",is_active:true})
    this.default_hours_config = actual_billable_hours.length > 0 ? true: false
    await this.PmBillingService.getMilestoneMatrix().then(async(res) => {
      if (res['messType'] == "S") {
        let matrixConfigData = res['data']
        let matrixCheckData = {service_type_id:this.service_type_id, milestone_status_id:this.data.status_id,milestone_type_id:this.data.milestone_type, is_active : 1,is_bills : (this.bill_enable ? 1 : 0)}
        
        this.matrixConfig = _.findWhere(matrixConfigData, matrixCheckData)
        console.log(this.matrixConfig)
        this.approvalForBillingAdvice = (this.matrixConfig && this.matrixConfig.approval_for_billing_advise) ? true : false
        this.highlightHoursDeviations = (this.matrixConfig && this.matrixConfig.highlight_hours_deviations) ? true : false
        this.approvalForHoursDeviations = (this.matrixConfig && this.matrixConfig.approval_for_hours_deviations) ? ( this.milestone_status_id == 15 ? false : true ) : false
        this.approvalForValueDeviations = (this.matrixConfig && this.matrixConfig.approval_for_value_deviations) ? ( this.milestone_status_id == 15 ? true : false ) : false
        this.approvalForActualDeviations = (this.matrixConfig && this.matrixConfig.approval_for_actual_deviations) ? ( this.milestone_status_id == 15 ? false : true ) : false
        this.approvalForCreditNote = (this.matrixConfig && this.matrixConfig.approval_for_credit_note) ? true : false
        this.approvalForRevertMilestone = (this.matrixConfig && this.matrixConfig.approval_for_revert) ? true : false
        this.deviationAllowedPercentage = (this.matrixConfig && this.matrixConfig.value_deviation_percentage) ? this.matrixConfig.value_deviation_percentage : 0
        this.allowActualHrsDecimal = (this.matrixConfig && this.matrixConfig.billing_milestone_value_check) ? true : false
        this.monthly_hours_flag = (this.matrixConfig && this.matrixConfig.monthly_hours_display) ? true: false;
        this.previousMilestoneCheck = (this.matrixConfig && this.matrixConfig.previous_milestone_check) ? true : false;

        if(this.monthly_hours_flag)
        {
            await this.PmBillingService.getBillingAdviceBillingPlanData(this.projectID, this.ItemID, this.quote_id).then((res)=>{
              if(res['messType']=="S")
              {
                  this.billingPlanData = res['data']
              }
            })
        }

      }
      else{
        this.matrixConfig = null;
        this.display=false
        this.loading=false
      }
    })
    await this.updateSectionList();
    if(!this.bill_enable){
      await this.PmBillingService.getLumpsumBillingAdviceData(this.data,this.billing_type,this.actual_hours_config,this.withOpportunity).then(async (res:any)=>{
      
        if(this.billingStepper)
        {
            if(res['messType']=="S")
            {
              if(res['data'].length==0)
              {
                this.checkBillingStepper =false;
              }
            }
        }
        if(res['messType']=='S'){
        if(res['data'].length>0){ 
        this.items=res['data']

       
  
        await this.PmBillingService.getBillingInfo(this.data.Mid).then((result:any)=>{
          
          if(result['messType']=='S'){
            this.mode='Update'
            
            this.billingInfo=JSON.parse(result['data'][0].t_and_m_billing_milestone_info)
            this.billingInfoNew=result['res']
            for(let items of this.items){
              
              items['zifo_timesheet_hours']=parseFloat((items[this.zifo_hours_left]+items[this.zifo_hours_right]).toFixed(2))
              items['zifo_previous_timesheet_hours']=parseFloat((items[this.zifo_previous_hours_left]+items[this.zifo_previous_hours_right]).toFixed(2))
              if(items['is_overlapped'] && items['is_overlapped']==1){
                items['after_zifo_timesheet_hours'] = items['overall_total_planned_hours'] !=0 ? parseFloat(((items['planned_hours'] / items['overall_total_planned_hours'])*(items['zifo_timesheet_hours'])).toFixed(2)) :0
              }
              else{
                items['after_zifo_timesheet_hours'] = items['zifo_timesheet_hours']
              }
              for(let item of this.billingInfoNew){
                if(items['id']==item['id']){
                  items['comments'] = item['comments']
                  items['is_selected'] =false;
                  items['future_effort_required'] = item['future_effort_required']
                  items['actual_billable_hours_position'] = item['actual_billable_hours_position']
                  if(this.dialogData.status_id<8)
                    {
                  // items['non_billable_hours']=item['non_billable_hours']
                  // items['billable_hours']=item['billable_hours']
                  // items['planned_hours']=item['planned_hours']
                  items['per_hour_rate'] = item['per_hour_rate']
                  items['value'] = item['value']
                  // items['timesheet_hours'] = item['timesheet_hours']
                  // items['logged_hours'] = item['logged_hours']
                  if(item['actual_billable_hours']!=0){
                    items['actual_billable_hours']=item['actual_billable_hours']
                  }
                  else if(item['actual_billable_hours']==0 && this.default_hours_config){
                    items['actual_billable_hours']=item['actual_billable_hours']
                  }
                  else{
                    if(this.actual_hours_config){
                     
                      if(this.enable_actual_billable_hours_default){
                      items['actual_billable_hours']=items['actual_billable_hours']
                      }
                    }
                    else{
                      
                      if(this.based_on_rate_config){
                        let temp=_.where(this.quote_position_list,{id:items['rate_card_id']})
                       
                        if(temp.length>0){
                            if(temp[0].unit==2){
                              if(this.enable_actual_billable_hours_default){
                              items['actual_billable_hours']=items[this.actual_hours_entry_based_on_rate]
                              }
                            }
                            if(temp[0].unit==1)
                            {
                              if(this.enable_actual_billable_hours_default){
                              items['actual_billable_hours']=items[this.actual_hours_entry]
                              }
                            }
                        }
                      }
                      else{
                        if(this.enable_actual_billable_hours_default){
                       items['actual_billable_hours']=items[this.actual_hours_entry]
                        }
                      }
                      
                    }
                  }
                  if(item['planned_hours']!=0){
                    items['planned_hours']=item['planned_hours']
                  }
                  else{
                    items['planned_hours']=items[this.planned_hours_entry]
                  }
                  items['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                  // items['previous_timesheet_hours']=item['previous_timesheet_hours']
                  // items['previous_billable_hours']=item['previous_billable_hours']
                  // items['previous_non_billable_hours']=item['previous_non_billable_hours']
                  // items['entity_id']=item['entity_id']
                  // items['division_id']=item['division_id']
                  // items['sub_division_id']=item['sub_division_id']
                  // items['id']=item['id']
                  // items['rate_card_id']=item['rate_card_id']
                }
                else{
                  items['per_hour_rate'] = item['per_hour_rate']
                  items['value'] = item['value']
                  items['timesheet_hours']=item['timesheet_hours']
                  items['billable_hours']=item['billable_hours']
                  items['non_billable_hours']=item['non_billable_hours']
                  items['previous_timesheet_hours']=item['previous_timesheet_hours']
                  items['previous_non_billable_hours']=item['previous_non_billable_hours']
                  items['previous_billable_hours']=item['previous_billable_hours']
                  items['planned_hours']=item['planned_hours']
                  items['rounded_hours']=item['rounded_hours']
                  items['actual_billable_hours']=item['actual_billable_hours']
                  item['temp']=0
                  items['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                  items['zifo_timesheet_hours']=parseFloat((items[this.zifo_hours_left]+items[this.zifo_hours_right]).toFixed(2))
                  items['zifo_previous_timesheet_hours']=parseFloat((items[this.zifo_previous_hours_left]+items[this.zifo_previous_hours_right]).toFixed(2))
                  items['after_zifo_timesheet_hours'] = item['after_zifo_timesheet_hours'] ? item['after_zifo_timesheet_hours'] :0
                  items['after_rounded_hours'] = item['after_rounded_hours'] ? item['after_rounded_hours'] :0
                }
                }
              }
            }

            if(this.calculationRoundOffBased)
            {
              for(let items of this.items)
              {
                  items['after_rounded_hours'] = this.getSumTimesheetHoursForAssociate(this.items, items)
                  if(this.dialogData.status_id<8)
                  { 
                    if(this.enable_actual_billable_hours_default){
                    items['actual_billable_hours'] = items['after_rounded_hours'] 
                    }
                  }
                  for(let item of this.billingInfoNew)
                  {
                    if(items['id']==item['id'])
                    {
                      
                          items['actual_billable_hours'] = item['actual_billable_hours']
                      
                    }
                  }

              }
            }
            this.datasaved=true
          }
          else{
            if(this.dialogData.status_id<8){
            for(let items of this.items){
             
              items['zifo_timesheet_hours']=parseFloat((items[this.zifo_hours_left]+items[this.zifo_hours_right]).toFixed(2))
              items['zifo_previous_timesheet_hours']=parseFloat((items[this.zifo_previous_hours_left]+items[this.zifo_previous_hours_right]).toFixed(2))
              items['planned_hours']=items[this.planned_hours_entry]
              if(items['is_overlapped'] && items['is_overlapped']==1){
                items['after_zifo_timesheet_hours'] = items['overall_total_planned_hours'] != 0 ? parseFloat(((items['planned_hours'] / items['overall_total_planned_hours'])*(items['zifo_timesheet_hours'])).toFixed(2)) : 0
              }
              else{
                items['after_zifo_timesheet_hours'] = items['zifo_timesheet_hours']
              }
                if(this.actual_hours_config){
                  
                  if(this.enable_actual_billable_hours_default){
                  items['actual_billable_hours']=items['actual_billable_hours']
                  }
                }
                else{
                  
                  if(this.based_on_rate_config){
                    let temp=_.where(this.quote_position_list,{id:items['rate_card_id']})
                    
                    if(temp.length>0){
                        if(temp[0].unit==2){
                          if(this.enable_actual_billable_hours_default){
                          items['actual_billable_hours']=items[this.actual_hours_entry_based_on_rate]
                          }
                        }
                        if(temp[0].unit==1)
                        {
                          if(this.enable_actual_billable_hours_default){
                          items['actual_billable_hours']=items[this.actual_hours_entry]
                          }
                        }
                    }
                  }
                  else{
                    if(this.enable_actual_billable_hours_default){
                   items['actual_billable_hours']=items[this.actual_hours_entry]
                    }
                  }
                  
                }
            }

            if(this.calculationRoundOffBased)
            {
              for(let items of this.items)
              {
    
                items['after_rounded_hours'] = this.getSumTimesheetHoursForAssociate(this.items, items)
                if(this.enable_actual_billable_hours_default){
                items['actual_billable_hours'] = items['after_rounded_hours'] 
                }
                
              }
            }
          }
          else{
            this.items=[]
          }
            this.datasaved=true
          }
        })
        
        if(this.positionBased){
        const uniqueIds = this.getUniqueIds(this.items);
        let check_position=0
        for (let i = 0; i < uniqueIds.length; i++) {
          for(let item of this.items){
            item['has_child']=false
            item['expandEnable']=false
            item['isVisible']=false
            if(uniqueIds[i]==item['rate_card_id']){
              check_position=1
              let find_name=_.where(this.quote_position_list,{id:item['rate_card_id']})
              
              if(find_name.length>0){
              this.position_name=find_name[0].name
              this.quote_position_location = find_name[0].location
              }
              item['planned_hours'] = item['planned_hours'] ? item['planned_hours'] :0

              if(item['billable']==1)
                this.planned_hours=this.planned_hours+item['planned_hours']
              this.timesheet_hours=this.timesheet_hours+item['timesheet_hours']
              // this.planned_hours=this.planned_hours+item['planned_hours']
              this.billable_hours=this.billable_hours+item['billable_hours']
              this.non_billable_hours=this.non_billable_hours+item['non_billable_hours']
              this.per_hour_rate=item['per_hour_rate']
              this.value=this.value+item['value']
              this.actual_billable_hours_position = item['actual_billable_hours_position']
              this.actual_billable_hours=this.actual_billable_hours+item['actual_billable_hours']
              this.previous_non_billable_hours=this.previous_non_billable_hours+item['previous_non_billable_hours']
              this.previous_billable_hours=this.previous_billable_hours+item['previous_billable_hours']
              this.previous_timesheet_hours=this.previous_timesheet_hours+item['previous_timesheet_hours']
              this.rounded_hours=this.rounded_hours+item['rounded_hours']
              item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
               this.actual_billable_days=this.actual_billable_days+item['actual_billable_days']
               this.zifo_timesheet_hours=this.zifo_timesheet_hours+item['zifo_timesheet_hours']
               this.after_zifo_timesheet_hours = this.after_zifo_timesheet_hours + item['after_zifo_timesheet_hours']
               this.zifo_previous_timesheet_hours=this.zifo_previous_timesheet_hours+item['zifo_previous_timesheet_hours']
               this.after_rounded_hours = this.after_rounded_hours + (item['after_rounded_hours'] ? item['after_rounded_hours']:0)
               this.is_employee_level = item['is_employee_level']
               this.is_disabled = item['is_disabled']
               this.actual_till_previous_date = item['actual_till_previous_date'] ? item['actual_till_previous_date'] :0
               this.actual_till_date = this.actual_till_previous_date + this.actual_billable_hours
               this.future_effort_required = item['future_effort_required'] ?  item['future_effort_required'] :0
               this.total_quote_position_hours = (item['future_planned'] ? item['future_planned'] : 0) + this.future_effort_required + this.actual_till_date
               this.total_quote_position_value = item['total_quote_position_value']
               this.baseline_planned_effort = item['baseline_planned_effort']
               this.future_planned = item['future_planned']
               this.current_planned = item['current_planned']
               this.revised_per_hour_rate = item['revised_per_hour_rate'] ? item['revised_per_hour_rate'] : item['per_hour_rate']
               this.revenue_till_previous_month = item['revenue_till_previous_month'] ? item['revenue_till_previous_month'] : 0
               this.revenue_till_date = item['revenue_till_date'] ? item['revenue_till_date']:0
               this.future_revenue = item['future_revenue'] ? item['future_revenue'] : (this.future_planned + this.future_effort_required) * this.revised_per_hour_rate
               this.total_revenue = this.revenue_till_date + this.future_revenue
               this.current_month_per_hour_rate = item['current_month_per_hour_rate'] ? item['current_month_per_hour_rate'] : 0
               this.current_month_revenue = item['current_month_revenue'] ? item['current_month_revenue'] :0
               this.current_month_progress = item['current_month_progress'] ? item['current_month_progress'] : 0;
               this.cummulative_month_progress = item['cummulative_month_progress'] ? item['cummulative_month_progress'] :0;
  

               console.log("Total Quote Position Hours", this.total_quote_position_hours, this.total_revenue, this.future_revenue,  this.future_planned, this.future_effort_required, this.current_planned, this.revenue_till_previous_month)

               
            }
          }
          if(check_position==1){

            

           this.PositionData.push({'id':uniqueIds[i],'name':this.position_name,'timesheet_hours':(this.timesheet_hours).toFixed(2),'planned_hours':(this.planned_hours).toFixed(2),'billable_hours':(this.billable_hours).toFixed(2),'non_billable_hours':(this.non_billable_hours).toFixed(2),'per_hour_rate':this.per_hour_rate,'value':this.value.toFixed(2),'actual_billable_hours':this.actual_billable_hours_position,'previous_non_billable_hours':this.previous_non_billable_hours.toFixed(2),'previous_billable_hours':this.previous_billable_hours.toFixed(2),'previous_timesheet_hours':this.previous_timesheet_hours.toFixed(2),'rounded_hours':this.rounded_hours.toFixed(2),'actual_billable_days':this.actual_billable_days.toFixed(2),'zifo_timesheet_hours':this.zifo_timesheet_hours.toFixed(2),'after_zifo_timesheet_hours': this.after_zifo_timesheet_hours.toFixed(2), 'after_rounded_hours': this.after_rounded_hours.toFixed(2), 'zifo_previous_timesheet_hours':this.zifo_previous_timesheet_hours.toFixed(2),'has_child':true,'expandEnable':false,'isVisible':true, 'is_employee_level': this.is_employee_level, 'is_disabled': this.is_disabled, 'quote_position_location': this.quote_position_location, 'actual_till_date': this.actual_till_date, 'future_effort_required': this.future_effort_required, 'total_quote_position_hours': this.total_quote_position_hours, 'display_total_quote_position_hours': this.total_quote_position_hours, 'total_quote_position_value': this.total_quote_position_value,  "baseline_planned_effort": this.baseline_planned_effort, "revised_per_hour_rate": this.revised_per_hour_rate, 'total_revenue': this.total_revenue, 'future_revenue': this.future_revenue, 'revenue_till_date': this.revenue_till_date, 'display_actual_till_previous_date': this.actual_till_previous_date, 'display_actual_till_date': this.actual_till_date, 'display_revenue_till_date': this.revenue_till_date, 'display_total_revenue': this.total_revenue, 'future_planned': this.future_planned, current_month_per_hour_rate: this.current_month_per_hour_rate, current_month_revenue: this.current_month_revenue, current_planned: this.current_planned, revenue_till_previous_month: this.revenue_till_previous_month, current_month_progress: this.current_month_progress, cummulative_month_progress: this.cummulative_month_progress })

           for(let data of this.PositionData)
           {
              if(this.monthly_hours_flag && this.dialogData.status_id<8)
              {
                  
                  let billingPlan = _.where(this.billingPlanData,{month: this.milestone_month, year: this.milestone_year, position_id: data['id']})

                  //console.log("POSTION DATA", data, this.billingPlanData, billingPlan)

                  if(billingPlan.length>0)
                  {
                    data['actual_billable_hours'] = data['actual_billable_hours'] ==0 ? billingPlan[0]['planned_hours'] : data['actual_billable_hours'];
                  }
                  else
                  {
                    data['actual_billable_hours'] = data['actual_billable_hours'] ==0 ? 0 : data['actual_billable_hours']
                  }
              }
           }
           for(let test of this.items){
             if(uniqueIds[i]==test['rate_card_id']){
                 this.PositionData.push(test)
            }
           }
           this.timesheet_hours=0
           this.planned_hours=0
           this.billable_hours=0
           this.non_billable_hours=0
           this.per_hour_rate=0
           this.value=0
           this.actual_billable_hours=0
           this.previous_non_billable_hours=0
           this.previous_billable_hours=0
           this.previous_timesheet_hours=0
           this.actual_billable_days=0
           this.rounded_hours=0
           this.zifo_timesheet_hours=0
           this.after_zifo_timesheet_hours =0;
           this.zifo_previous_timesheet_hours=0;
           this.after_rounded_hours =0;
          }
          else{
            check_position=0
          }
        }
        
        for(let items of this.PositionData){
          if(items['actual_billable_hours']!=undefined && items['revised_per_hour_rate']!=undefined){
            items['value']=items['actual_billable_hours']* items['revised_per_hour_rate']
            }
      }
        if(this.monthly_hours_flag && this.dialogData.status_id<8)
        {
            for(let i=0;i<this.PositionData.length;i++)
            {
              this.getActualBillableHours(this.PositionData[i]['actual_billable_hours'], i, this.PositionData[i]['id'] )
            }
        }
        
        this.loading=false
        this.display=true
      }
      else{
        for(let items of this.items){
          if(items['actual_billable_hours']!=undefined && items['per_hour_rate']!=undefined){
            items['value']=items['actual_billable_hours']* items['per_hour_rate']
            }
      }
        this.loading=false
        this.display=true
      }
       
      }else{
        this.display=false
          this.loading=false
      }
        }
        else{
          this.display=false
          this.loading=false
        }
      })
    }
    else{
      this.positionBased = true;
      await this.PmBillingService.getBillingAdviceDataForBills(this.data,this.withOpportunity).then(async (res:any)=>{
        if(res && res.messType == 'S'){
          this.PositionData = res['data']
          this.datasaved = true;
        }
        else{
          this.PositionData = []
        }
      })
      // this.PositionData = [
      //   {
      //     "project_id": 7008,
      //     "project_item_id": 86624,
      //     "milestone_id": 1100982,
      //     "id": 23119,
      //     "quote_position": 30,
      //     "entity_id": 1,
      //     "division_id": 27,
      //     "sub_division_id": 116,
      //     "billable_hours": 64,
      //     "non_billable_hours": 0,
      //     "timesheet_hours": 64,
      //     "previous_billable_hours": 0,
      //     "previous_non_billable_hours": 0,
      //     "previous_timesheet_hours": 0,
      //     "planned_hours": 125.68306,
      //     "actual_billable_hours": 125,
      //     "per_hour_rate": 130,
      //     "value": 16250,
      //     "associate_id": 3116,
      //     "rounded_hours": 0,
      //     "after_zifo_timesheet_hours": 64,
      //     "comments": null,
      //     "isVisible":true
      //   }
      // ]
      this.loading=false
    }
    const billing_new_version = _.where(this.formConfig, { type:"new-billing-advice", field_name: "billing_new_version", is_active: true })
    this.billingAdviceNewVersion = billing_new_version.length > 0 ? billing_new_version[0].isVisible ? billing_new_version[0].isVisible : false : false

    let retrieveDecimalPlaces = _.where(this.formConfig, {
      type: 'new-billing-advice',
      field_name: 'decimal_places',
      is_active: true,
    });

    this.decimalValue = (retrieveDecimalPlaces && retrieveDecimalPlaces.length > 0) ? (retrieveDecimalPlaces[0].decimal_places ? retrieveDecimalPlaces[0].decimal_places : 2) : 2;
    this.allowActualHrsDecimal = (retrieveDecimalPlaces && retrieveDecimalPlaces.length > 0) ? (retrieveDecimalPlaces[0].actual_in_decimal ? retrieveDecimalPlaces[0].actual_in_decimal : false) : false;
    this.allowTimesheetInDecimal = (retrieveDecimalPlaces && retrieveDecimalPlaces.length > 0) ? (retrieveDecimalPlaces[0].timesheet_in_decimal ? retrieveDecimalPlaces[0].timesheet_in_decimal : false) : false;

    // console.log('allowActualHrsDecimal:',this.allowActualHrsDecimal)
    await this.getVersionHistory();
    await this.initializeInvoice();
    this.invoiceLoader = false;
    this.previousMilestoneValue = this.getTotalBillingValue()
    const oldDate = moment(this.data.invoice_date).format("DD-MMM-YYYY")
    this.oldInvoiceDate = {
      "Invoice Date": oldDate ? oldDate : null
    }

    if(this.approvalForValueDeviations){
      await this.PmBillingService.getWorkflowDetailsBasedOnSource(this.projectID,this.ItemID,this.milestoneID).then((res=>{
        if(res['messType']=='S' && res['data']){
          this.workflowExist = true;
          this.workflowDetails = res['data']
          this.workflowDetails.old_data = this.workflowDetails?.old_data && typeof this.workflowDetails?.old_data == 'string' ? JSON.parse(this.workflowDetails?.old_data) : this.workflowDetails?.old_data;
          this.oldBillingValue = this.workflowDetails.old_data?.old_billing_value ? this.workflowDetails.old_data?.old_billing_value : null;
        }
      }))
    }
    this.oldBillingValue = await this.getTotalBillingValueForVariation()
    this.previousMilestoneValue = this.getTotalBillingValue()

    if(this.withOpportunity){
      await this.PmBillingService.checkOpportunityBlanketPO(this.projectID,this.ItemID,this.quote_id).then((res=>{
        if(res['messType']=='S'){
          this.blanketPo = res['data'] 
        }
      }))
    }
    this.oldItems = JSON.parse(JSON.stringify(this.items));

    if(this.dialogData.status_id <8)
    {
      this.updateIntialBillingAdviceValue()
    }
    this.invoiceLoader = false;
  }

  sort(key: string) {
    
    // this.sortBy = key;
    if (this.sortField === key) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = key;
      this.sortDirection = 'asc';
    }
  }

  onCloseClick(){
    let navigate_to = this.bill_enable ? 'bills' : 'milestone'
    let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;
    this.router.navigateByUrl(navigationUrl);

  }
  async saveData(){
    
    this.saveDisabled = true;
    this.submitProgress = true;
    if(this.bill_enable){
      await this.updateItemsForSavingBills();
    }
    //this.checkNegative();
    if(this.valid){
      let billing_value =0
      let milestone_value=0
      let message
      for(let item of this.dialogData.value){
        if(this.code===item['currency_code']){
             milestone_value=item['value']
        }
      }
      for(let item of this.PositionData)
        {
          if(item['has_child'])
          {
            console.log("Current Month Position Data", item)
            billing_value+=parseFloat(item['current_month_revenue'].toString())
          }
        }

      if(this.data.invoice_date=="" || this.data.invoice_date==" " || this.data.invoice_date==null || this.data.invoice_date=="null" || this.data.invoice_date=="0000-00-00" || this.data.invoice_date=="Invalid Date")
      {
        if(this.invoiceStepper)
        {
          const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_date_err_msg ? this.retrieveMessages[0].errors.invoice_date_err_msg : 'Kindly Enter Valid Invoice Date' : 'Kindly Enter Valid Invoice Date';
          this.toasterService.showWarning(invoice_date_err_msg, 10000);
          this.submitProgress = false;
          return;
        }
        else
        {
          this.data.invoice_date = this.milestoneEndDate;
        }
      }
   
      // let totalAmount = this.getTotalValue('amount').toFixed(2);
      // let billingValue = this.getTotalBillingValue().toFixed(2);
      // let overall_po_value_check = this.matrixConfig ? (this.matrixConfig['overall_po_value_check'] ? true : false) : true;

      // if (parseFloat(totalAmount) !== parseFloat(billingValue) && overall_po_value_check) {
     
      //   this.toasterService.showWarning('Billing Advice Value is not equal to Invoice Value', 10000);
      //   this.submitProgress = false;
      //   return;
      // }
      if(billing_value>milestone_value){
        message="Milestone Value is lesser than Billable Milestone Value! Do You Want To Continue? "
      }
      if(billing_value<milestone_value){
        message="Milestone Value is greater than Billable Milestone Value! Do You Want To Continue? "
      }
     
      if(this.value_error){
      Swal.fire({
        text:message,
        showCancelButton: true,
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        allowOutsideClick: false,
        allowEscapeKey: false, 
        icon: 'warning'
      }).then(async (result) => {
        if (result.isConfirmed) {
          if(this.mode=='Create'){
            let invoice_list = [];
            await this.PmBillingService.saveBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billing_value).then(async(res:any)=>{
              if(res['messType']=='S'){
                let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
                      if(this.billingAdviceNewVersion)
                      {
                        await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                            value = res;
                            if(value =='[]')
                              {
                                  const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                                  this.toasterService.showError(billingUpdate_currency_failed)
                                  return 
                              }
                        })
                      }
                this.newInvoiceDate = {
                  "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                }
                this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
               const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
               this.toasterService.showSuccess(billingUpdate_success, 10000)
               this.saveDisabled=false;

               //this.PmBillingService.insertTimeTrackerInsertion(this.data.projectID, this.data.itemID)
               
               setTimeout(() => {
                let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;
                this.router.navigateByUrl(navigationUrl);
                this.submitProgress=false;
              }, 2000);
              }
              else{
               const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
               this.toasterService.showError(billingUpdate_unsuccess);
               this.submitProgress=false;
               this.saveDisabled=false;
              }
            })
          }
          else if(this.mode='Update'){
                  let invoice_list = [];
                  await this.PmBillingService.updateBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billing_value).then(async(res:any)=>{
                    
                    if(res['messType']=='S'){
                      let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
                      if(this.billingAdviceNewVersion)
                      {
                        await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                            value = res;
                            if(value =='[]')
                              {
                                  const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                                  this.toasterService.showError(billingUpdate_currency_failed)
                                  return 
                              }
                        })
                      }
                      this.newInvoiceDate = {
                        "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                      }
                      this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                      const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                      this.toasterService.showSuccess(billingUpdate_success, 10000)
                      //this.PmBillingService.insertTimeTrackerInsertion(this.data.projectID, this.data.itemID)
                      this.saveDisabled=false;
                      setTimeout(() => {
                        let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                        let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;
                        this.router.navigateByUrl(navigationUrl);
                        this.submitProgress=false;
                      }, 2000);
                     }
                     else{
                      const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                      this.toasterService.showError(billingUpdate_unsuccess);
                      this.submitProgress=false;
                      this.saveDisabled=false;
                     }
                  })
          }
        
        }
        else if (result.dismiss === Swal.DismissReason.cancel) {
          
        }
      })
    }
    else{
      if(this.mode=='Create'){
        let invoice_list = [];
        await this.PmBillingService.saveBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billing_value).then(async (res:any)=>{
          
          if(res['messType']=='S'){
            let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
              if(this.billingAdviceNewVersion)
              {
                await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                    value = res;
                    if(value =='[]')
                    {
                        const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                        this.toasterService.showError(billingUpdate_currency_failed)
                        return 
                    }
                })
              }
            this.newInvoiceDate = {
              "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
            }
            this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
           const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
           await this.toasterService.showSuccess(billingUpdate_success, 10000)
           setTimeout(() => {
            // let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/${this._utilityService.encodeURIComponent(this.item_name)}/billing/milestone`;
            // this.router.navigateByUrl(navigationUrl);
            this.submitProgress=false;
          }, 2000);
          }
          else{
           const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
           this.toasterService.showError(billingUpdate_unsuccess);
           this.submitProgress=false;
           this.saveDisabled=false;
          }
        })
      }
      else if(this.mode='Update'){
          let invoice_list = [];
              await this.PmBillingService.updateBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billing_value).then(async (res:any)=>{
                
                if(res['messType']=='S'){
                  let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
                      if(this.billingAdviceNewVersion)
                      {
                        await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                            value = res;
                            if(value =='[]')
                              {
                                  const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                                  this.toasterService.showError(billingUpdate_currency_failed)
                                  return 
                              }
                        })
                      }
                  this.newInvoiceDate = {
                    "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                  }
                  this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                  const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                  this.toasterService.showSuccess(billingUpdate_success, 10000)
                  this.saveDisabled=false;
                  setTimeout(() => {
                    // let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/${this._utilityService.encodeURIComponent(this.item_name)}/billing/milestone`;
                    // this.router.navigateByUrl(navigationUrl);
                    this.submitProgress=false;
                  }, 2000);
                 }
                 else{
                  const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                  this.toasterService.showError(billingUpdate_unsuccess);
                  this.submitProgress=false;
                  this.saveDisabled=false;
                 }
              })
      }
    }

    }
    else{
      this.saveDisabled =false;
      this.submitProgress=false;
    }
  
  }
  checkNegative(){
    let control;
    for(let item of this.items){
      if(item.planned_hours < 0 || item.actual_billable_hours < 0 || item.per_hour_rate < 0 || item.value < 0){
        control = true;
        this.toasterService.showWarning('Entered Values should not be negative', 10000);
        break;
      }
      else{
        control = false;
      }
    }
    if(control){
      this.valid = false;
    }
    else{
      this.valid = true;
    }
  }
  getUniqueIds(data: any[]): number[] {
    const uniqueIds = new Set<number>();
    data.forEach(item => {
      if(item.rate_card_id!=null){
      uniqueIds.add(item.rate_card_id);
      }
    });
    return Array.from(uniqueIds);
  }
  async openDialog():Promise<void>{
   await this.getVersionHistory();
      this.dialog.open(VersionHistoryComponent, {
        data: {
          headerText: 'Version History',
          versionList: this.versionList,
          isLoading: false,
          currentVersionId: this.currentVersionId,
          actionData: this.actionData,
          showactionType:false
        },
         width: '35%',
         height: '100%',
         position:{right:'0px'}
      });
  }
getMaxVersionId(versionList: any[]): number | null {
  if (versionList.length === 0) return null;
  return Math.max(...versionList.map(version => version.version_name));
}
  showChild(rows){

    rows['expandEnable'] = !rows['expandEnable'];
 
    if(!rows['expandEnable'])
    {
        for(let item of this.PositionData){
          
          if(rows.id==item['rate_card_id']){
           
            item['isVisible']=false
          }
        }
    }
    else
    {
      for(let item of this.PositionData){
        
        if(rows.id==item['rate_card_id']){
          
          item['isVisible']=true
        }
      }
    }
  }
  // Toggle all line items - expand if any are collapsed, otherwise collapse all
  toggleAll() {
    if(this.hasCollapsedItems()) {
      this.expandAll();
    } else {
      this.collapseAll();
    }
  }

  // Expand all line items under each position
  expandAll() {
    for(let item of this.PositionData) {
      if(item.has_child) {
        item['expandEnable'] = true;
        // Show all child items for this position
        for(let childItem of this.PositionData) {
          if(item.id == childItem['rate_card_id']) {
            childItem['isVisible'] = true;
          }
        }
      }
    }
  }

  // Collapse all line items under each position
  collapseAll() {
    for(let item of this.PositionData) {
      if(item.has_child) {
        item['expandEnable'] = false;
        // Hide all child items for this position
        for(let childItem of this.PositionData) {
          if(item.id == childItem['rate_card_id']) {
            childItem['isVisible'] = false;
          }
        }
      }
    }
  }

  // Check if there are any collapsed items (to enable/disable expand all button)
  hasCollapsedItems(): boolean {
    return this.PositionData.some(item => item.has_child && !item.expandEnable);
  }

  // Check if there are any expanded items (to enable/disable collapse all button)
  hasExpandedItems(): boolean {
    return this.PositionData.some(item => item.has_child && item.expandEnable);
  }
  getActualBillableHours(value, index, id){
    value = value!="" ? value : 0
    

    
    if(!isNaN(value)){
    let subdata=_.where(this.PositionData,{rate_card_id:id})
    let dividedValue=value/subdata.length
    if(this.employeeBased){
     this.items[index]['actual_billable_hours']=value
          if(this.items[index]['actual_billable_hours']!=undefined && this.items[index]['per_hour_rate']!=undefined){
            this.items[index]['value']=this.items[index]['actual_billable_hours']*this.items[index]['per_hour_rate']
            this.items[index]['actual_billable_days']=this.items[index]['actual_billable_hours']/this.project_daily_working_hours
          }
    }
    else{
   
    let is_employee_level = false;
        for(let item of this.PositionData){
          if(item['id']==id){
            is_employee_level = item['is_employee_level']

            if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
              item['value']=item['actual_billable_hours']*item['per_hour_rate']
             item['actual_billable_days']=(item['actual_billable_hours']/this.project_daily_working_hours).toFixed(1)
            }
          }
          if(item['rate_card_id']==id){
            
            item['actual_billable_hours']=dividedValue
            
            if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
              item['value']=item['actual_billable_hours']*item['per_hour_rate']
              item['actual_billable_days']=(item['actual_billable_hours']/this.project_daily_working_hours).toFixed(1)
            }
          }
        } 
        
        if(!is_employee_level){
          for(let item of this.items){
            if(item['rate_card_id']==id){
              
                let roundedHoursPosition = this.getSumRoundedHourForPosition(this.items,  id)

                if(roundedHoursPosition>0)
                {
                 
                    item['actual_billable_hours'] = ((item['after_zifo_timesheet_hours'] / roundedHoursPosition )*(value))
                    if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
                      item['value']=item['actual_billable_hours']*item['per_hour_rate']
                      item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                    }
                  
                }
                else
                {

                  if(this.planned_hours_if_timesheet_not_present)
                  {
                    let plannedHoursPosition = this.getSumPlannedHourForPosition(this.items, id)

                    if(plannedHoursPosition>0)
                    {
                      item['actual_billable_hours'] = ((item['planned_hours'] / plannedHoursPosition )*(value))
                      if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
                        item['value']=item['actual_billable_hours']*item['per_hour_rate']
                        item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                      }
                    }
                    else
                    {
  
                      item['actual_billable_hours'] = 0
                      if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
                        item['value']=item['actual_billable_hours']*item['per_hour_rate']
                        item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                      }
                    }
                  }
                  else
                  {

                    item['actual_billable_hours'] = 0
                    if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
                      item['value']=item['actual_billable_hours']*item['per_hour_rate']
                      item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                    }
                  }
                  
                }
                
              
            }

          }


          for(let item of this.PositionData)
            {
              if(item['id']==id){

                item['actual_billable_hours'] = item.is_employee_level ? this.getSumActualBillableHoursForPosition(this.items, id) : this.safeFixed(item['actual_billable_hours'])

                console.log("Actual Billable Hours", item['actual_billable_hours'])
  
                 //Step 1: Sum Actual till Date
                 item['display_actual_till_date'] =  item['display_actual_till_previous_date'] + item['actual_billable_hours']
  
                 //Step 2: Update Future Effort Required = Current Billing Plan Hours - Actual Billable Hours
                 item['future_effort_required'] = this.safeFixed(item['current_planned'] - item['actual_billable_hours'])
  
                 //Step 2: Sum Quote Position hours with Actual Hours
                 item['display_total_quote_position_hours'] = item['display_actual_till_date'] + item['future_effort_required'] + item['future_planned']
   
                 //Step 3 : Calculate Per Hour Rate
                 item['revised_per_hour_rate'] = item['display_total_quote_position_hours'] ?  item['display_total_quote_position_hours']!=0 ? (item['total_quote_position_value']/parseFloat(item['display_total_quote_position_hours'])) : item['per_hour_rate'] : item['per_hour_rate']
                 
                 //Step 4: Future Revenue
                 item['future_revenue'] = ((item['future_effort_required']  ? item['future_effort_required']  : 0) + item['future_planned'])  * item['revised_per_hour_rate']
   
                 //Step 5: Calculate Revenue Till Date
                 item['display_revenue_till_date'] = item['display_actual_till_date'] * item['revised_per_hour_rate']
   
                 //Step 6: Calcule Total Revneue
                 item['display_total_revenue'] = item['display_revenue_till_date'] + item['future_revenue']
                
                 
                 item['current_month_revenue'] = item['display_revenue_till_date'] - item['revenue_till_previous_month']
  
                 
  
                 item['current_month_per_hour_rate'] =item['actual_billable_hours']!=0? item['current_month_revenue'] / item['actual_billable_hours']:item['revised_per_hour_rate']
  
                 item['current_month_progress'] = (item['current_month_revenue']/item['total_quote_position_value']) * 100
  
                 console.log("Current Month Revenue", item['current_month_revenue'], "Quote Position Value", item['total_quote_position_value'], "Revenue Till Date", item['display_revenue_till_date'])
  
                 item['cummulative_month_progress'] = (item['display_revenue_till_date']/item['total_quote_position_value']) * 100
  
                if(item['actual_billable_hours']!=undefined && item['revised_per_hour_rate']!=undefined)
                {
                  item['value']=item['actual_billable_hours']*item['revised_per_hour_rate']
  
                  item['per_hour_rate'] = item['revised_per_hour_rate']
  
                  item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                }
              }

              console.log("Position Data", item)
              
            }
        }
        else{
         

          for(let item of this.items)
          {
            if(item['id'] == id)
            {
              item['actual_billable_hours'] = value;

              if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined)
              {
                item['value']=item['actual_billable_hours']*item['per_hour_rate']

                item['actual_billable_days'] =item['actual_billable_hours']/this.project_daily_working_hours
              }
            }
          }
          let rate_card_id = this.PositionData[index]['rate_card_id']


          for(let item of this.PositionData)
          {
            if(item['id']==rate_card_id){

              console.log("Position Data", item)
              
              item['actual_billable_hours'] = this.getSumActualBillableHoursForPosition(this.items, rate_card_id)

               //Step 1: Sum Actual till Date
               item['display_actual_till_date'] =  item['display_actual_till_previous_date'] + item['actual_billable_hours']

               //Step 2: Update Future Effort Required = Current Billing Plan Hours - Actual Billable Hours
               item['future_effort_required'] = this.safeFixed(item['current_planned'] - item['actual_billable_hours'])

               //Step 2: Sum Quote Position hours with Actual Hours
               item['display_total_quote_position_hours'] = item['display_actual_till_date'] + item['future_effort_required'] + item['future_planned']
 
               //Step 3 : Calculate Per Hour Rate
               item['revised_per_hour_rate'] = item['display_total_quote_position_hours'] ?  item['display_total_quote_position_hours']!=0 ? (item['total_quote_position_value']/parseFloat(item['display_total_quote_position_hours'])) : item['per_hour_rate'] : item['per_hour_rate']
               
               //Step 4: Future Revenue
               item['future_revenue'] = ((item['future_effort_required']  ? item['future_effort_required']  : 0) + item['future_planned'])  * item['revised_per_hour_rate']
 
               //Step 5: Calculate Revenue Till Date
               item['display_revenue_till_date'] = item['display_actual_till_date'] * item['revised_per_hour_rate']
 
               //Step 6: Calcule Total Revneue
               item['display_total_revenue'] = item['display_revenue_till_date'] + item['future_revenue']
              
               
               item['current_month_revenue'] = item['display_revenue_till_date'] - item['revenue_till_previous_month']

               

               item['current_month_per_hour_rate'] =item['actual_billable_hours']!=0? item['current_month_revenue'] / item['actual_billable_hours']:item['revised_per_hour_rate']

               item['current_month_progress'] = (item['current_month_revenue']/item['total_quote_position_value']) * 100

               console.log("Current Month Revenue", item['current_month_revenue'], "Quote Position Value", item['total_quote_position_value'], "Revenue Till Date", item['display_revenue_till_date'])

               item['cummulative_month_progress'] = (item['display_revenue_till_date']/item['total_quote_position_value']) * 100

              if(item['actual_billable_hours']!=undefined && item['revised_per_hour_rate']!=undefined)
              {
                item['value']=item['actual_billable_hours']*item['revised_per_hour_rate']

                item['per_hour_rate'] = item['revised_per_hour_rate']

                item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
              }
            }
            
          }

        }
        
      }
    }
  }

  getPerHourRate(value,id){

    if(!isNaN(value)){
    if(this.employeeBased){

          this.items[id]['per_hour_rate']=value
          if(this.items[id]['actual_billable_hours']!=undefined && this.items[id]['per_hour_rate']!=undefined){
            this.items[id]['value']=this.items[id]['actual_billable_hours']*this.items[id]['per_hour_rate']
          }
          
    }
    else{
   let subdata=_.where(this.PositionData,{rate_card_id:id})
    // let dividedValue=value/subdata.length
 
    let i=0
        for(let item of this.PositionData){
          if(item['id']==id){
            if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
              item['value']=item['actual_billable_hours']*item['per_hour_rate']
            }
          }
          if(item['rate_card_id']==id){
            item['per_hour_rate']=value
            if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
              this.PositionData[i]['value']=parseFloat(item['actual_billable_hours'])*parseFloat(item['per_hour_rate'])
            }
          }
          
          i=i+1
        }
        for(let item of this.items){
          if(item['rate_card_id']==id){
            item['per_hour_rate']=value
            if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
              item['value']=item['actual_billable_hours']*item['per_hour_rate']
            }
          }
        }
      }
    }
  }
  getValue(value,id){

  //  let subdata=_.where(this.PositionData,{rate_card_id:id})
  //   let dividedValue=value/subdata.length
  //       for(let item of this.PositionData){
  //         if(item['rate_card_id']==id){
  //           item['value']=dividedValue
  //         }
  //       }
  //       for(let item of this.items){
  //         if(item['rate_card_id']==id){
  //           item['value']=dividedValue
  //         }
  //       }
  }
  checkEmployeeBased(){
    if(this.employeeBased==false){
      this.employeeBased=true
      this.positionBased=false
    }
  }
  checkPositionBased(){
    if(this.positionBased==false){
      this.employeeBased=false
      this.positionBased=true
    }
  }
  async confirmData(){
    this.submitProgress = true;
    if(this.bill_enable){
      await this.updateItemsForSavingBills();
    }

    if(this.previousMilestoneCondition)
    {
      const _previousMilestoneCondition = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.previousMilestoneCondition ? this.retrieveMessages[0].errors.previousMilestoneCondition : 'Complete the previous milestone to proceed with accrual or invoicing of this milestone' : 'Complete the previous milestone to proceed with accrual or invoicing of this milestone';
      this.toasterService.showWarning(_previousMilestoneCondition, 10000);
      return;
    }

    if(this.at_risk && this.item_status_id != 4)
    {
      const content1 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.at_risk_error ? this.retrieveMessages[0].errors.at_risk_error : 'Project in AT Risk Flag' : 'Project in AT Risk Flag';
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      

      this.toasterService.showWarning(content1 +' '+ content2,10000);
      this.submitProgress = false;
    }
    else if((this.item_status_id == 7 || this.item_status_id == 12 || this.item_status_id == 6))
    {
      let checkName = _.findWhere(this.PmMasterService.status_list,{id: this.item_status_id})
      const content1 = checkName ? this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.status_error + checkName['name'] +" Status! "? this.retrieveMessages[0].errors.status_error + checkName['name']+" Status! " : 'Project is in '+checkName['name'] +" Status! ": 'Project is in '+ checkName['name']+" Status! " :"";
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      
      this.toasterService.showWarning(content1 +' '+ content2,10000);
      this.submitProgress = false;
    }
    else if (
      this.opportunity_status_config &&
      this.opportunity_status_config.length > 0 &&
      !this.opportunity_status_config[0].enabled_status.includes(this.opportunity_status_id)
    ) { 
      let statusName = this.opportunity_status_name;
      let contentMessage = `Cannot be moved to YTB, Opportunity is in Stage: ${statusName}`;
      this.toasterService.showWarning(contentMessage, 10000);
    }
    else
    {
      
      if(this.billing_advice_action == "'creditNote'" || this.data.milestone_type == 6)
      {
        let commentMandatory = _.where(this.formConfig,{type:"new-billing-advice", field_name:"credit_note_attachment_mandatory", is_active: true})

        if(commentMandatory.length>0)
        {
            await this.PmBillingService.checkAttachmentPresentForMilestones([this.milestoneID]).then((res: any)=>{
                if(res && res.length==0)
                {
                    const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.attachment_mandatory_error ? this.retrieveMessages[0].errors.attachment_mandatory_error : 'Please add attachment before raising Credit Note' : 'Please add attachment before raising Credit Note';
                    this.toasterService.showWarning(invoice_date_err_msg, 10000);
                    this.submitProgress=false;
            
                    return;
                }
            })

            if(!this.submitProgress)
                return

            
            await this.PmBillingService.checkCommentPresentForMilestones([this.milestoneID]).then((res: any)=>{
              if(res && res.length==0)
              {
                  const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.comment_mandatory_error ? this.retrieveMessages[0].errors.comment_mandatory_error : 'Enter comments before raising Credit Note' : 'Enter comments before raising Credit Note';
                  this.toasterService.showWarning(invoice_date_err_msg, 10000);
                  this.submitProgress=false;
                  return;
              }
          })

          if(!this.submitProgress)
            return
        }
      }

      let commentLookFormConfig = _.findWhere(this.formConfig, {type:"new-billing-advice", field_name:"difference_comment_mandatory", is_active: true})
      
      if(commentLookFormConfig)
      {

          let commentMandatory = await this.checkForCommentsMandatory()

          if(!commentMandatory)
          {
            this.submitProgress = false;

            const comments_mandatory = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.comments_mandatory ? this.retrieveMessages[0].errors.comments_mandatory : 'Kindly provide comments for any difference between Billable and Actual Hours.' : 'Kindly provide comments for any difference between Billable and Actual Hours.';
            this.toasterService.showWarning(comments_mandatory, 10000);
            return;
          }
      }
      
      this.confirmDisabled = true;
      //this.checkNegative();
      if(this.valid){
        let billing_value =0
        let milestone_value : number = 0
        let message
        for(let item of this.dialogData.value){
          if(this.code===item['currency_code']){
               milestone_value=item['value']
          }
        }
        for(let item of this.PositionData)
          {
            if(item['has_child'])
            {
              console.log("Current Month Position Data", item)
              billing_value+=parseFloat(item['current_month_revenue'].toString())
            }
          }
        if(this.data.invoice_date=="" || this.data.invoice_date==" " || this.data.invoice_date==null || this.data.invoice_date=="null" || this.data.invoice_date=="0000-00-00" || this.data.invoice_date=="Invalid Date")
        {
          if(this.invoiceStepper)
          {
            const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_date_err_msg ? this.retrieveMessages[0].errors.invoice_date_err_msg : 'Kindly Enter Valid Invoice Date' : 'Kindly Enter Valid Invoice Date';
            this.toasterService.showWarning(invoice_date_err_msg, 10000);
            this.submitProgress = false;
            return;
          }
          else
          {
            this.data.invoice_date = this.milestoneEndDate;
          }

          
          
        }

        let invoiceDateBetween = _.where(this.formConfig,{type:"billing_advise", field_name:"check_invoice_date_between", is_active: true})

        if(invoiceDateBetween.length > 0)
        {
          const isWithinRange = moment(this.data.invoice_date).isBetween(this.minDate, this.maxDate, null, '[]');

          if (!isWithinRange) 
          {
            const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_date_err_msg ? this.retrieveMessages[0].errors.invoice_date_err_msg : 'Kindly Enter Valid Invoice Date' : 'Kindly Enter Valid Invoice Date';
            this.toasterService.showWarning(invoice_date_err_msg, 10000);
            this.submitProgress = false;
            return;
          }
        }
        if(!this.checkInvoiceDetails()){
          const invoice_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_err_msg ? this.retrieveMessages[0].errors.invoice_err_msg : 'Kindly Enter Valid Invoice Description' : 'Kindly Enter Valid Invoice Description';
          this.toasterService.showWarning(invoice_err_msg, 10000);
          this.submitProgress = false;
          return;
        }

        if(this.po_number == null || this.po_number == undefined || this.po_number.trim() === '' || this.po_number == 'null'){
          this.toasterService.showWarning('PO number not available; hence, invoice cannot be raised.', 10000);
          this.submitProgress = false;
          return;
        }
        let totalAmount = this.getTotalValue('amount').toFixed(2);
        let billingValue = this.getTotalBillingValue().toFixed(2);
        let overall_po_value_check = this.matrixConfig ? (this.matrixConfig['overall_po_value_check'] ? true : false) : true;
        let billing_milestone_value_check  = this.matrixConfig ? (this.matrixConfig['billing_milestone_value_check'] ? true : false) : false;
        let tolerance_value_check = this.matrixConfig ? (this.matrixConfig['is_tolerance_present'] ? true: false) : false;

        if(parseFloat(billingValue) <= 0 && this.matrixConfig &&  this.matrixConfig?.billing_advice_stepper){
          this.toasterService.showWarning('Billing Advice Value should be greater than 0', 10000);
          this.submitProgress = false;
          return;
        }

        if(billing_milestone_value_check){
          let checkBillingValue = parseFloat(billingValue)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkBillingValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice and Milestone exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkMilestoneValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Milestone Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }
        
        if(overall_po_value_check && !this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkBillingValue = parseFloat(billingValue);

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkBillingValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }

        if(overall_po_value_check && this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Milestone Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkMilestoneValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Milestone Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }
        
        
        
        if(await this.checkYTBData())
        {
          let check = !this.matrixConfig?.milestone_value_invoice_details ? "Billing Advice Value must not exceed Order Value" : "Milestone Value must not exceed Order Value"
          this.toasterService.showWarning(check, 10000);
          this.submitProgress = false;
          return;
        }
        if(billing_value>milestone_value){
          message="Milestone Value is lesser than Billable Milestone Value! Do You Want To Continue? "
        }
        if(billing_value<milestone_value){
          message="Milestone Value is greater than Billable Milestone Value! Do You Want To Continue? "
        }

        if(this.value_error)
        {
          Swal.fire({
            text:message,
            showCancelButton: true,
            confirmButtonText: 'OK',
            cancelButtonText: 'Cancel',
            allowOutsideClick: false,
            allowEscapeKey: false, 
            icon: 'warning'
          }).then(async (result) => {
            if (result.isConfirmed) {
              let approvalGroupId = this.milestone_status_id !== 15 ? 1 : 2;
              let approvalsDetails = await this.checkForApprovals(approvalGroupId ,this.items, 8)
              let apiCall = approvalsDetails?.isForApproval ? false : true;
              if(this.mode=='Create'){
                await this.PmBillingService.saveBillingAdvise(this.data.Mid,this.billingStepper ? this.items :[], this.PositionData, this.data.projectID,this.data.itemID,this.invoiceList,this.po_number,this.data.invoice_date, billing_value, apiCall).then(async (res:any)=>{
                  if(res['messType']=='S'){
                  let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
                  if(this.billingAdviceNewVersion)
                  {
                    await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                        value = res;
                        if(value =='[]')
                        {
                            const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                            this.toasterService.showError(billingUpdate_currency_failed)
                            return 
                        }
                    })
                  }
                  this.newStatus = {
                    Status: this.getNameByIdPipe.transform(8, this.milestone_status_list)
                  }
                  this.newInvoiceDate = {
                    "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                  }
                  this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                  if(approvalsDetails?.isForApproval){
                    if(approvalsDetails?.navigate){
                      approvalGroupId = approvalsDetails?.triggerApprovalGroupId ?  approvalsDetails?.triggerApprovalGroupId : approvalGroupId;
                      let approvals = await this.triggerApprovals(approvalGroupId, this.items, 8);
                      if(approvals?.navigate){
                        let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                        let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;
                        this.router.navigateByUrl(navigationUrl);
                        this.submitProgress = false;
                        return;
                      }else{
                        this.confirmDisabled=false;
                        this.submitProgress = false;
                        return;
                      }
                    }else{
                      this.confirmDisabled=false;
                      this.submitProgress = false;
                      return;
                    }
                  }
                  await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 8, this.milestoneID, value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                  const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                  this.toasterService.showSuccess(billingUpdate_success, 10000)
                  let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                  let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                  this.router.navigateByUrl(navigationUrl);
                  this.submitProgress = false;
                  }
                  else{
                  const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                  this.toasterService.showError(billingUpdate_unsuccess);
                  this.submitProgress = false;
                  this.confirmDisabled=false;
                  }
                })
              }
              else if(this.mode='Update'){
                      await this.PmBillingService.updateBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,this.invoiceList,this.po_number,this.data.invoice_date, billing_value, apiCall).then(async (res:any)=>{
                        if(res['messType']=='S'){
                          let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
                          if(this.billingAdviceNewVersion)
                          {
                            await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                                value = res;
                                if(value =='[]')
                                  {
                                      const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                                      this.toasterService.showError(billingUpdate_currency_failed)
                                      return 
                                  }
                            })
                          }
                          this.newStatus = {
                            Status: this.getNameByIdPipe.transform(8, this.milestone_status_list)
                          }
                          this.newInvoiceDate = {
                            "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                          }
                          this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                          if(approvalsDetails?.isForApproval){
                            if(approvalsDetails?.navigate){
                              approvalGroupId = approvalsDetails?.triggerApprovalGroupId ?  approvalsDetails?.triggerApprovalGroupId : approvalGroupId;
                              let approvals = await this.triggerApprovals(approvalGroupId, this.items, 8);
                              if(approvals?.navigate){
                                let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                                let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                                this.router.navigateByUrl(navigationUrl);
                                this.submitProgress = false;
                                return;
                              }else{
                                this.confirmDisabled=false;
                                this.submitProgress = false;
                                return;
                              }
                            }else{
                              this.confirmDisabled=false;
                              this.submitProgress = false;
                              return;
                            }
                          }
                          await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 8, this.milestoneID, value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'),this.oldStatus, this.newStatus, 44)
                          const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                          this.toasterService.showSuccess(billingUpdate_success, 10000);
                          let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                          let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                          this.router.navigateByUrl(navigationUrl);
                          this.submitProgress = false;
                        }
                        else{
                          const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                          this.toasterService.showError(billingUpdate_unsuccess);
                          this.submitProgress = false;
                          this.confirmDisabled=false;
                        }
                      })
              }
            
            }
            else if (result.dismiss === Swal.DismissReason.cancel) {
              
            }
          })
        }
        else
        {
          let approvalGroupId = this.milestone_status_id !== 15 ? 1 : 2;
          let approvalsDetails = await this.checkForApprovals(approvalGroupId ,this.items, 8)
          let apiCall = approvalsDetails?.isForApproval ? false : true;
          if(this.mode=='Create'){
            await this.PmBillingService.saveBillingAdvise(this.data.Mid,this.billingStepper ?this.items:[], this.PositionData, this.data.projectID,this.data.itemID,this.invoiceList,this.po_number,this.data.invoice_date, billing_value,apiCall).then(async (res:any)=>{
              if(res['messType']=='S'){
              let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
              if(this.billingAdviceNewVersion)
              {
                await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                    value = res;
                    if(value =='[]')
                      {
                          const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                          this.toasterService.showError(billingUpdate_currency_failed)
                          return 
                      }
                })
              }
              this.newStatus = {
                Status: this.getNameByIdPipe.transform(8, this.milestone_status_list)
              }
              this.newInvoiceDate = {
                "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
              }
              this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
              if(approvalsDetails?.isForApproval){
                if(approvalsDetails?.navigate){
                  approvalGroupId = approvalsDetails?.triggerApprovalGroupId ?  approvalsDetails?.triggerApprovalGroupId : approvalGroupId;
                  let approvals = await this.triggerApprovals(approvalGroupId, this.items, 8);
                  if(approvals?.navigate){
                    let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                    let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                    this.router.navigateByUrl(navigationUrl);
                    this.submitProgress = false;
                    return;
                  }else{
                    this.confirmDisabled=false;
                    this.submitProgress = false;
                    return;
                  }
                }else{
                  this.confirmDisabled=false;
                  this.submitProgress = false;
                  return;
                }
              }
              await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 8, this.milestoneID, value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'),this.oldStatus, this.newStatus, 44)
              const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
              this.toasterService.showSuccess(billingUpdate_success, 10000)
              let navigate_to = this.bill_enable ? 'bills' : 'milestone'
              let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
              this.router.navigateByUrl(navigationUrl);
              this.submitProgress = false;
              }
              else{
              const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
              this.toasterService.showError(billingUpdate_unsuccess);
              this.submitProgress = false;
              this.confirmDisabled=false;
              }
            })
          }
          else if(this.mode='Update'){
                  await this.PmBillingService.updateBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,this.invoiceList,this.po_number,this.data.invoice_date, billing_value).then(async (res:any)=>{
                    if(res['messType']=='S'){
                      let value = this.billingAdviceNewVersion ? this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value : this.data.value;
                      if(this.billingAdviceNewVersion)
                      {
                        await this.PmBillingService.getUpdatedBillingValue(this.projectID, this.ItemID, this.milestoneID, value, this.code, moment().format("YYYY-MM-DD")).then((res)=>{
                            value = res;
                            if(value =='[]')
                              {
                                  const billingUpdate_currency_failed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_currency_failed ? this.retrieveMessages[0].errors.billingUpdate_currency_failed : 'Error while submitting Billing Advice' : 'Error while submitting Billing Advice';
                                  this.toasterService.showError(billingUpdate_currency_failed)
                                  return 
                              }
                        })
                      }
                      this.newStatus = {
                        Status: this.getNameByIdPipe.transform(8, this.milestone_status_list)
                      }
                      this.newInvoiceDate = {
                        "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                      }
                      this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                      if(approvalsDetails?.isForApproval){
                        if(approvalsDetails?.navigate){
                          approvalGroupId = approvalsDetails?.triggerApprovalGroupId ?  approvalsDetails?.triggerApprovalGroupId : approvalGroupId;
                          let approvals = await this.triggerApprovals(approvalGroupId, this.items, 8);
                          if(approvals?.navigate){
                            let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                            let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                            this.router.navigateByUrl(navigationUrl);
                            this.submitProgress = false;
                            return;
                          }else{
                            this.confirmDisabled=false;
                            this.submitProgress = false;
                            return;
                          }
                        }else{
                          this.confirmDisabled=false;
                          this.submitProgress = false;
                          return;
                        }
                      }
                      await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 8, this.milestoneID, value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                      const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                      this.toasterService.showSuccess(billingUpdate_success, 10000);
                      let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                      let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                      this.router.navigateByUrl(navigationUrl);
                      this.submitProgress = false;
                    }
                    else{
                      const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                      this.toasterService.showError(billingUpdate_unsuccess);
                      this.submitProgress = false;
                      this.confirmDisabled=false;
                    }
                  })
          }
        }

      }
      else{
        this.confirmDisabled =false;
        this.submitProgress = false;
      }
    }
  
  }
  enableFullScreen(){
    
    if(!this.fullscreen)
    {  this.content_height='420px'
      try{
        if (this.document.exitFullscreen) {
          this.document.exitFullscreen();
        } else if (this.document.mozCancelFullScreen) {
          /* Firefox */
          this.document.mozCancelFullScreen();
        } else if (this.document.webkitExitFullscreen) {
          /* Chrome, Safari and Opera */
          this.document.webkitExitFullscreen();
        } else if (this.document.msExitFullscreen) {
          /* IE/Edge */
          this.document.msExitFullscreen();
        }
      }
      catch(err){
          console.log(err)
      }
    }
    else
    {
      const elem = this.divRef.nativeElement;

      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) { /* Firefox */
        elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) { /* Chrome, Safari and Opera */
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) { /* IE/Edge */
        elem.msRequestFullscreen();
      }
      this.content_height='100%'
    }

    this.fullscreen = !this.fullscreen

    // this.toolbarActionCalled(this.fullscreen ? 'closeFullScreen' : 'enableFullScreen')

    
    
  }
  
  /**
   * @description Returning Back to the Billing Milestone landing Page
   */
  returnback(){
    let navigate_to = this.bill_enable ? 'bills' : 'milestone'
    let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;
    this.router.navigateByUrl(navigationUrl);
  }
  
  calculateDynamicContentHeight() {
    const height = window.innerHeight - 57;
    this.dynamicHeight = height + 'px';
    document.documentElement.style.setProperty(
      '--detailsHeight',
      this.dynamicHeight
    );
    const tableHeight = window.innerHeight - 300 + 'px';
    document.documentElement.style.setProperty(
      '--tableHeight',
      tableHeight
    );
  }
  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }
  async addAttachment(milestone_id, gantt_id) {
    let context_id = "PRJ_" + milestone_id + gantt_id
    this.formConfig.source = 62;
    this.formConfig.keyId = milestone_id;
    this.formConfig.ItemId = this.ItemID;
  
    await this.attachmentService.openAttachment(context_id, this.formConfig)

    await this.PmBillingService.checkAttachmentPresentForMilestones([this.milestoneID]).then((res: any)=>{
      if(res && res.length>0)
      {
          this.is_attachment_present =true;
      }
      else
      {
          this.is_attachment_present =false;
      }
    })
  }
  addNotes() {
    this.commentsInput.application_id = 915;
    this.commentsInput.application_name = "Project Management";
    this.commentsInput.title = this.data.label;
    this.commentsInput.unique_id_1 = this.data.Mid;
    let modalParams = {
      inputData: this.commentsInput,
      context: {
        'Milestone Name': this.data.label,

        'Milestone Start Date': moment(this.data.start_date).format('DD-MMM-YY'),

        'Milestone End Date': moment(this.data.end_date).format("DD-MMM-YY"),

        'Milestone Status': this.data.status_name
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%'
    };
    const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
      height: '100%',
      width: '75%',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });

    openChatCommentContextModalComponent.afterClosed().subscribe(async (res) => {

      await this.PmBillingService.checkCommentPresentForMilestones(this.data.Mid).then((res: any)=>{
        
          if(res && res.length>0)
          {
              this.is_comment_present = true;
          }
          else
          {
              this.is_comment_present = false;
          }
        
      })
    })
  }

  getNameByFormConfig(type, field_name, label){
    let getResult = _.findWhere(this.formConfig,{field_name: field_name, type: type})
    return getResult ? getResult['label'] ? getResult['label'] : label : label
  }
  async accuralData(){
    this.submitProgress=true;
    if(this.bill_enable){
      await this.updateItemsForSavingBills();
    }

    let percentage = this.getCummulativeMonthProgress()
    for (let item of this.PositionData) {
      if (item['has_child'] && item['actual_billable_hours']==0 && item['current_month_revenue']!=0) {
        this.submitProgress=false;
        const message =
          '' + this.getNameByFormConfig("new-billing-advice", "check_messages", "Messages") + ''
        this.toasterService.showWarning(message, 10000);
        return;
      }
    }


    if(percentage>100)
    {
      const milestone_percentage = 
      'Your '+this.getNameByFormConfig("new-billing-advice", "cummulative_month_progress", "Project Progress")+'  goes beyond 100%. Adjust your '+this.getNameByFormConfig('new-billing-advice', 'future_effort_required', "Future Effort Required") 
      
      this.toasterService.showWarning(milestone_percentage, 10000);
      return;
    }


    let checkCurrentMonthProgress = _.findWhere(this.formConfig,{type:"new-billing-advice", field_name:"current_month_progress_zero_check", is_active: true})

    if(checkCurrentMonthProgress)
    {
    
      let zeroPercentage = this.getCurrentMonthProgress();

      if(zeroPercentage == 0)
      {
        const milestone_percentage = 'Your '+this.getNameByFormConfig("new-billing-advice", "current_month_progress", "Current Month Progress")+'  is equal to Zero. Adjust your '+this.getNameByFormConfig('new-billing-advice', 'future_effort_required', "Future Effort Required") 
        
        this.toasterService.showWarning(milestone_percentage, 10000);
        return;
      }
    }

    if(this.previousMilestoneCondition)
    {
      const _previousMilestoneCondition = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.previousMilestoneCondition ? this.retrieveMessages[0].errors.previousMilestoneCondition : 'Complete the previous milestone to proceed with accrual or invoicing of this milestone' : 'Complete the previous milestone to proceed with accrual or invoicing of this milestone';
      this.toasterService.showWarning(_previousMilestoneCondition, 10000);
      return;
    }
    if((this.item_status_id == 12 || this.item_status_id == 6))
    {
      let checkName = _.findWhere(this.PmMasterService.status_list,{id: this.item_status_id})
      const content1 = checkName ? this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.status_error + checkName['name'] +" Status! "? this.retrieveMessages[0].errors.status_error + checkName['name']+" Status! " : 'Project is in '+checkName['name'] +" Status! ": 'Project is in '+ checkName['name']+" Status! " :"";
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      this.submitProgress=false;
      this.toasterService.showWarning(content1 +' '+ content2,10000);
    }
    else
    {
     
      let commentLookFormConfig = _.findWhere(this.formConfig, {type:"new-billing-advice", field_name:"difference_comment_mandatory", is_active: true})
      
      if(commentLookFormConfig)
      {

          let commentMandatory = await this.checkForCommentsMandatory()

          if(!commentMandatory)
          {
            this.submitProgress = false;

            const comments_mandatory = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.comments_mandatory ? this.retrieveMessages[0].errors.comments_mandatory : 'Kindly provide comments for any difference between Billable and Actual Hours.' : 'Kindly provide comments for any difference between Billable and Actual Hours.';
            this.toasterService.showWarning(comments_mandatory, 10000);
            return;
          }
      }
      
      this.confirmDisabled = true;
      //this.checkNegative();
      if(this.valid){
        let billing_value =0
        let milestone_value=0
        let message
        for(let item of this.dialogData.value){
          if(this.code===item['currency_code']){
               milestone_value=item['value']
          }
        }
        for(let item of this.PositionData)
        {
          if(item['has_child'])
          {
            console.log("Current Month Position Data", item)
            billing_value+=parseFloat(item['current_month_revenue'].toString())
          }
        }
        if(this.data.invoice_date=="" || this.data.invoice_date==" " || this.data.invoice_date==null || this.data.invoice_date=="null" || this.data.invoice_date=="0000-00-00" || this.data.invoice_date=="Invalid Date")
        {
          if(this.invoiceStepper)
          {
            const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_date_err_msg ? this.retrieveMessages[0].errors.invoice_date_err_msg : 'Kindly Enter Valid Invoice Date' : 'Kindly Enter Valid Invoice Date';
            this.toasterService.showWarning(invoice_date_err_msg, 10000);
            this.submitProgress = false;
            return;
          }
          else
          {
            this.data.invoice_date = this.milestoneEndDate;
          }

          
        }
        let invoiceDateBetween = _.where(this.formConfig,{type:"billing_advise", field_name:"check_invoice_date_between", is_active: true})

        if(invoiceDateBetween.length > 0)
        {
          const isWithinRange = moment(this.data.invoice_date).isBetween(this.minDate, this.maxDate, null, '[]');

          if (!isWithinRange) 
          {
            const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_date_err_msg ? this.retrieveMessages[0].errors.invoice_date_err_msg : 'Kindly Enter Valid Invoice Date' : 'Kindly Enter Valid Invoice Date';
            this.toasterService.showWarning(invoice_date_err_msg, 10000);
            this.submitProgress = false;
            return;
          }
        }
        // if(!this.checkInvoiceDetails()){
        //   const invoice_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_err_msg ? this.retrieveMessages[0].errors.invoice_err_msg : 'Kindly Enter Valid Invoice Description' : 'Kindly Enter Valid Invoice Description';
        //   this.toasterService.showWarning(invoice_err_msg, 10000);
        //   this.submitProgress = false;
        //   return;
        // }
        let totalAmount = this.getTotalValue('amount').toFixed(2);
        let billingValue = this.getTotalBillingValue().toFixed(2);

        let overall_po_value_check = this.matrixConfig ? (this.matrixConfig['overall_po_value_check'] ? true : false) : true;
        let billing_milestone_value_check  = this.matrixConfig ? (this.matrixConfig['billing_milestone_value_check'] ? true : false) : false;
        let tolerance_value_check = this.matrixConfig ? (this.matrixConfig['is_tolerance_present'] ? true: false) : false;

        // if(parseFloat(billingValue) <= 0 && this.matrixConfig &&  this.matrixConfig?.billing_advice_stepper){
        //   this.toasterService.showWarning('Billing Advice Value should be greater than 0', 10000);
        //   this.submitProgress = false;
        //   return;
        // }

        if(billing_milestone_value_check){
          let checkBillingValue = parseFloat(billingValue)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkBillingValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice Value and Milestone Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkMilestoneValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Milestone Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }
        
        if(overall_po_value_check && !this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkBillingValue = parseFloat(billingValue);

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkBillingValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }

        if(overall_po_value_check && this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Milestone Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkMilestoneValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Milestone Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }

        let balance = await this.resourceLoadingComponent.checkPositionBalance()

        if(balance)
        {
          // console.log(await this.checkAccruedData(),"Accrued Data")
          if(await this.checkAccruedData())
          {
            let check = !this.matrixConfig?.milestone_value_invoice_details ? "Billing Advice Value must not exceed Order Value" : "Milestone Value must not exceed Order Value"
            this.toasterService.showWarning(check, 10000);
            this.submitProgress = false;
            return;
          }
          if(billing_value>milestone_value){
            message="Milestone Value is lesser than Billable Milestone Value! Do You Want To Continue? "
          }
          if(billing_value<milestone_value){
            message="Milestone Value is greater than Billable Milestone Value! Do You Want To Continue? "
          }
          if(this.value_error)
          {
                    
            Swal.fire({
              text:message,
              showCancelButton: true,
              confirmButtonText: 'OK',
              cancelButtonText: 'Cancel',
              allowOutsideClick: false,
              allowEscapeKey: false, 
              icon: 'warning'
            }).then(async (result) => {
              if (result.isConfirmed) {
        
                if(this.mode=='Create'){
                  let invoice_list = [];
                  await this.PmBillingService.saveBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billingValue , true).then(async (res:any)=>{
                    if(res['messType']=='S'){
                    this.newStatus = {
                      Status: this.getNameByIdPipe.transform(15, this.milestone_status_list)
                    }
                    this.newInvoiceDate = {
                      "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                    }
                    this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                    // if(approvalsDetails?.isForApproval){
                    //   if(approvalsDetails?.navigate){
                    //     approvalGroupId = approvalsDetails?.triggerApprovalGroupId ?  approvalsDetails?.triggerApprovalGroupId : approvalGroupId;
                    //     let approvals = await this.triggerApprovals(approvalGroupId, this.items, 15);
                    //     if(approvals?.navigate){
                    //       let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                    //       let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                    //       this.router.navigateByUrl(navigationUrl);
                    //       this.submitProgress = false;
                    //       return;
                    //     }else{
                    //       this.confirmDisabled=false;
                    //       this.submitProgress = false;
                    //       return;
                    //     }
                    //   }else{
                    //     this.confirmDisabled=false;
                    //     this.submitProgress = false;
                    //     return;
                    //   }
                    // }
                    await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 15, this.milestoneID, billingValue, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                    await this.resourceLoadingComponent.releaseForCosting().then((res)=>{
                      console.log("Resource Costing",)
                    })
                    
                    const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                    this.toasterService.showSuccess(billingUpdate_success, 10000)
                    let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                    let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                    this.router.navigateByUrl(navigationUrl);
                    this.submitProgress=false;
                    }
                    else{
                    const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                    this.toasterService.showError(billingUpdate_unsuccess);
                    this.submitProgress=false;
                    this.confirmDisabled=false;
                    }
                  })
                }
                else if(this.mode='Update'){
                        let invoice_list = [];
                        await this.PmBillingService.updateBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billingValue, true).then(async (res:any)=>{
                          if(res['messType']=='S'){
                            this.newStatus = {
                              Status: this.getNameByIdPipe.transform(15, this.milestone_status_list)
                            }
                            
                            this.newInvoiceDate = {
                              "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                            }
                            this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                            await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 15, this.milestoneID, billingValue, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                            await this.resourceLoadingComponent.releaseForCosting().then((res)=>{
                              console.log("Resource Costing",)
                            })
                            
                            const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                            this.toasterService.showSuccess(billingUpdate_success, 10000)
                            let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                            let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                            this.router.navigateByUrl(navigationUrl);
                            this.submitProgress=false;
                          }
                          else{
                            const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                            this.toasterService.showError(billingUpdate_unsuccess);
                            this.submitProgress=false;
                            this.confirmDisabled=false;
                          }
                        })
                }
              
              }
              else if (result.dismiss === Swal.DismissReason.cancel) {
                
              }
            })
          }
          else
          {
         
            if(this.mode=='Create'){
              let invoice_list = [];
              await this.PmBillingService.saveBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billing_value, true).then(async (res:any)=>{
                if(res['messType']=='S'){
                this.newStatus = {
                  Status: this.getNameByIdPipe.transform(15, this.milestone_status_list)
                }
                this.newInvoiceDate = {
                  "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                }
                this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 15, this.milestoneID, billingValue, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                await this.resourceLoadingComponent.releaseForCosting().then((res)=>{
                  console.log("Resource Costing",)
                })
                const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                this.toasterService.showSuccess(billingUpdate_success, 10000)
                
                let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                this.router.navigateByUrl(navigationUrl);
                this.submitProgress=false;
                }
                else{
                const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                this.toasterService.showError(billingUpdate_unsuccess);
                this.submitProgress=false;
                this.confirmDisabled=false;
                }
              })
            }
            else if(this.mode='Update'){
              let invoice_list = [];
                    await this.PmBillingService.updateBillingAdvise(this.data.Mid,this.billingStepper ? this.items : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, billing_value, true).then(async (res:any)=>{
                      if(res['messType']=='S'){
                        this.newStatus = {
                          Status: this.getNameByIdPipe.transform(15, this.milestone_status_list)
                        }
                        this.newInvoiceDate = {
                          "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
                        }
                        this.PmBillingService.updateInvoiceDate(this.ItemID, this.milestoneID, this.data.invoice_date, this.oldInvoiceDate, this.newInvoiceDate).subscribe((res)=>{})
                        await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 15, this.milestoneID, billingValue, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                        await this.resourceLoadingComponent.releaseForCosting().then((res)=>{
                          console.log("Resource Costing",)
                        })
                        const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
                        this.toasterService.showSuccess(billingUpdate_success, 10000)
                        
                        let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                        let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                        this.router.navigateByUrl(navigationUrl);
                        this.submitProgress=false;
                      }
                      else{
                        const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                        this.toasterService.showError(billingUpdate_unsuccess);
                        this.submitProgress=false;
                        this.confirmDisabled=false;
                      }
                    })
            }
          }
        }
        else
        {
          this.toasterService.showWarning('Please Plan for Total Budget',20000);
          this.confirmDisabled =false;
          this.submitProgress=false;
          return;
        }

      }
      else{
        this.confirmDisabled =false;
        this.submitProgress=false;
      }
    }
  
  }
  downloadCostingSheet() {
    
    let downloaddata=[]
    for(let item of this.items){
      downloaddata.push({'Employee ID':item['associate_id'],'Name':item['name'],'Billable Hours':item['billable_hours'],'Non Billable Hours':item['non_billable_hours'],'Non Billable Hours Flexi':item['non_billable_hours_flexi'],'Non Billable Hours Growth':item['non_billable_hours_growth'],'timesheet_hours':item['timesheet_hours'],'Previous Billable Hours':item['previous_billable_hours'],'Previous Non Billable Hours':item['previous_non_billable_hours'],'Previous Non Billable Hours Flexi':item['previous_non_billable_hours_flexi'],'Previous Non Billable Hours Growth':item['previous_non_billable_hours_growth'],'previous_timesheet_hours':item['previous_timesheet_hours'],'Planned Hours':item['planned_hours'],'Rounded Hours':item['rounded_hours'],'Actual Billale Hours':item['actual_billable_hours'],'Per Hours Rate':item['per_hour_rate'],'Value':item['value']})
    }
    this.exceltoJson.exportAsExcelFile(downloaddata,this.data.label +'- Billing Advice')
  }

  getSumRoundedHourForPosition(data,  id){
    

    let employeeDataPosition = _.where(data, { rate_card_id: id})

    let afterRoundedHours = 0

    for(let employee of employeeDataPosition){
        afterRoundedHours += parseFloat(employee['after_zifo_timesheet_hours'])
    }

    return afterRoundedHours

  }

  getSumPlannedHourForPosition(data,  id){
    

    let employeeDataPosition = _.where(data, { rate_card_id: id})

    let afterRoundedHours = 0

    for(let employee of employeeDataPosition)
    {
        if(employee['billable']==1)
          afterRoundedHours += employee['planned_hours']
    }

    return afterRoundedHours

  }

  getSumTimesheetHoursForAssociate(data, item){

    let after_timesheet_hours_overlapped = _.where(data, { associate_id: item['associate_id'], is_overlapped: 1})


    let totalRoundoff = 0
    let totalAfterTimesheetHours =0;

    if(after_timesheet_hours_overlapped.length>0)
    {
      for(let time of after_timesheet_hours_overlapped){
        totalAfterTimesheetHours +=  time['after_zifo_timesheet_hours']
      }

      if(totalAfterTimesheetHours!=0)
        totalRoundoff = item['rounded_hours'] * (item['after_zifo_timesheet_hours']/totalAfterTimesheetHours)
    }
    else
    {
      totalRoundoff = item['rounded_hours']
    }

    



    return totalRoundoff
  }


  async syncRoundoff(){

    let associates = _.uniq(_.pluck(this.items,"associate_id"))

    this.roundoff_sync = true;
    this.toasterService.showSuccess("Sync In-Progress", 3000)


    await this.PmBillingService.syncRoundOff(this.ItemID, this.milestoneID, moment(this.milestoneStartDate).format("YYYY-MM-DD"), moment(this.milestoneEndDate).format("YYYY-MM-DD"),  associates).then(async(res)=>{
      
        await this.PmBillingService.updateRoundOffFlag(this.milestoneID)
    })

    
  }

  /**
   * @description Initializing Invoice List
   */
  async initializeInvoice(){
    this.invoiceLoader = true;
    this.invoiceList = [];
    this.unitList = await this.getUnitMasterList();
    let invoiceData : any = await this.getMilestoneInvoiceDetails();
    if(invoiceData.length > 0){
      for (let item of invoiceData) {
        this.invoiceList.push({
          description: item.description ? item.description : '' ,
          rate: item?.rate,
          quantity: item?.quantity,
          unit: item?.unit,
          amount: item?.amount
        });
      }
    }
    else{
      let milestone_value_invoice_details = this.matrixConfig ? (this.matrixConfig['milestone_value_invoice_details'] ? true :  false) : false;

      let milestoneValue = this.getMilestoneValue()
      let billingValue = this.getTotalBillingValue();
    
      let rateValue = milestone_value_invoice_details ? milestoneValue : billingValue

      
      this.invoiceList.push(
        { 
          description: this.data.label,
          rate: rateValue,
          quantity:1,
          unit:1,
          amount: rateValue
      });
    }
    this.invoiceLoader = false
  }

  /**
   * @description Updating Section List
   */
  async updateSectionList() {

    await this.authService.getReadWriteAccess(this.projectID, this.ItemID).then(async(res)=>{
      if(res)
      {
        
        if(this.milestone_status_id<8 || this.milestone_status_id==15)
        {
          this.saveButtonEnabled = this.matrixConfig ? (this.matrixConfig['save_submit'] ? true :  false) : true;
          this.accruedButtonEnabled = this.matrixConfig ? (this.matrixConfig['accrual_submit'] ? true :  false) : false;
          this.invoiceButtonEnabled = this.matrixConfig ? (this.matrixConfig['invoice_submit'] ? true :  false) : false;
          this.cancelButtonEnabled = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 305) && this.matrixConfig ? (this.matrixConfig['cancel_submit'] ? true : false) : false;
          
        }

        if(this.milestone_status_id == 8)
        {
          this.reversalButtonEnabled = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 95) && this.matrixConfig ? (this.matrixConfig['reversal_submit'] ? true : false): false
          this.enableCheckBoxClass = this.matrixConfig ? (this.matrixConfig['enable_checkbox'] ? true : false): false
        }
        if(this.milestone_status_id == 15)
        {
          this.reversalButtonEnabled = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 94) && this.matrixConfig ? (this.matrixConfig['reversal_submit'] ? true : false): false
          this.enableCheckBoxClass = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 307) && this.matrixConfig ? (this.matrixConfig['partial_invoice_submit'] ? true : false): false
          this.partialInvoiceButtonEnabled = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 307) && (this.matrixConfig ? (this.matrixConfig['partial_invoice_submit'] ? true :  false) : true)
          
        }
        if(this.milestone_status_id >=8 && this.milestone_status_id<15)
        {
          this.creditNoteButtonEnabled = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 306) && (this.matrixConfig ? (this.matrixConfig['credit_note_submit'] ? true : false): false)
          this.enableCheckBoxClass = await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 306) && this.matrixConfig ? (this.matrixConfig['credit_note_submit'] ? true : false): false
          
        }
      }
    })
    
    if(this.at_risk)
    {
      this.invoiceButtonEnabled=false;
    }
    this.editMode = this.matrixConfig ? (this.matrixConfig['billing_advice_stepper_disabled'] ? false :  true) : false;
    this.billingStepper = this.matrixConfig ? (this.matrixConfig['billing_advice_stepper'] ? true :  false) : true;
    this.invoiceStepper = this.matrixConfig ? (this.matrixConfig['invoice_details_stepper'] ? true :  false) : true;
    
    // let invoiceStepper = this.matrixConfig ? (this.matrixConfig['invoice_details_stepper'] ? true :  false) : true;
    

    if (this.matrixConfig) {
      this.sectionList.forEach(section => {
        
        // Check if status_id is greater than 8 and not equal to 15
        if (this.data.status_id >= 8 && this.data.status_id != 15) {
          section.is_completed = true;
        }
    
        // Check if matrixConfig.section is defined and then access stepperName
        section.is_visible = this.matrixConfig[section.stepperName] ? true : false;
      });
    }
    
    
    const visibleSections = this.sectionList.filter(section => section.is_visible);
    
    this.sectionList = visibleSections
    this.templateId = (this.sectionList.length > 0 ?  this.sectionList[0].key_id : null);

    await this.intiateBillingAdviceAction()

    if(!this.editMode){
      for(let item of this.invoiceGridConfig){
        if(item['type'] == 'action'){
          item['isVisible'] = false;
        }
        else if(item['type'] != 'dropdown-text'){
          if(item['type'] == 'inputTextArea'){
            item['type'] = 'text-overflow'
          }
          else if(item['type'] == 'text-decimal'){
            item['type'] = 'text-decimal'
          }
          else{
            item['type'] = 'text'
          }
        }
      }
    }
  }

  /**
   * @description Adding Invoice Line Item
   */
  addInvoice() {
    this.invoiceList.push({ description: '', rate: null,quantity:null,unit:1,amount:null
    });
  }
  /**
   * @description Removing the Invoice Line Item 
   * @param index 
   */
  removeInvoice(index: number) {
    this.invoiceList.splice(index, 1);
  }

  /**
   * @description For Navigating between the sections
   * @param index
   * @param type
   */
  async navigateSection(index: number, type: 'current' | 'back' | 'next') {
    this.isComponentLoading=true;
    if (!this.sectionList || this.sectionList.length === 0) {
      return;
    }
  
    if (index < 0 || index >= this.sectionList.length) {
      return;
    }
  
    let newIndex = index;
  
    if (type === 'current') {
      newIndex = index;
    } else if (type === 'back') {
      newIndex = index - 1;
    } else if (type === 'next') {
      let percentage = this.getCummulativeMonthProgress()
      if(percentage>100)
      {
        const milestone_percentage = 
        'Your '+this.getNameByFormConfig("new-billing-advice", "cummulative_month_progress", "Project Progress")+'  goes beyond 100%. Adjust your '+this.getNameByFormConfig('new-billing-advice', 'future_effort_required', "Future Effort Required") 
        
        this.toasterService.showWarning(milestone_percentage, 10000);
        return;
      }
      newIndex = index + 1;
    }
  
    if (newIndex < 0 || newIndex >= this.sectionList.length) {
      return;
    }
  
    this.stepId = newIndex + 1;
    this.templateId = this.sectionList[newIndex].key_id;
    if(this.templateId == 2){
      await this.initializeInvoice();
    }
    if(this.templateId == 3){
      
      await this.intializeBillingPlan();
    }
  }

  /**
   * @description For Displaying Total Value of a Specific Column
   * @param key 
   */
  getTotalValue(key){
    return this.invoiceList.reduce((sum, item) => {
      return sum + (parseFloat(item[key]) || 0);
    }, 0);
  }

  /**
   * @description Calculating the Total Amount
   * @param event 
   * @param index 
   */
  getTotalAmount(event,index){
  
    let item = this.invoiceList[index];
      
    // Calculate the total amount
    let rate = item.rate || 0;
    let quantity = item.quantity || 0;
    item.amount = rate * quantity;
  }

  openDatePicker() {
    if(this.editMode){
      this.datepicker.open();
    }
  }
  onDateChange(event: any) {

    this.oldInvoiceDate = {
        "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
    }
    this.data.invoice_date = event.value;

    if(this.data.invoice_date=="" || this.data.invoice_date==" " || this.data.invoice_date==null || this.data.invoice_date=="null" || this.data.invoice_date=="0000-00-00" || this.data.invoice_date=="Invalid Date")
    {
      //this.PmBillingService.updateInvoiceDate(this.milestoneID, this.data.invoice_date)
    }
    this.datepicker.open();
    
  }


  onInvoiceDateChange(event: any)
  {
    
    this.oldInvoiceDate = {
      "Invoice Date": moment(this.data.invoice_date).format("DD-MMM-YYYY")
    } 

    let invoice_date = moment(event).format("YYYY-MM-DD");
   
    if(invoice_date !="" && invoice_date !=" " && invoice_date !=null && invoice_date !="null" && invoice_date !="0000-00-00" && invoice_date !="Invalid Date")
    {
      
      // this.PmBillingService.updateInvoiceDate(this.milestoneID, invoice_date).subscribe((res)=>{
        
      // })
    }
  }

  toggleEmployeeLevel(event,position){
    
    for(let item of this.PositionData){
      if(position.id==item['rate_card_id'] || position.id == item.id){
        item['is_employee_level'] = !event.checked
        if(this.allowActualHrsDecimal){
          item['actual_billable_hours'] = 0;
          item['value'] = 0;
        }
      }
      
    }
    
    for(let data of this.PositionData)
    {
      if(position.id == data['id'])
      {
        if(this.monthly_hours_flag && this.dialogData.status_id<8)
          {
              
              let billingPlan = _.where(this.billingPlanData,{month: this.milestone_month, year: this.milestone_year, position_id: data['id']})

              if(billingPlan.length>0)
              {
                data['actual_billable_hours'] = data['actual_billable_hours'] ==0 ? billingPlan[0]['planned_hours'] : data['actual_billable_hours'];
              }
              else
              {
                data['actual_billable_hours'] = data['actual_billable_hours'] ==0 ? 0 : data['actual_billable_hours']
              }
          }
        }
    }

    this.formatActualBillableHours(position.id);
  }

  getMilestoneValue(){
    const item = this.data.value.find(item => item.currency_code === this.code);
    return item ? item.value : undefined;
  }

  /**
 * @description Fetching Milestone Invoice Details
 * @returns 
 */
getMilestoneInvoiceDetails() {
  return new Promise((resolve,reject) => {
    this.subs.sink = this.PmBillingService.getMilestoneInvoiceDetails(this.data.Mid).subscribe(
       (res:any) => {     
        if(res && res['messType'] == 'S') {
           resolve((res['data'] && res['data'].length > 0 ) ? res['data'] : [])
        } 
        else{
          resolve([]);
        }
      },
      (err) => {
        console.log(err);
        this.toasterService.showError('Failed to fetch Invoice Details')
        resolve([]);
      }
    );
  });
 }
 async getVersionHistory():Promise<void>{
    this.subs.sink = this.PmBillingService.getBillingAdviceVersionHistory(this.data.Mid).subscribe(
      (res: any) => {
        this.versionList = res.data.map(item => ({
          changed_on: new Date(item.created_by),
          created_by_name: item.user_name,
          version_name: item.version_id
        }));
        this.currentVersionId = this.getMaxVersionId(this.versionList);
       },
       (err: any) => {
         console.log(err);
        }
    );
 }
 /**
  * @description Fetching Total Billing Value
  * @returns 
  */
 getTotalBillingValue(){
  let billing_value = 0;


  
  for(let item of this.PositionData)
  {
    if(item['has_child'])
      billing_value += parseFloat(item['current_month_revenue'].toString()) || 0;
  }
    
  
  
  return billing_value;
 }


 /**
  * @description Getter Function to return in Date Format
  * @param date 
  * @returns 
  */
 getDateWithFormat(date){
  return (date && date != 'Invalid date') ? moment(date).format('DD-MMM-YYYY') : '-'
  
  // const dateString = typeof date === 'string' ? date : date?.toString();
  // const isValid = moment(dateString, moment.ISO_8601, true).isValid();
  // return isValid ? moment(dateString).format('DD-MMM-YYYY') : '-';
 }
 
 /**
  * @description fetching Value from the dropdown
  * @param list 
  * @param id 
  * @param listValue Whether to fetch List or List Value
  */
 getDropdownValue(list,id,listValue=false){
  if(listValue){
    return this[list] || [];
  }
  else{
    for(let item of (this[list] || [])){
      if(item.id == id){
        return item.name
      }
    }
    return null
  }
 }

 getSumActualBillableHoursForPosition(data,  id){
    

  let employeeDataPosition = _.where(data, { rate_card_id: id})

  let afterRoundedHours = 0

  for(let employee of employeeDataPosition){
      afterRoundedHours += parseFloat(employee['actual_billable_hours'])
  }

  return afterRoundedHours

}

  /**
 * @description Fetching UOM Master Data
 * @returns 
 */
  getUnitMasterList() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this.PmBillingService.getUnitListMasterData().subscribe(
        (res:any) => {
          
          if(res) {
            resolve(res)
          } 
          else{
            resolve([]);
          }
        },
        (err) => {
          console.log(err);
          this.toasterService.showError('Failed to fetch Unit List Master Data')
          resolve([]);
        }
      );
    });
  }

  formatActualBillableHours(id){

        let value = 0;

        let is_employee_level = false;

        for(let item of this.PositionData)
        {
           if(item['id']==id)
           {
             is_employee_level = item['is_employee_level']
             value = item['actual_billable_hours'];
           }
        } 
  
        if(!is_employee_level)
        {
          for(let item of this.items)
          {
            if(item['rate_card_id']==id)
            {
               
                 let roundedHoursPosition = this.getSumRoundedHourForPosition(this.items,  id)
 
                 if(roundedHoursPosition>0)
                 {
                  
                    item['actual_billable_hours'] = ((item['after_zifo_timesheet_hours'] / roundedHoursPosition )*(value))
                    if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined)
                    {
                       item['value']=item['actual_billable_hours']*item['per_hour_rate']
                       item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                    }
                   
                 }
                 else
                 {
 
                   if(this.planned_hours_if_timesheet_not_present)
                   {
                     let plannedHoursPosition = this.getSumPlannedHourForPosition(this.items, id)
 
                     if(plannedHoursPosition>0)
                     {
                       item['actual_billable_hours'] = ((item['planned_hours'] / plannedHoursPosition )*(value))
                       if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
                         item['value']=item['actual_billable_hours']*item['per_hour_rate']
                         item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                       }
                     }
                     else
                     {
   
                       item['actual_billable_hours'] = 0
                       if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
                         item['value']=item['actual_billable_hours']*item['per_hour_rate']
                         item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                       }
                     }
                   }
                   else
                   {
 
                     item['actual_billable_hours'] = 0
                     if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined){
                       item['value']=item['actual_billable_hours']*item['per_hour_rate']
                       item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
                     }
                   }
                   
                 }
                 
               
            }
 
          }
        }
        else
        {
          

          

          for(let item of this.PositionData)
          {
             if(item['rate_card_id']==id)
             {
               item['actual_billable_hours'] = parseFloat(item['actual_billable_hours']).toFixed(0)
             }
          }

          // console.log("Position Dataa List Value Check List Value", this.PositionData)

          for(let item of this.PositionData)
          {
            if(item['id']==id){
              
              item['actual_billable_hours'] = this.getSumActualBillableHoursForPosition(this.items, id)

              if(item['actual_billable_hours']!=undefined && item['per_hour_rate']!=undefined)
              {
                item['value']=item['actual_billable_hours']*item['per_hour_rate']

                item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
              }
            }

            
            
          }

          for(let item of this.PositionData)
          {
                item['value']=item['actual_billable_hours']*item['per_hour_rate']

                item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
          }



        }
         
       
  }

  /**
   * @description Checking for Empty Invoice Description
   * @returns 
   */
  checkInvoiceDetails(){
    if(this.matrixConfig?.invoice_details_stepper){
      for(let invoice of this.invoiceList){
        if(invoice.description == undefined || invoice.description == null || invoice.description.trim() === ''){
          return false;
        }
      }
      return true;
    }
    return true;
  }


  async checkYTBData(){

    let billing_value = this.getTotalBillingValue().toFixed(2);
    let milestone_value : number = 0

    for(let item of this.dialogData.value){
      if(this.code===item['currency_code']){
            milestone_value=item['value']
      }
    }

    let value = this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value;

    return new Promise((resolve, reject)=>{
      this.PmBillingService.validateYTBData(this.projectID, this.ItemID, this.milestoneID, this.milestone_type, value).subscribe((res)=>{
          if(res['messType']=="S")
          {
              resolve(res['data'])
          }
          else
          {
              resolve(res['data'])
          }
      })
    })
  }

  async checkAccruedData(){

    let billing_value = this.getTotalBillingValue().toFixed(2);
    let milestone_value : number = 0

    for(let item of this.dialogData.value){
      if(this.code===item['currency_code']){
            milestone_value=item['value']
      }
    }

    let value = this.matrixConfig?.milestone_value_invoice_details ? milestone_value : billing_value;

    return new Promise((resolve, reject)=>{
      this.PmBillingService.validateAccruedData(this.projectID, this.ItemID, this.milestoneID, value).subscribe((res)=>{
          if(res['messType']=="S")
          {
              resolve(res['data'])
          }
          else
          {
              resolve(res['data'])
          }
      })
    })
  }
  /**
   * @description Checking for Timesheet 0 Hours
   * @param value 
   * @returns 
   */
  isZero(value: any): boolean {
    return value != null && parseFloat(value) === 0;
  }

  isZeroTimesheetHours(item){
      //console.log("ZERO TIMESHEET HOURS", item)
      let result = _.where(this.formConfig,{type:"new-billing-advice", field_name:"timesheet_hours_disable", is_active: true})

      if(result.length>0)
      {
          if(result[0]['enable_employee_level'] && item.is_employee_level)
          {

            return false
          }
          else
          {
            let value =  result[0]['key_name'] ? item[result[0]['key_name']] : item["after_zifo_timesheet_hours"]

            return value != null && parseFloat(value) === 0;
          }
      }
      else
      {
          return false
      }
  }

  isWithinTolerance(value1, value2, tolerance) 
  {

    let difference = Math.abs(value1 - value2);

    return difference <= tolerance;
  }

  async reversalData(){
    this.saveDisabled = true;
    this.submitProgress = true;
    let checkReverseAccrued = _.findWhere(this.formConfig,({type:"billing-advice", field_name:"reverse-accrued", is_active: true}))

    let requestedStatus = this.milestone_status_id == 15 && checkReverseAccrued ? 16 : 4;
    let approvalsDetails = await this.checkForApprovals( 5, this.data, requestedStatus)
    if(approvalsDetails?.isForApproval){
      if(approvalsDetails?.navigate){
        let approvals = await this.triggerApprovals(5, this.data, requestedStatus);
        if(approvals?.navigate){
          let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/milestone`;
          this.router.navigateByUrl(navigationUrl);
          this.submitProgress = false;
          return;
        }else{
          this.confirmDisabled=false;
          this.submitProgress = false;
          return;
        }
      }
    }else{
      this.submitProgress=false;
      return;
    }

    if(this.milestone_status_id == 8)
    {
      await this._utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to revert from YTB?", "").then(async (result) => {
        if (result) {
          await this.PmBillingService.revertMilestoneFromYTB(this.milestoneID).then(async (res)=>{
                   if(res['messType']=='S')
                  {
                    const revert_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.revert_success ? this.retrieveMessages[0].errors.revert_success : 'Milestone Successfully Reverted From YTB Status' : 'Milestone Successfully Reverted From YTB Status';
                    this.toasterService.showSuccess(revert_success,10000)
                    this.onCloseClick();
                  }
                  else
                  {
                    const revert_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.revert_unsuccess ? this.retrieveMessages[0].errors.revert_unsuccess : 'Milestone UnSuccessfull to Revert From YTB Status' : 'Milestone UnSuccessfull to Revert From YTB Status';
                    this.toasterService.showWarning(revert_unsuccess, 10000)
                  }
                  this.submitProgress = false;
          })
        }
        else
        {
          this.submitProgress=false;
        }
      });
    }
    if(this.milestone_status_id == 15)
    {
  
      await this._utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to revert from Accrued?", "").then(async (result) => {
        if (result) {

          if(checkReverseAccrued)
          {
            await this.PmBillingService.moveMilestoneToReverseAccrued(this.milestoneID).then(async (res)=>{
              if(res['messType']=='S')
              {

                const revert_success ='Milestone Successfully moved to Reverse Accrued';
                this.toasterService.showSuccess(revert_success,10000)
                this.onCloseClick();
              }
              else
              {
            
                const revert_unsuccess = 'Milestone movement to Reverse Accrual is unsuccessful' ;
                this.toasterService.showWarning(revert_unsuccess, 10000)
              }
              this.submitProgress = false;
              
            })
          }
          else
          {
            await this.PmBillingService.revertMilestoneFromAccrual(this.milestoneID).then(async (res)=>{
              if(res['messType']=='S')
              {

                const revert_success ='Milestone Successfully Reverted From Accrued Status';
                this.toasterService.showSuccess(revert_success,10000)
                this.onCloseClick();
              }
              else
              {
            
                const revert_unsuccess = 'Milestone UnSuccessfull to Revert From Accrued Status' ;
                this.toasterService.showWarning(revert_unsuccess, 10000)
              }
              this.submitProgress = false;
          
            })
          }
        }
      });
    }
  }

  async cancelData(){
    await this._utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to cancel the milestone","").then(async(result) => {
      if (result) {
        this.submitProgress = true;
        this.newStatus = {
          Status: this.getNameByIdPipe.transform(6, this.milestone_status_list)
        }
        await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 6, this.milestoneID, 0, moment(this.milestoneEndDate).format("MM"), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44).then((res)=>{
          this.submitProgress = false;
          this.onCloseClick();
        })
      }

    });
  }

  async creditNoteData(){
 

    this.creditNoteSelected = true

    if(this.creditNoteSelected)
    {
      this.enableCheckBoxClass=true;
      this.editMode =true;
    }
    else
    {
      this.enableCheckBoxClass=false;
      this.saveButtonEnabled=false;
      this.invoiceButtonEnabled=false;
      this.accruedButtonEnabled=false;
      this.partialInvoiceButtonEnabled=false;
      this.editMode=false;
    }
    
  }


  onCheckBoxToggleChange(event, position)
  {
    
    for(let item of this.PositionData)
    {
      if(position.id==item['rate_card_id'] || position.id == item.id)
      {
        item['is_selected'] = event
      }
    }
    
    let rate_card_ids = _.pluck(this.PositionData,"rate_card_id")

    
    for(let card of rate_card_ids)
    {
        let condition = true;
        let records = _.where(this.PositionData,{rate_card_id: card});

        for(let rec of records)
        {
            if(!rec['is_selected'])
            {
              condition =false;
              break;
            }
        }
        
        if(!condition)
        {
          for(let item of this.PositionData)
          {
              if(item['id'] == card)
              {
                  item['is_selected']=false;
              }
          }
          continue;
        }
        if(condition)
        {
          for(let item of this.PositionData)
          {
              if(item['id'] == card)
              {
                  item['is_selected']=true;
              }
          }
        }

        
    }


    for(let item of this.items)
    {
        let checkIsSelected = _.findWhere(this.PositionData,{id: item['id']})

        if(checkIsSelected)
        {
            item['is_selected'] = checkIsSelected['is_selected'] ? true: false;
        }
        else
        {
           item['is_selected']= false;
        }
    }

    

    
  }

  viewLineItemComment(item){
    
    console.log(item)
    const openBillingAdviceComment = this.dialog.open(BillingAdviceCommentComponent, {
     
   
      data: { comments: item ? item['comments'] :"", formConfig: this.formConfig, editMode: this.editMode}
    });

    openBillingAdviceComment.afterClosed().subscribe(async (result:any)=>{
      if(result && result.length>0)
      {
        console.log("Result", result)
        item['comments'] = result
      }
    })

  }

  async partialInvoiceData(){
    
    this.saveDisabled = true;
    this.submitProgress = true;
    let selected = _.uniq(_.pluck(this.items,"is_selected"))
    console.log("Items", this.items)
    if(this.at_risk && this.item_status_id != 4)
    {
      const content1 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.at_risk_error ? this.retrieveMessages[0].errors.at_risk_error : 'Project in AT Risk Flag' : 'Project in AT Risk Flag';
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      

      this.toasterService.showWarning(content1 +' '+ content2,10000);
      this.submitProgress = false;
    }
    else if((this.item_status_id == 7 || this.item_status_id == 12 || this.item_status_id == 6))
    {
      let checkName = _.findWhere(this.PmMasterService.status_list,{id: this.item_status_id})
      const content1 = checkName ? this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.status_error + checkName['name'] +" Status! "? this.retrieveMessages[0].errors.status_error + checkName['name']+" Status! " : 'Project is in '+checkName['name'] +" Status! ": 'Project is in '+ checkName['name']+" Status! " :"";
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      
      this.toasterService.showWarning(content1 +' '+ content2,10000);
      this.submitProgress = false;
    }
    else if(selected.length ==1 && !selected[0]){

      
        const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.selected_error_pi ? this.retrieveMessages[0].errors.selected_error_pi : 'Please select records to proceed with Partial Invoice!' : 'Please select records to proceed with Partial Invoice!';
      
        this.toasterService.showWarning(content2,10000);
        this.submitProgress = false;
     

      
    }
    else if(!this.editMode && selected.length ==1 && selected[0]){

      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.all_selected_error_pi ? this.retrieveMessages[0].errors.all_selected_error_pi : 'All records should not be selected for Partial Invoice!' : 'All records should not be selected for Partial Invoice!';
      
      this.toasterService.showWarning(content2,10000);
      this.submitProgress = false;
    }
    
    else
    {
      this.confirmDisabled = true;
      //this.checkNegative();
      if(this.valid){
        let billing_value =0
        let milestone_value : number = 0
        let message
        for(let item of this.dialogData.value){
          if(this.code===item['currency_code']){
               milestone_value=item['value']
          }
        }
        for(let item of this.items){
          billing_value+=parseFloat(item['value'].toString())
        }
        if(this.data.invoice_date=="" || this.data.invoice_date==" " || this.data.invoice_date==null || this.data.invoice_date=="null" || this.data.invoice_date=="0000-00-00" || this.data.invoice_date=="Invalid Date")
        {
          if(!this.billingStepper)
          {
            const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_date_err_msg ? this.retrieveMessages[0].errors.invoice_date_err_msg : 'Kindly Enter Valid Invoice Date' : 'Kindly Enter Valid Invoice Date';
            this.toasterService.showWarning(invoice_date_err_msg, 10000);
            this.submitProgress = false;
            return;
          }
          else
          {
            this.data.invoice_date = this.milestoneEndDate;
          }
        }
        if(!this.checkInvoiceDetails()){
          const invoice_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_err_msg ? this.retrieveMessages[0].errors.invoice_err_msg : 'Kindly Enter Valid Invoice Description' : 'Kindly Enter Valid Invoice Description';
          this.toasterService.showWarning(invoice_err_msg, 10000);
          this.submitProgress = false;
          return;
        }

        if(this.po_number == null || this.po_number == undefined || this.po_number.trim() === '' || this.po_number == 'null'){
          this.toasterService.showWarning('PO number not available; hence, invoice cannot be raised.', 10000);
          this.submitProgress = false;
          return;
        }
        let totalAmount = this.getTotalValue('amount').toFixed(2);
        let billingValue = this.getTotalBillingValue().toFixed(2);
        let overall_po_value_check = this.matrixConfig ? (this.matrixConfig['overall_po_value_check'] ? true : false) : true;
        let billing_milestone_value_check  = this.matrixConfig ? (this.matrixConfig['billing_milestone_value_check'] ? true : false) : false;
        let tolerance_value_check = this.matrixConfig ? (this.matrixConfig['is_tolerance_present'] ? true: false) : false;

        if(parseFloat(billingValue) <= 0 && this.matrixConfig &&  this.matrixConfig?.billing_advice_stepper){
          this.toasterService.showWarning('Billing Advice Value should be greater than 0', 10000);
          this.submitProgress = false;
          return;
        }

        if(billing_milestone_value_check){
          let checkBillingValue = parseFloat(billingValue)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkBillingValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice and Milestone exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkMilestoneValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Milestone Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }
        
        if(overall_po_value_check && !this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkBillingValue = parseFloat(billingValue);

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkBillingValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }

        if(overall_po_value_check && this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Milestone Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkMilestoneValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Milestone Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }
        
        
        
        if(await this.checkYTBData())
        {
          let check = !this.matrixConfig?.milestone_value_invoice_details ? "Billing Advice Value must not exceed Order Value" : "Milestone Value must not exceed Order Value"
          this.toasterService.showWarning(check, 10000);
          this.submitProgress = false;
          return;
        }
        if(billing_value>milestone_value){
          message="Milestone Value is lesser than Billable Milestone Value! Do You Want To Continue? "
        }
        if(billing_value<milestone_value){
          message="Milestone Value is greater than Billable Milestone Value! Do You Want To Continue? "
        }

        let partialInvoiceData =[]

        let accrualInvoiceData =[]

        let writeOffInvoiceData =[];

        

        
          for(let item of this.items)
          {
    
            if(selected.length>1 && item['is_selected'])
            {

              let records = _.where(this.oldItems, {id: item.id})

              if(records.length>0)
              {
                  records[0]['actual_billable_hours'] = records[0]['actual_billable_hours'] - item['actual_billable_hours']
                  records[0]['actual_billable_hours'] = records[0]['actual_billable_hours']<0 ? 0 : records[0]['actual_billable_hours']
                  records[0]['value'] = records[0]['actual_billable_hours'] * records[0]['per_hour_rate']
                  
                  writeOffInvoiceData.push(records[0])
              }

              partialInvoiceData.push(item)
            }
            if(selected.length>1 && !item['is_selected'])
            {
              accrualInvoiceData.push(item)
            }
            if(selected.length==1)
            {
              let records = _.where(this.oldItems, {id: item.id})

              if(records.length>0)
              {
                  records[0]['actual_billable_hours'] = records[0]['actual_billable_hours'] - item['actual_billable_hours']
                  records[0]['actual_billable_hours'] = records[0]['actual_billable_hours']<0 ? 0 : records[0]['actual_billable_hours']
                  records[0]['value'] = records[0]['actual_billable_hours'] * records[0]['per_hour_rate']
                  
                  writeOffInvoiceData.push(records[0])
              }

              

              partialInvoiceData.push(item)
            }
          }

      console.log("Accrual Invoice Data", accrualInvoiceData, writeOffInvoiceData, partialInvoiceData)
      if(this.items.length == partialInvoiceData.length && writeOffInvoiceData.length!=0)
      {
        const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.all_selected_error_pi ? this.retrieveMessages[0].errors.all_selected_error_pi : 'All records should not be selected for Partial Invoice!' : 'All records should not be selected for Partial Invoice!';
      
        this.toasterService.showWarning(content2,10000);
        this.submitProgress = false;

      }
       
        


        
      if(this.submitProgress)
      {
        let approvalsDetails = await this.checkForApprovals(2 ,partialInvoiceData, 15)
        if(approvalsDetails?.isForApproval){
          if(approvalsDetails?.navigate){
            let approvers = await this.PmBillingService.getApproversDetails(this.projectID, this.ItemID, 3);
            if(approvers['messType']=='E'){
              this.toasterService.showError(approvers['messText']);
              this.submitProgress=false;
              return;
            }else if(approvers['messType']=='W'){
              this.toasterService.showWarning(approvers['messText'], 10000);
              this.submitProgress=false;
              return;
            }

            if(approvers['data'] && approvers['data'].length == 0){
              this.toasterService.showWarning("Approvers Details not found for raising the request", 10000);
              this.submitProgress=false;
              return;
            }
          }
          else{
            this.submitProgress=false;
            return;
          }
        }
        if(this.mode=='Create')
          {
                  let invoice_list = [];
                  await this.PmBillingService.saveBillingAdvise(this.data.Mid,this.billingStepper ? accrualInvoiceData : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, this.data.value, false).then(async (res:any)=>{
                      if(res['messType']=='S'){
                        this.newStatus = {
                          Status: this.getNameByIdPipe.transform(15, this.milestone_status_list)
                        }
                        await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 15, this.milestoneID, this.data.value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                        }
                        else{
                        const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                        this.toasterService.showError(billingUpdate_unsuccess);
                        return;
                        }
                  })
          }
          else if(this.mode='Update')
          {
                  let invoice_list = [];
                  await this.PmBillingService.updateBillingAdvise(this.data.Mid,this.billingStepper ? accrualInvoiceData : [], this.PositionData, this.data.projectID,this.data.itemID,invoice_list,this.po_number,this.data.invoice_date, this.data.value,false).then(async (res:any)=>{
                      if(res['messType']=='S')
                        {
                          this.newStatus = {
                            Status: this.getNameByIdPipe.transform(15, this.milestone_status_list)
                          }
                          await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 15, this.milestoneID, this.data.value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44)
                        }
                        else
                        {
                          const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
                          this.toasterService.showError(billingUpdate_unsuccess);
                          
                        }
                  })
          }
          let milestoneStatus = approvalsDetails?.isForApproval ? 17 : 8;
        this.PmBillingService.createPartialInvoiceMilestone(this.projectID, this.ItemID, partialInvoiceData, writeOffInvoiceData, this.milestoneID, this.milestoneStartDate, this.milestoneEndDate, milestoneStatus).then(async (res)=>{
          if(res['messType']=="S")
          {
            if(approvalsDetails?.isForApproval){
              if(approvalsDetails?.navigate){
                let data: any = {};
                data.parent_milestone_payload = {
                  data: this.data,
                  billing_type: this.billing_type,
                  actual_hours_config: this.actual_hours_config,
                  withOpportunity: this.withOpportunity
                };
                
                data.child_milestone_payload = {
                  data: { ...res['milestoneDetails'] },
                  billing_type: this.billing_type,
                  actual_hours_config: this.actual_hours_config,
                  withOpportunity: this.withOpportunity
                };
                
                let billingAdviseDetails = { ...res['milestoneDetails'] };
                billingAdviseDetails.payload_data = data;
                billingAdviseDetails.code = this.code;
                billingAdviseDetails.parent_milestone_id = this.milestoneID;

                let approvals = await this.triggerApprovals(3, billingAdviseDetails, 8);
                if(approvals?.navigate){
                  let navigate_to = this.bill_enable ? 'bills' : 'milestone'
                  let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
                  this.router.navigateByUrl(navigationUrl);
                  this.submitProgress = false;
                  return;
                }else{
                  this.confirmDisabled=false;
                  this.submitProgress = false;
                  return;
                }
              }else{
                this.confirmDisabled=false;
                this.submitProgress = false;
                return;
              }
            }
            this.submitProgress=false;
            this.confirmDisabled=false;
            const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
            this.toasterService.showSuccess(billingUpdate_success, 10000)
            let navigate_to = this.bill_enable ? 'bills' : 'milestone'
             let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
            this.router.navigateByUrl(navigationUrl);
          }
          else
          {
            const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
            this.toasterService.showError(billingUpdate_unsuccess);
          }
        })
      }
    }
  }

    
  }

  async creditNoteSubmitData()
  {
    this.saveDisabled = true;
    this.submitProgress = true;

    
    if(this.at_risk && this.item_status_id != 4)
    {
      const content1 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.at_risk_error ? this.retrieveMessages[0].errors.at_risk_error : 'Project in AT Risk Flag' : 'Project in AT Risk Flag';
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      

      this.toasterService.showWarning(content1 +' '+ content2,10000);
      this.submitProgress = false;
    }
    else if((this.item_status_id == 7 || this.item_status_id == 12 || this.item_status_id == 6))
    {
      let checkName = _.findWhere(this.PmMasterService.status_list,{id: this.item_status_id})
      const content1 = checkName ? this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.status_error + checkName['name'] +" Status! "? this.retrieveMessages[0].errors.status_error + checkName['name']+" Status! " : 'Project is in '+checkName['name'] +" Status! ": 'Project is in '+ checkName['name']+" Status! " :"";
      const content2 = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.ytb_error ? this.retrieveMessages[0].errors.ytb_error : 'Cannot be moved to YTB' : 'Cannot be moved to YTB';
      
      this.toasterService.showWarning(content1 +' '+ content2,10000);
      this.submitProgress = false;
    }
    else
    {

      if(this.billing_advice_action == "'creditNote'" || this.data.milestone_type == 6)
      {
        let commentMandatory = _.where(this.formConfig,{type:"new-billing-advice", field_name:"credit_note_attachment_mandatory", is_active: true})

        if(commentMandatory.length>0)
        {
            await this.PmBillingService.checkAttachmentPresentForMilestones([this.milestoneID]).then((res: any)=>{
                if(res && res.length==0)
                {
                    const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.attachment_mandatory_error ? this.retrieveMessages[0].errors.attachment_mandatory_error : 'Please add attachment before raising Credit Note' : 'Please add attachment before raising Credit Note';
                    this.toasterService.showWarning(invoice_date_err_msg, 10000);
                    this.submitProgress=false;
            
                    return;
                }
            })

            if(!this.submitProgress)
                return

            
            await this.PmBillingService.checkCommentPresentForMilestones([this.milestoneID]).then((res: any)=>{
              if(res && res.length==0)
              {
                  const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.comment_mandatory_error ? this.retrieveMessages[0].errors.comment_mandatory_error : 'Enter comments before raising Credit Note' : 'Enter comments before raising Credit Note';
                  this.toasterService.showWarning(invoice_date_err_msg, 10000);
                  this.submitProgress=false;
                  return;
              }
          })

          if(!this.submitProgress)
            return
        }
      }
      this.confirmDisabled = true;
      //this.checkNegative();
      if(this.valid){
        let billing_value =0
        let milestone_value : number = 0
        let message
        for(let item of this.dialogData.value){
          if(this.code===item['currency_code']){
               milestone_value=item['value']
          }
        }
        for(let item of this.items){
          billing_value+=parseFloat(item['value'].toString())
        }
        if(this.data.invoice_date=="" || this.data.invoice_date==" " || this.data.invoice_date==null || this.data.invoice_date=="null" || this.data.invoice_date=="0000-00-00" || this.data.invoice_date=="Invalid Date")
        {
          if(!this.billingStepper)
          {
            const invoice_date_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_date_err_msg ? this.retrieveMessages[0].errors.invoice_date_err_msg : 'Kindly Enter Valid Invoice Date' : 'Kindly Enter Valid Invoice Date';
            this.toasterService.showWarning(invoice_date_err_msg, 10000);
            this.submitProgress = false;
            return;
          }
          else
          {
            this.data.invoice_date = this.milestoneEndDate;
          }
        }
        if(!this.checkInvoiceDetails()){
          const invoice_err_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoice_err_msg ? this.retrieveMessages[0].errors.invoice_err_msg : 'Kindly Enter Valid Invoice Description' : 'Kindly Enter Valid Invoice Description';
          this.toasterService.showWarning(invoice_err_msg, 10000);
          this.submitProgress = false;
          return;
        }

        if(this.po_number == null || this.po_number == undefined || this.po_number.trim() === '' || this.po_number == 'null'){
          this.toasterService.showWarning('PO number not available; hence, invoice cannot be raised.', 10000);
          this.submitProgress = false;
          return;
        }
        let totalAmount = this.getTotalValue('amount').toFixed(2);
        let billingValue = this.getTotalBillingValue().toFixed(2);
        let overall_po_value_check = this.matrixConfig ? (this.matrixConfig['overall_po_value_check'] ? true : false) : true;
        let billing_milestone_value_check  = this.matrixConfig ? (this.matrixConfig['billing_milestone_value_check'] ? true : false) : false;
        let tolerance_value_check = this.matrixConfig ? (this.matrixConfig['is_tolerance_present'] ? true: false) : false;

        if(parseFloat(billingValue) <= 0 && this.matrixConfig &&  this.matrixConfig?.billing_advice_stepper){
          this.toasterService.showWarning('Billing Advice Value should be greater than 0', 10000);
          this.submitProgress = false;
          return;
        }

        if(billing_milestone_value_check){
          let checkBillingValue = parseFloat(billingValue)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkBillingValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice and Milestone exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkMilestoneValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Milestone Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }

        let invoiceValue = 0;

        for(let invoice of this.invoiceList)
        {
            invoiceValue += invoice['amount']
        }

        let creditNoteMilestoneValue = this.matrixConfig?.milestone_value_invoice_details ?  invoiceValue  : this.getTotalBillingValue()
        this.previousMilestoneValue = this.matrixConfig?.milestone_value_invoice_details ? this.getMilestoneValue() : this.previousMilestoneValue

        console.log("Previous Milestone Value", this.previousMilestoneValue, creditNoteMilestoneValue)

        

        if(this.previousMilestoneValue <creditNoteMilestoneValue)
        {
          const creditNoteMilestonePrevvalue = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.creditNote_unsuccess ? this.retrieveMessages[0].errors.creditNote_unsuccess : 'Please ensure the credit note value is less than or equal to the billed value.' : 'Please ensure the credit note value is less than or equal to the billed value.';
          this.toasterService.showWarning(creditNoteMilestonePrevvalue,300000);
          this.submitProgress = false;
          return;
        }
        
        if(overall_po_value_check && !this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkBillingValue = parseFloat(billingValue);

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkBillingValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Billing Advice Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkBillingValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Billing Advice Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }

        if(overall_po_value_check && this.matrixConfig?.milestone_value_invoice_details){
          let checkInvoiceValue = parseFloat(totalAmount)
          let checkMilestoneValue = parseFloat((milestone_value).toFixed(2));

          if(tolerance_value_check)
          {
            if(!this.isWithinTolerance(checkInvoiceValue, checkMilestoneValue, this.tolerance_value))
            {
              this.toasterService.showWarning(`Milestone Value and Invoice Value exceeds the allowed tolerance of ${this.tolerance_value}.`, 10000);
              this.submitProgress = false;
              return;
            }
          }
          else
          {
              if(checkMilestoneValue != checkInvoiceValue)
              {
                this.toasterService.showWarning('Milestone Value is not equal to Invoice Value', 10000);
                this.submitProgress = false;
                return;
              }
          }
        }
      
        
        
        
     

    let creditNoteInvoiceData =[]

    let selected = _.uniq(_.pluck(this.items,"is_selected"))
  
    for(let item of this.items){

      if(selected.length>1 && item['is_selected'])
      {
        creditNoteInvoiceData.push(item)
      }
      if(selected.length==1)
      {
        creditNoteInvoiceData.push(item)
      }
    }
    let approvalsDetails = await this.checkForApprovals(6 , creditNoteInvoiceData, 8)
    if(approvalsDetails?.isForApproval){
      if(approvalsDetails?.navigate){
        let approvers = await this.PmBillingService.getApproversDetails(this.projectID, this.ItemID, 6);
        if(approvers['messType']=='E'){
          this.toasterService.showError(approvers['messText']);
          this.submitProgress=false;
          return;
        }else if(approvers['messType']=='W'){
          this.toasterService.showWarning(approvers['messText'], 10000);
          this.submitProgress=false;
          return;
        }

   
        if(approvers['data'] && approvers['data'].length == 0){
          this.toasterService.showWarning("Approvers Details not found for raising the request", 10000);
          this.submitProgress=false;
          return;
        }
      }
      else{
        this.submitProgress=false;
        return;
      }
    }
    let milestoneStatus = approvalsDetails?.isForApproval ? 17 : 8;
    this.PmBillingService.createCreditNoteInvoiceMilestone(this.projectID, this.ItemID, creditNoteInvoiceData, this.invoiceList, this.milestoneID, this.milestoneStartDate, this.milestoneEndDate, this.matrixConfig?.milestone_value_invoice_details ? "milestone_value" :"billing_advice_value",  milestoneStatus).then(async(res)=>{
      if(res['messType']=="S")
      {
        if(approvalsDetails?.isForApproval){
          if(approvalsDetails?.navigate){
            let milestoneId = res['milestoneId'];
            let approvalDetails = {
              "milestoneId": milestoneId,
              "parent_milestone_id": this.data.Mid,

            }
            let approvals = await this.triggerApprovals(6, approvalDetails, 8);
            if(approvals?.navigate){
              this.submitProgress=false;
              this.confirmDisabled=false;
              let navigate_to = this.bill_enable ? 'bills' : 'milestone'
              let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
              this.router.navigateByUrl(navigationUrl);
              return;
            }else{
              this.confirmDisabled=false;
              this.submitProgress = false;
              return;
            }
          }else{
            this.confirmDisabled=false;
            this.submitProgress = false;
            return;
          }
        }
        this.submitProgress=false;
        this.confirmDisabled=false;
        const billingUpdate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_success ? this.retrieveMessages[0].errors.billingUpdate_success : 'Updated Successfully' : 'Updated Successfully';
        this.toasterService.showSuccess(billingUpdate_success, 10000)
        let navigate_to = this.bill_enable ? 'bills' : 'milestone'
        let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
        this.router.navigateByUrl(navigationUrl);
      }
      else
      {
        const billingUpdate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.billingUpdate_unsuccess ? this.retrieveMessages[0].errors.billingUpdate_unsuccess : 'Update billing UnSuccessful' : 'Update billing UnSuccessful';
        this.toasterService.showError(billingUpdate_unsuccess);
      }
    })
      }
    } 
}

async checkForApprovals(approvalGroupId, billingAdviseDetails, requestedStatus) {
  let isForApproval = false;
  let triggerApprovalGroupId = approvalGroupId
  let message = '';

  if (this.approvalForHoursDeviations && approvalGroupId === 1) {
    message = this.retrieveMessages.length > 0 ?
      this.retrieveMessages[0].alert.hours_deviation_approval ||
      "We've noticed a difference between the actual and timesheet billable hours. An approval request will be sent to the Delivery Head. Press Okay to proceed." :
      "We've noticed a difference between the actual and timesheet billable hours. An approval request will be sent to the Delivery Head. Press Okay to proceed.";
    for (let i = 0; i < billingAdviseDetails.length; i++) {
      const item = billingAdviseDetails[i];
      const billableHours = parseFloat(item.billable_hours);
      const actualBillableHours = parseFloat(item.actual_billable_hours);
      if (billableHours !== actualBillableHours) {
        isForApproval = true;
        break;
      }
    }
    if (!isForApproval) {
      isForApproval = this.approvalForBillingAdvice ? true : false
      triggerApprovalGroupId = 4
      message = this.retrieveMessages.length > 0 ?
        this.retrieveMessages[0].alert.billing_advice_approval ||
        "An Approval request will be raised to the Finance manager. Press Okay to proceed." :
        "An Approval request will be raised to the Finance manager. Press Okay to proceed.";
    }
  } else if (this.approvalForBillingAdvice && approvalGroupId === 1) {
    message = this.retrieveMessages.length > 0 ?
      this.retrieveMessages[0].alert.billing_advice_approval ||
      "An Approval request will be raised to the Finance manager. Press Okay to proceed." :
      "An Approval request will be raised to the Finance manager. Press Okay to proceed.";

    isForApproval = true;
    triggerApprovalGroupId = 4

  } else if (this.approvalForValueDeviations && approvalGroupId === 2) {
    message = this.retrieveMessages.length > 0 ?
      this.retrieveMessages[0].alert.value_deviation_approval ||
      "We've noticed a difference between the accrued and invoiced value. An approval request will be sent to the Delivery Head & Finance Manager. Press Okay to proceed." :
      "We've noticed a difference between the accrued and invoiced value. An approval request will be sent to the Delivery Head & Finance Manager. Press Okay to proceed.";

    this.newBillingValue = await this.getTotalBillingValueForVariation();
    let allowedDeviation = this.oldBillingValue * (this.deviationAllowedPercentage / 100);

    let lowerBound = this.oldBillingValue - allowedDeviation;
    let upperBound = this.oldBillingValue + allowedDeviation;

    if (this.newBillingValue >= lowerBound && this.newBillingValue <= upperBound) {
      isForApproval = false
    } else {
      isForApproval = true
    }

  } else if (this.approvalForRevertMilestone && approvalGroupId === 5) {
    message = this.retrieveMessages.length > 0 ?
      this.retrieveMessages[0].alert.revert_milestone_approval ||
      "An Approval request will be raised to the Finance manager. Press Okay to proceed." :
      "An Approval request will be raised to the Finance manager. Press Okay to proceed.";

    isForApproval = true;
  }  else if (this.approvalForCreditNote && approvalGroupId === 6) {
    message = this.retrieveMessages.length > 0 ?
      this.retrieveMessages[0].alert.credit_note_approval ||
      "An Approval request will be raised to the Finance manager for credit note creation. Press Okay to proceed." :
      "An Approval request will be raised to the Finance manager for credit note creation. Press Okay to proceed.";

    isForApproval = true;
  }

  if (isForApproval) {
    const result = await Swal.fire({
      text: message ? message : 'Approval Request will be sent. Press Okay to proceed',
      showCancelButton: true,
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      allowOutsideClick: false,
      allowEscapeKey: false,
      icon: 'warning'
    });

    if (result.isConfirmed) {
      return { "isForApproval": true, "navigate": true, triggerApprovalGroupId };
    } else if (result.dismiss === Swal.DismissReason.cancel) {
      return { "isForApproval": true, "navigate": false };
    }
  } else {
    return { "isForApproval": false, "navigate": true };
  }
}

async triggerApprovals(approvalGroupId, billingAdviseDetails, requestedStatus) {
  let navigate = false;
  if (approvalGroupId === 1 || approvalGroupId === 4 || approvalGroupId === 5 ) {
    const res = await this.PmBillingService.triggerApprovalForHoursDeviation(this.projectID, this.ItemID, requestedStatus, this.milestoneID, this.milestone_status_id, approvalGroupId);

    if (res['messType'] === "S") {
      this.newStatus = {
        Status: this.getNameByIdPipe.transform(17, this.milestone_status_list)
      }
      await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 17, this.milestoneID, this.data.value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44);
      this.toasterService.showSuccess(res['messText'] ? res['messText'] : 'Approval request sent successfully', 10000);
      navigate = true;
    } else if (res['messType'] === "W") {
      navigate = false;
      this.toasterService.showWarning(res['messText'], 10000);
    } else {
      navigate = false;
      this.toasterService.showError(res['messText'] ? res['messText'] : 'Error occurred while triggering approvals');
    }
  }
  if (approvalGroupId === 2) {
      this.data.code = this.code;
      const res = await this.PmBillingService.triggerApprovalForValueDeviation(this.projectID, this.ItemID, this.milestoneID,this.oldBillingValue, this.newBillingValue, this.data, approvalGroupId);
      
      if (res['messType'] === "S") {
        this.newStatus = {
          Status: this.getNameByIdPipe.transform(17, this.milestone_status_list)
        }
        await this.PmBillingService.updateMilestoneStatus(this.projectID, this.ItemID, 17, this.milestoneID, this.data.value, moment(this.data.end_date).format('MM'), moment().format('YYYY-MM-DD'), this.oldStatus, this.newStatus, 44);
        this.toasterService.showSuccess(res['messText'] ? res['messText'] : 'Approval request sent successfully', 10000);
        navigate = true;
      } else if (res['messType'] === "W") {
        navigate = false;
        this.toasterService.showWarning(res['messText'], 10000);
      } else {
        navigate = false;
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error occurred while triggering approvals');
      }
  }
  if (approvalGroupId === 3){
    const res = await this.PmBillingService.triggerApprovalForValueDeviation(this.projectID, this.ItemID, billingAdviseDetails.milestoneID,this.oldBillingValue, this.newBillingValue, billingAdviseDetails, approvalGroupId);
    if (res['messType'] === "S") {
      this.toasterService.showSuccess(res['messText'] ? res['messText'] : 'Approval request sent successfully', 10000);
      navigate = true;
    } else if (res['messType'] === "W") {
        navigate = false;
        this.toasterService.showWarning(res['messText'], 10000);
      } else {
        navigate = false;
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error occurred while triggering approvals');
      }
  }
  if( approvalGroupId === 6){
    const res = await this.PmBillingService.triggerApprovalForCreditNoteCreation(this.projectID, this.ItemID, requestedStatus, billingAdviseDetails.milestoneId, this.milestone_status_id, approvalGroupId, billingAdviseDetails);
    if (res['messType'] === "S") {
      this.toasterService.showSuccess(res['messText'] ? res['messText'] : 'Approval request sent successfully', 10000);
      navigate = true;
    } else if (res['messType'] === "W") {
      navigate = false;
      this.toasterService.showWarning(res['messText'], 10000);
    } else {
      navigate = false;
      this.toasterService.showError(res['messText'] ? res['messText'] : 'Error occurred while triggering approvals');
    }
  }
  return { "isForApproval": true, "navigate": navigate };
}

async intiateBillingAdviceAction(){
  console.log("Validate", this.billing_advice_action == "'partialInvoice'", this.billing_advice_action, "partialInvoice")
  if(this.billing_advice_action == "'cancel'")
  {
      this.enableCheckBoxClass=false;
      this.saveButtonEnabled=false;
      this.invoiceButtonEnabled=false;
      this.accruedButtonEnabled=false;
      this.partialInvoiceButtonEnabled=false;
      this.creditNoteButtonEnabled =false;
      this.editMode=false;
      this.reversalButtonEnabled =false;

  }
  else if(this.billing_advice_action == "'revert'")
  {
      this.enableCheckBoxClass=false;
      this.saveButtonEnabled=false;
      this.invoiceButtonEnabled=false;
      this.accruedButtonEnabled=false;
      this.partialInvoiceButtonEnabled=false;
      this.editMode=false;
      this.creditNoteButtonEnabled =false;
      this.cancelButtonEnabled=false;
      
  }
  else if(this.billing_advice_action == "'partialInvoice'")
  {
      let records = _.where(this.formConfig,{type:"new-billing-advice", field_name:"edit-partial-invoice", is_active: true})
      this.editMode = records.length>0 ? this.partialInvoiceButtonEnabled && true : false;
      
      this.invoiceButtonEnabled=false;
      this.cancelButtonEnabled=false;
      this.reversalButtonEnabled=false;
      this.enableCheckBoxClass=true;
      
      


  }
  else if(this.billing_advice_action == "'creditNote'")
  {

      this.editMode = this.creditNoteButtonEnabled
     
      this.invoiceButtonEnabled=false;
      this.cancelButtonEnabled=false;
      this.reversalButtonEnabled=false;

  }
  else if(this.billing_advice_action == "'work'")
  {
      this.cancelButtonEnabled=false;
      this.reversalButtonEnabled=false;
      this.partialInvoiceButtonEnabled=false;
      this.creditNoteButtonEnabled=false;
      this.enableCheckBoxClass=false;

  }
}

getInputColor(billableHours: any, actualBillableHours: any): string {
  const numericBillableHours = parseFloat(billableHours);
  const numericActualBillableHours = parseFloat(actualBillableHours);

  // Check if any value is NaN and handle it (optional, based on your use case)
  if (isNaN(numericBillableHours) || isNaN(numericActualBillableHours)) {
    return '#272A47';  
  }

  if (numericBillableHours === numericActualBillableHours) {
    return '#52C41A';  
  } else if (numericBillableHours > numericActualBillableHours) {
    return '#FF3A46'; 
  } else {
    return '#FA8C16';  
  }
}

async getTotalBillingValueForVariation() {
  return new Promise((resolve) => {
    let billing_value = 0;

    if (!this.creditNoteSelected && !this.partialInvoiceButtonEnabled) {
      for (let item of this.items) {
        billing_value += parseFloat(item['value'].toString()) || 0;
      }
    } else {
      console.log(this.items);
      let selected = _.uniq(_.pluck(this.items, "is_selected"));

      console.log("Selected", selected);

      for (let item of this.items) {
        if (selected.length > 1 && item['is_selected'])
          billing_value += parseFloat(item['value'].toString()) || 0;
        if (selected.length == 1)
          billing_value += parseFloat(item['value'].toString()) || 0;
      }
    }

    resolve(billing_value);
  });
}

async updateWorkflowRequest(approvalType){
  this.submitProgress=true;
  this.confirmDisabled=true;
  if(approvalType == 'A'){
    const res = await this.PmBillingService.updateWorkflowRequest(approvalType,this.workflowDetails?.workflow_header_id,null);
    if (res['messType'] === "S") {
      this.toasterService.showSuccess(res['messText'],10000);
      await new Promise(resolve => setTimeout(resolve, 500));
      let navigate_to = this.bill_enable ? 'bills' : 'milestone'
      let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
      this.router.navigateByUrl(navigationUrl);

    }else{
      this.toasterService.showError(res['messText']);
    }
  }else{
    const dialogRef = this.dialog.open(RejectPopupComponent, {
      data: { details: this.workflowDetails, formConfig: this.formConfig },
    });

    dialogRef.afterClosed().subscribe(async (result:any)=>{
      if(result){
        const res = await this.PmBillingService.updateWorkflowRequest(approvalType,this.workflowDetails?.workflow_header_id,result);
        if (res['messType'] === "S") {
          this.toasterService.showSuccess(res['messText'],10000);
          let navigate_to = this.bill_enable ? 'bills' : 'milestone'
          let navigationUrl = `main/project-management/${this.projectID}/${this._utilityService.encodeURIComponent(this.project_name)}/${this.ItemID}/ ${this._utilityService.encodeURIComponent(this.item_name)}/billing/${navigate_to}`;;
          this.router.navigateByUrl(navigationUrl);
        }else{
          this.toasterService.showError(res['messText']);
        }
      }
    })
  }
  this.submitProgress=false;
  this.confirmDisabled=false;
}

updateItemsForSavingBills(){
  let items_data = [];
  for(let item of this.PositionData){
    items_data.push(
      {
        "associate_id": null,
        "oid": "",
        "start_date": null,
        "end_date": null,
        "first_name": null,
        "middle_name": null,
        "last_name": null,
        "id": null,
        "entity_id": null,
        "division_id": null,
        "sub_division_id": null,
        "quote_position": item['position'] ? item['position'] : null,
        "billable": null,
        "identity": null,
        "rate_card_id": item['rate_card_id'] ? item['rate_card_id'] : null,
        "rate_revenue": null,
        "split_percentage": null,
        "name": "",
        "is_employee_level": false,
        "is_disabled": false,
        "value": item['value'] ? item['value'] : 0,
        "per_hour_rate": item['per_hour_rate'] ? item['per_hour_rate'] : 0,
        "quote_position_location": null,
        "is_overlapped": 0,
        "timesheet_hours": 0,
        "logged_hours": 0,
        "billable_hours": 0,
        "non_billable_hours": 0,
        "non_billable_hours_flexi": 0,
        "non_billable_hours_growth": 0,
        "previous_timesheet_hours": 0,
        "previous_billable_hours": 0,
        "previous_non_billable_hours": 0,
        "previous_non_billable_hours_flexi": 0,
        "previous_non_billable_hours_growth": 0,
        "planned_hours": item['planned_hours'] ? item['planned_hours'] : 0,
        "rounded_hours": 0,
        "billable_hours_milestone": 0,
        "rounded_hours_milestone": 0,
        "total_hours": 0,
        "actual_billable_hours": item['actual_billable_hours'] ? item['actual_billable_hours'] : 0,
        "actual_billable_days": item['actual_billable_days'] ? item['actual_billable_days'] : 0,
        "temp": 0,
        "billable_days": 0,
        "modified_rounded_hours": 0,
        "modified_billable_hours": 0,
        "modified_new_rounded_hours": 0,
        "modified_new_billable_hours": 0,
        "zifo_timesheet_hours": 0,
        "zifo_previous_timesheet_hours": 0,
        "after_zifo_timesheet_hours": 0,
        "after_rounded_hours": 0,
        "has_child": true,
        "expandEnable": false,
        "isVisible": true
    },
    )
  }
  this.items = items_data;
}


async checkForCommentsMandatory(){


    console.log("Comment Position data", this.PositionData, this.editMode)
    if(this.editMode)
    {
      let checkForComment = true;
      for(let item of this.PositionData)
      {

        if(!item.has_child)
        {
          const billableHours: any = item.billable_hours;
          const actualBillableHours: any = item.actual_billable_hours;


          console.log(item, billableHours, actualBillableHours)


          if (parseFloat(billableHours) !== parseFloat(actualBillableHours)) 
          {
              if(item.comments == "null" || item.comments == "" || item.comments ==null)
              {
                checkForComment=false;
                break;
              }
          }
        }

      }

      return checkForComment;
    }
    else
    {
      return true;
    }
  }

  intializeBillingPlan(){

  }

  getFutureEffortRequired(value, index)
  {
    value = value!="" ? value : 0
    
    
    if(!isNaN(value))
    {
      let rate_card_id = this.PositionData[index]['id']

      for(let item of this.PositionData)
      {
          
          if(item['id']==rate_card_id)
          {
              item['future_effort_required'] = parseFloat(value);

                //Step 1: Sum Actual till Date
                item['display_actual_till_date'] =  item['display_actual_till_previous_date'] + item['actual_billable_hours']

 
                //Step 2: Sum Quote Position hours with Actual Hours
                item['display_total_quote_position_hours'] = item['display_actual_till_date'] + item['future_effort_required'] + item['future_planned']
  
                //Step 3 : Calculate Per Hour Rate
                item['revised_per_hour_rate'] = item['display_total_quote_position_hours'] ?  item['display_total_quote_position_hours']!=0 ? (item['total_quote_position_value']/parseFloat(item['display_total_quote_position_hours'])) : item['per_hour_rate'] : item['per_hour_rate']
                
                //Step 4: Future Revenue
                item['future_revenue'] = ((item['future_effort_required']  ? item['future_effort_required']  : 0) + item['future_planned'])  * item['revised_per_hour_rate']
  
                //Step 5: Calculate Revenue Till Date
                item['display_revenue_till_date'] = item['display_actual_till_date'] * item['revised_per_hour_rate']
  
                //Step 6: Calcule Total Revneue
                item['display_total_revenue'] = item['display_revenue_till_date'] + item['future_revenue']
               
                
                item['current_month_revenue'] = item['display_revenue_till_date'] - item['revenue_till_previous_month']
 
                
 
                item['current_month_per_hour_rate'] =item['actual_billable_hours']!=0? (item['current_month_revenue'] / item['actual_billable_hours']) :item['revised_per_hour_rate']
 
                item['current_month_progress'] = (item['current_month_revenue']/item['total_quote_position_value']) * 100
 
                console.log("Current Month Revenue", item['current_month_revenue'], "Quote Position Value", item['total_quote_position_value'], "Revenue Till Date", item['display_revenue_till_date'], "Current Month Per Hour Rate", item['current_month_per_hour_rate'], item['actual_billable_hours'])
 
                item['cummulative_month_progress'] = (item['display_revenue_till_date']/item['total_quote_position_value']) * 100
 
               if(item['actual_billable_hours']!=undefined && item['revised_per_hour_rate']!=undefined)
               {
                 item['value']=item['actual_billable_hours']*item['revised_per_hour_rate']
 
                 item['per_hour_rate'] = item['revised_per_hour_rate']
 
                 item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
               }
          
          }
      }
    }

    console.log(index, this.PositionData)

  }

  validateForecastedData(){


  }

  getCurrentMonthProgress() {
    const positionList = this.PositionData.filter(position => position.has_child);
    const totalCurrentMonthRevenue = positionList.reduce((sum, position) => sum + position.current_month_revenue, 0);
    const quote = this.projectQuoteList.find(quote => quote.quote_id === this.quote_id);

    if (quote) {
      const purchaseOrder = quote.purchase_order;
      return purchaseOrder ? this.safeFixed((totalCurrentMonthRevenue / purchaseOrder) * 100) : 0;
    } else {
      return 0;
    }
  }

  

  getCummulativeMonthProgress() {
    const positionList = _.where(this.PositionData, { has_child: true });
    const totalRevenueTillDate = positionList.reduce((sum, position) => sum + position['display_revenue_till_date'], 0);

    const quoteList = _.where(this.projectQuoteList, { quote_id: this.quote_id });

    if (quoteList.length > 0) {
        const purchaseOrder = quoteList[0]['purchase_order'];
        return purchaseOrder != null && purchaseOrder != 0 ? this.safeFixed((totalRevenueTillDate / purchaseOrder) * 100) : 0;
    } else {
        return 0;
    }
}

  updateIntialBillingAdviceValue(){
    console.log("PositionData", this.PositionData)
    for(let item of this.PositionData)
      {
        if(item['has_child'])
        {

          item['actual_billable_hours'] = item.is_employee_level ? this.getSumActualBillableHoursForPosition(this.items, item['id']) : this.safeFixed(item['actual_billable_hours'])

          console.log("Actual Billable Hours", item['actual_billable_hours'])

           //Step 1: Sum Actual till Date
           item['display_actual_till_date'] =  item['display_actual_till_previous_date'] + item['actual_billable_hours']

           //Step 2: Update Future Effort Required = Current Billing Plan Hours - Actual Billable Hours
           item['future_effort_required'] = item['future_effort_required'] ? item['future_effort_required']:  this.safeFixed(item['current_planned'] - item['actual_billable_hours'])

           //Step 2: Sum Quote Position hours with Actual Hours
           item['display_total_quote_position_hours'] = item['display_actual_till_date'] + item['future_effort_required'] + item['future_planned']

           //Step 3 : Calculate Per Hour Rate
           item['revised_per_hour_rate'] = item['display_total_quote_position_hours'] ?  item['display_total_quote_position_hours']!=0 ? (item['total_quote_position_value']/parseFloat(item['display_total_quote_position_hours'])) : item['per_hour_rate'] : item['per_hour_rate']
           
           //Step 4: Future Revenue
           item['future_revenue'] = ((item['future_effort_required']  ? item['future_effort_required']  : 0) + item['future_planned'])  * item['revised_per_hour_rate']

           //Step 5: Calculate Revenue Till Date
           item['display_revenue_till_date'] = item['display_actual_till_date'] * item['revised_per_hour_rate']

           //Step 6: Calcule Total Revneue
           item['display_total_revenue'] = item['display_revenue_till_date'] + item['future_revenue']
          
           
           item['current_month_revenue'] = item['display_revenue_till_date'] - item['revenue_till_previous_month']

           

           item['current_month_per_hour_rate'] =item['actual_billable_hours']!=0? item['current_month_revenue'] / item['actual_billable_hours']:item['revised_per_hour_rate']

           item['current_month_progress'] = (item['current_month_revenue']/item['total_quote_position_value']) * 100

           console.log("Current Month Revenue", item['current_month_revenue'], "Quote Position Value", item['total_quote_position_value'], "Revenue Till Date", item['display_revenue_till_date'])

           item['cummulative_month_progress'] = (item['display_revenue_till_date']/item['total_quote_position_value']) * 100

          if(item['actual_billable_hours']!=undefined && item['revised_per_hour_rate']!=undefined)
          {
            item['value']=item['actual_billable_hours']*item['revised_per_hour_rate']

            item['per_hour_rate'] = item['revised_per_hour_rate']

            item['actual_billable_days']=item['actual_billable_hours']/this.project_daily_working_hours
          }
        }

        console.log("Position Data", item)
        
      }
  }


  displayForecast(){
    console.log("Check HERE")
    this.isComponentLoading = !this.isComponentLoading
  }


  safeFixed(value) {
    let num = Number(value);
    return (!isFinite(num) || isNaN(num)) ? 0.00 : parseFloat(num.toFixed(2));
  }


}
       
    
  
  
  export interface Item {
    associate_id: number;
    name: string;
    start_date: Date;
    end_date:Date;
    timesheet_hours:number;
    planned_hours:number;
    logged_hours:number;
    billable_hours:number;
    non_billable_hours:number;
    total_hours:number;
    first_name:string;
    last_name:string;
    middle_name:string;
    oid:string;
    per_hour_rate:number;
    value:number
    actual_billable_hours:number
    previous_non_billable_hours:number
    previous_billable_hours:number
    previous_timesheet_hours:number
    entity_id:number
    division_id:number
    sub_division_id:number
    id:number
    rate_card_id:number
    has_child:boolean
    expandEnable:boolean
    isVisible:boolean
    actual_billable_days:number
    zifo_previous_timesheet_hours:number
    zifo_timesheet_hours:number
    after_zifo_timesheet_hours: number
    after_rounded_hours: number
    is_employee_level: boolean
    is_disabled: boolean
  }

  export interface Invoice {
    description: string;
    rate: number;
    quantity:number;
    unit:number;
    amount:number;
  }
  

