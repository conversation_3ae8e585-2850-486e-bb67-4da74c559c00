require('./secretsProcessor').setSecrets().then(()=>{
  require('./logger').intialize()
  const express = require("express");
const fs=require('fs');
require('./tracing.js')
const morgan=require('morgan');
var compression = require("compression");
const path = require("path");
const mongo_conn_native = require("./mongo_conn_native").Connection;
const passport = require("passport");
const ENV_FILE = path.join(__dirname, ".env");
require("dotenv").config({ path: ENV_FILE });
// create express app
const app = express();
let Sentry = require("@sentry/node")
// const { ProfilingIntegration } = require("@sentry/profiling-node");

//Sentry

if (process.env.NODE_ENV == "prd" || process.env.NODE_ENV == "qual") {
  Sentry.init({
    dsn: "https://<EMAIL>/4505225218621440",
    integrations: [
      // enable HTTP calls tracing
      new Sentry.Integrations.Http({ tracing: true }),
      // enable Express.js middleware tracing
      new Sentry.Integrations.Express({
        // to trace all requests to the default router
        app,
        // alternatively, you can specify the routes you want to trace:
        // router: someRouter,
      }),
      // new ProfilingIntegration()
    ],

    // We recommend adjusting this value in production, or using tracesSampler
    // for finer control
    tracesSampleRate: 1.0,
    // profilesSampleRate: 1.0
  });

  // RequestHandler creates a separate execution context, so that all
  // transactions/spans/breadcrumbs are isolated across requests
  app.use(Sentry.Handlers.requestHandler());
  // TracingHandler creates a trace for every incoming request
  app.use(Sentry.Handlers.tracingHandler());

  // the rest of your app

  app.use(Sentry.Handlers.errorHandler());
}

//Sentry End

// parse requests of content-type - application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: false }));

app.use(express.json({ limit: "500mb", extended: true }));

// parse requests of content-type - application/json
app.use(express.json());

//for compression
app.use(compression());
//morgan
if(process.env.NODE_ENV !='default')
{
  let accessLogStream = fs.createWriteStream(path.join(__dirname , '/morganlogs/inlogs.log'));
  let accessLogStream1=fs.createWriteStream(path.join(__dirname , '/morganlogs/outlogs.log'));
app.use(morgan('combined', {immediate:true ,stream: accessLogStream}));
app.use(morgan('combined',{stream:accessLogStream1}))
}
// passport configuration
require("./passport-config")(passport);
app.use(passport.initialize());
app.use(passport.session());

let healthcheckRouter = require("./healthcheck")
app.use("/queue/health", healthcheckRouter)


// listen for requests
app.listen(3900, async () => {
  console.log("Server is listening on port 3900");
  let connection = await mongo_conn_native.connectToMongo().then(()=>{
    process.send("ready")
  });
  const queue_route = require("./routes/queue-route");
  app.use("/queue", queue_route);
  app.use("/api/rabbitmq/queue",queue_route);
  require('./worker_dlq')
});
 //-------------------------->prometheus new<---------------------

//prometheus start
const prometheus = require('prom-client');
prometheus.collectDefaultMetrics()
const gcStats = require('prometheus-gc-stats'); //for gc-stats 
const startGcStats = gcStats(prometheus.register);//for gc-stats 
startGcStats();//for gc-stats 
// prometheus.register.clear()
// const apiMetrics = require('prometheus-api-metrics');
// app.use(apiMetrics())
// const responseTime = require('response-time')
// const restResponseTime = new prometheus.Histogram({name:'rabbitmq_rest_response_time',help:'REST API Response Time in Seconds',labelNames:['method','route','status_code']})
const errorCounter = new prometheus.Counter({ name: 'rabbitmq_error_count', help: 'Total Error Count',labelNames: ['method', 'api']  });
const counter = new prometheus.Counter({
  name: 'rabbitmq_requests_total',
  help: 'Total number of requests',
  labelNames: ['method', 'api'],
});
// app.use(responseTime((req,res,time)=>{console.log(req.route.path,' ',time*1000)})) for checking the response-time
// app.use(responseTime((req,res,time)=>{
//   if(req.route.path == '/metrics')
//   {
//     console.log('metrics scrapped')
//   }
//   else if(req?.route?.path)
//   {
//     restResponseTime.observe(
//       {
//         method: req.method,
//         route: req.route.path,
//         status_code: res.statusCode
//       }, time*1000
//     )
//   }
// }))
// Increment the counter on each request
app.use((req, res, next) => {
  counter.inc({ method: req.method ,api: req.path});
  next();
});
app.on('error', (err) => {
  // Increment error counter
  errorCounter.inc({ method: req.method ,api: req.path});
});

// Expose the metrics endpoint
app.get('/metrics', (req, res) => {
  prometheus.register.metrics().then((metrics) => {
    res.set('Content-Type', prometheus.register.contentType);
    res.end(metrics);
  }).catch((error) => {
    console.error('Error generating metrics:', error);
    res.status(500).send('Error generating metrics');
  });
});
//prometheus end
// ---------------------------prometheus new-------------------------------------



})
