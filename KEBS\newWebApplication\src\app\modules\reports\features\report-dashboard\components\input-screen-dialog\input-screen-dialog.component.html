<div class="input-screen">
    <div class="row d-flex justify-content-end">
        <div *ngIf="dialogData['buttonEnable']">
          <mat-icon class="close-button" (click)="onCloseClick()">clear</mat-icon>
        </div>
    </div>
    <form [formGroup]="inputScreenForm">
        <div class="form-class">
            <div class="field-class">
                <span>Region <span class="required-star"> &nbsp;*</span> </span>
                <app-input-search-name class="inputSearch" placeholder="Select Region" [list]="regionListMaster"
                formControlName="region" [disableNone]="true" [hideMatLabel]="true">
                </app-input-search-name>
            </div>
            <div class="field-class">
                <span>Business Unit</span>
                <app-input-search-name class="inputSearch" placeholder="Select Business Unit" [list]="divisionListMaster"
                    formControlName="division" [disableNone]="true" [hideMatLabel]="true">
                </app-input-search-name>
            </div>
            <div class="field-class">
                <span>Department</span>
                <app-input-search-name class="inputSearch" placeholder="Select Department Head" [list]="subdivisionListMaster"
                    formControlName="sub_division" [disableNone]="true" [hideMatLabel]="true">
                </app-input-search-name>
            </div>
        </div>
        <div class="button-class" (click)="apply()">
            <span class="btn-txt">Apply</span>
        </div>
    </form>
</div>