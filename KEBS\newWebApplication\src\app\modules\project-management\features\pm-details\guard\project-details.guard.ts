import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { PmAuthorizationService } from '../../../services/pm-authorization.service';
import * as _ from 'underscore';

@Injectable({
  providedIn: 'root'
})
export class ProjectDetailsGuard implements CanActivate {

  itemId: any;
  projectId: any;
  projectName: any;
  itemName: any;
  constructor(private _router: Router,
    private toaster: ToasterService,
    private authService: PmAuthorizationService,
    private router: Router,) { }


  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

    let url = state['url'];

    let hasAccess: any = await this.canAccess(url, route.data.objectId)

    if (hasAccess == false) {
      console.log("project Application Tab access")
      this.toaster.showError("You not having access to this Tab!", "Access Restricted", 10000)
      this._router.navigateByUrl("/main/project-management/"+this.projectId+"/"+this.projectName+"/"+this.itemId+"/"+this.itemName+"/overview");
    };

    return hasAccess
  }

  async canAccess(url, objectId) {


    return new Promise(async (resolve, reject) => {
      try {

        await this.authService.getUserRoleAccessProjectList().then(async(res) => {
          console.log(url, url.split("/"))
          this.itemId = parseInt(url.split("/")[5])
          this.projectId = parseInt(url.split("/")[3])
          this.projectName = url.split("/")[4]
          this.itemName = url.split("/")[6]
          console.log(this.itemId, this.projectId)

          resolve(await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, objectId));

        }, (err) => {
          resolve(false);
        })

      }
      catch (err) {
        resolve(false);
      }
    })

  }




}




