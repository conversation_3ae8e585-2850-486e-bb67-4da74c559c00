import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from "rxjs";
import { MatSnackBar } from "@angular/material/snack-bar";
import * as _ from 'underscore';
import * as moment from "moment";
import swal from 'sweetalert2/dist/sweetalert2.js';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})

export class UtilityService {

  getAppConfigs() {
    return this._http.post('/api/master/getAppConfigs', {});
  }

  dfDDMMMYYYY = "DD-MMM-YY";

  lmhv_status_colors = [
    {
      statusName: "Low",
      statusColor: "#c7c4c4",
    },
    {
      statusName: "Medium",
      statusColor: "#ff7200",
    },
    {
      statusName: "High",
      statusColor: "#1AD840",
    },
    {
      statusName: "Very High",
      statusColor: "#009432",
    }
  ];

  activityStatusColor_inlineEdit = [
    {
      statusName: "Open",
      statusColor: '#c7c4c4'
    },
    {
      statusName: "In Progress",
      statusColor: '#ff7200'
    },
    {
      statusName: "Completed",
      statusColor: '#009432'
    }
  ];

  projectMilestoneStatusColor_inlineEdit = [
    {
      statusName: "Open",
      statusColor: '#c7c4c4'
    },
    {
      statusName: "Execution",
      statusColor: '#ff7200'
    },
    {
      statusName: 'YTB',
      statusColor: '#648588',
    },
    {
      statusName: "Cancelled",
      statusColor: "#c7c4c4"
    },
  ];

  contractStatusColor_inlineEdit = [
    {
      statusName: "Renewed",
      statusColor: '#009432',
      status: "Renewed",
      color: "#009432"
    },
    {
      statusName: "Terminated",
      statusColor: '#c7c4c4',
      status: "Terminated",
      color: "#c7c4c4"
    },
    {
      statusName: 'Renewal in progress',
      statusColor: '#ff7200',
      status: "Renewal in progress",
      color: "#ff7200"
    },
  ];

  projectActivityStatusColor_inlineEdit = [
    {
      statusName: "Open",
      statusColor: '#c7c4c4'
    },
    {
      statusName: "Execution",
      statusColor: '#ff7200'
    },
    {
      statusName: "Completed",
      statusColor: '#009432'
    },
    {
      statusName: 'Assigned',
      statusColor: '#9980fa',
    },
    {
      statusName: 'In Progress',
      statusColor: '#ff7200',
    },
  ];

  lms_cert_status_inlineEdit = [
    {
      statusName: "Completed",
      statusColor: '#009432'
    },
    {
      statusName: 'Assigned',
      statusColor: '#9980fa',
    },
    {
      statusName: 'In Progress',
      statusColor: '#ff7200',
    },
  ];

  cm_task_gov_status_inlineEdit = [
    {
      statusName: "Completed",
      statusColor: '#4caf50'
    },
    {
      statusName: 'Open',
      statusColor: '#928F8D',
    },
    {
      statusName: 'In Progress',
      statusColor: '#ff7200',
    }
  ]
  // activityStatusColor_inlineEdit: any;

  okrStatuscolor_inlineEdit = [
    {
      statusName: 'Open',
      statusColor: '#ACA9A6',
    },
    {
      statusName: 'Not Started',
      statusColor: '#ACA9A6',
    },
    {
      statusName: 'In progress',
      statusColor: '#7dd259',
    },
    {
      statusName: 'Missed',
      statusColor: '#fe0000',
    },
    {
      statusName: 'Postponed',
      statusColor: '#febd00',
    },
    {
      statusName: 'Completed',
      statusColor: '#0077b8',
    },
    {
      statusName: 'On track',
      statusColor: '#7dd259',
    },
    {
      statusName: 'At risk',
      statusColor: '#fe0000',
    },
    {
      statusName: 'Delayed',
      statusColor: '#febd00',
    },
    {
      statusName: 'On Hold',
      statusColor: '#9370DB',
    },
    {
      statusName: 'Approved',
      statusColor: '#009432',
    },
    {
      statusName: 'Pending for approval',
      statusColor: '#ff7200',
    },
    {
      statusName: 'Rejected',
      statusColor: '#cf0001',
    }
  ];

  okrStatuscolor_inlineEdit_milestone = [
    {
      statusName: 'Not Started',
      statusColor: '#ACA9A6',
    },
    {
      statusName: 'Completed',
      statusColor: '#0077b8',
    },
    {
      statusName: 'On track',
      statusColor: '#7dd259',
    },
    {
      statusName: 'Cancelled',
      statusColor: '#ACA9A6',
    },
    {
      statusName: 'Deferred',
      statusColor: '#ACA9A6',
    }
  ];

  okrStatuscolor_inlineEdit_meeting = [
    {
      statusName: 'Not Started',
      statusColor: '#ACA9A6',
    },
    {
      statusName: 'In progress',
      statusColor: '#7dd259',
    },
    {
      statusName: 'Missed',
      statusColor: '#fe0000',
    },
    {
      statusName: 'Completed',
      statusColor: '#0077b8',
    }
  ];

  okrReportStatusColor_inlineEdit: any[] = [
    {
      statusColor: "#ACA9A6",
      statusName: "Not Started",
    }, {
      statusColor: "#7dd259",
      statusName: "On track",
    }, {

      statusColor: "#fe0000",
      statusName: "At risk",
    }, {

      statusColor: "#0077b8",
      statusName: "Completed",
    }, {

      statusColor: "#febd00",
      statusName: "Delayed",
    },
    {
      statusName: "In progress",
      statusColor: "orange"
    },
    {
      statusName: "Missed",
      statusColor: "brown"
    },
    {
      statusName: "Postponed",
      statusColor: "#febd00"
    },
    {
      statusName: 'On Hold',
      statusColor: '#9370DB',
    },
  ];

  complianceStatusColor_inlineEdit: any[] = [
    {
      statusName: "Open",
      statusColor: "#d3d3d3"
    },
    {
      statusName: "On Process",
      statusColor: "#FFA500"
    },
    {
      statusName: "Close",
      statusColor: "#00D100"
    },
    {
      statusName: "Hold",
      statusColor: "#FF0000"
    },
    {
      statusName: "Open",
      statusColor: "#d3d3d3"
    },
    {
      statusName: "On Process",
      statusColor: "#FFA500"
    },
    {
      statusName: "Request for Closure",
      statusColor: "#8A2BE2"
    },
    {
      statusName: "Approved by Manager",
      statusColor: "#0000FF"
    },
    {
      statusName: "Request for Department Approval",
      statusColor: "#90EE90"
    },
    {
      statusName: "Approved by Department",
      statusColor: "#87ceeb"
    },
    {
      statusName: "Close",
      statusColor: "#00D100"
    },
    {
      statusName: "Hold",
      statusColor: "#FF0000"
    }
  ];

  LeadSalesStatus_InlineEdit = [
    {
      statusName: 'Prospecting',
      statusColor: '#ffb142',
    },
    {
      statusName: 'Moved To Opportunity',
      statusColor: '#009432',
    },
    {
      statusName: 'Lead Nurturing',
      statusColor: '#9980fa',
    },
    {
      statusName: 'Dropped',
      statusColor: '#c0392b'
    },
    {
      statusName: 'Budgetary Submitted',
      statusColor: '#e948db',
    },

  ];

  contactPersona_InlineEdit = [
    {
      statusName: 'IT Team',
      statusColor: '#f7a027',
    },
    {
      statusName: 'HR Team',
      statusColor: '#009432',
    },
    {
      statusName: 'Procurement Team',
      statusColor: '#3b16cc',
    },
    {
      statusName: 'Finance Team',
      statusColor: '#c0392b'
    },
    {
      statusName: 'Management Team',
      statusColor: '#991ee0',
    },

  ];

  p2pVendorInvoice_inlineEdit = [
    {
      statusName: 'Open',
      statusColor: '#c7c4c4'
    },
    {
      statusName: 'Submitted',
      statusColor: '#ff7200'
    },
    {
      statusName: 'Approved',
      statusColor: '#87ceeb'
    },
    {
      statusName: 'Verified',
      statusColor: '#0077b8'
    },
    {
      statusName: 'Partially paid',
      statusColor: '#00D100'
    },
    {
      statusName: 'Rejected',
      statusColor: '#cf0001'
    },
    {
      statusName: 'Paid',
      statusColor: '#009432'
    },
    {
      statusName: 'In Progress',
      statusColor: '#ff7200'
    },
    {
      statusName: 'Completed',
      statusColor: '#4caf50'
    },
    {
      statusName: 'Advance',
      statusColor: '#955c6e'
    }, 
    {
      statusName: 'Cancelled',
      statusColor: '#ff7200'
    },
    {
      statusName: 'Draft',
      statusColor: '#9980fa'
    }

  ]

  rmRequestStatus = [
    {
      statusName: 'Pending',
      statusColor: '#FFBD3D'
    },
    {
      statusName: 'Closed',
      statusColor: '#52C41A'
    },
    {
      statusName: 'In-Progress',
      statusColor: '#FADB14'
    },
    {
      statusName: 'On-Hold',
      statusColor: '#45546E'
    },
    {
      statusName: 'Cancelled',
      statusColor: '#FF3A46'
    },
    {
      statusName: 'Allocated',
      statusColor: '#A0D911'
    },
    {
      statusName: 'Assigned',
      statusColor: '#009432'
    }
  ]

  rmProjectAvailableStatus : any = [];


  dateRangePickerRanges = {
    // 'Today': [moment(), moment()],
    // 'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
    'This Month': [moment().startOf('month'), moment().endOf('month')],
    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
    'Upcoming 3 Months': [moment().startOf('month'), moment().add(2, 'month').endOf('month')],
    'This Year': [moment().startOf('year'), moment().endOf('year')],
    'Previous Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, "year").endOf('year')]
  };

  isFullScreenApp = new BehaviorSubject(false);
  public getIsFullScreenAppValue = this.isFullScreenApp.asObservable();

  constructor(private $snackBar: MatSnackBar, private _http: HttpClient) { }

  addIsFullScreenAppValue(isFullScreenAppValue) {
    this.isFullScreenApp.next(isFullScreenAppValue);
  }

  getFileSize(bytes) {
    let thresh = 1000;
    if (Math.abs(bytes) < thresh) {
      return bytes + " B";
    }
    let units = ["kB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    let u = -1;
    do {
      bytes /= thresh;
      ++u;
    } while (Math.abs(bytes) >= thresh && u < units.length - 1);
    return bytes.toFixed(1) + " " + units[u];
  }




  showMessage(message, action, duration = 4500) {
    return this.$snackBar.open(message, action, {
      duration: duration
    });
  }

  convert(val) {
    if (!val) return "-";
    else val = (val / 10000000).toFixed(2) + " Cr";
    return val;
  }

  public convertToLocalTime(time) {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return localTime;
  }

  public openConfirmationSweetAlert = () => {

    return new Promise((resolve, reject) => {

      let template = {
        customClass: {
          title: "title-class",
          confirmButton: "confirm-button-class",
          cancelButton: "confirm-button-class",
        },
        title: "Are you sure?",
        text: "You won't be able to revert!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes",
      }
      swal.fire(
        template
      ).then((result) => {
        // console.log(result);
        resolve(result.value);
      });
    });

  };


  public showSweetalert = (title, type) => {
    swal.fire(
      '',
      title,
      type
    )
  };

  convertToLocalTimezone(time) {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return moment(localTime).format("DD-MM-YY");
    //return localTime;
  }

  getFormattedDate(monthYearDate, date, hour, minute, second, millisecond) {

    return monthYearDate.date(date).hour(hour).minute(minute).second(second).millisecond(millisecond);

  }

  showToastMessage(message) {

    this.$snackBar.open(message, "Dismiss", {
      duration: 5000
    });

  }

  getDisplayValueRoundOff(displayValue, displayValueOriginal) {

    return displayValueOriginal != 0 ? (displayValue != 0 ? displayValue : "0.00") : "-";

  }

  showErrorMessage(err, errReportingTeams) {

    // this.spinner.hide();

    let errorMessage = "Error: ";

    if (err && err.messType && err.messText && err.messText.code)
      errorMessage += err.messText.code;

    else if (err && err.messType && err.messText)
      errorMessage += err.messText;

    else if (err && err.messType && err.messText && err.messText.stack)
      errorMessage += err.messText.stack;

    else if (err && err.messType && err.messText && err.messText.message)
      errorMessage += err.messText.message;

    else if (err && err.error && err.error.messText && err.error.messText.code)
      errorMessage += err.error.messText.code;

    else if (err && err.error && err.error.messType && err.error.messText)
      errorMessage += err.error.messText;

    else if (err && err.error && !err.error.messText && err.message)
      errorMessage += err.message;

    else if (err && !err.error && err.message)
      errorMessage += err.message;

    else
      errorMessage += "Unknown error";

    errorMessage += ". Kindly report it to the ";

    errorMessage += errReportingTeams;

    errorMessage += " Team to get it resolved!";

    this.showToastMessage(errorMessage);

  }

  encodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
    });
  }

  getDisplayValue(displayValue, displayValueOriginal) {

    return displayValueOriginal != 0 ? (displayValue != 0 ? displayValue : "0.00") : "-";

  }

  getDisplayValueInInrFormat(displayValue) {

    var n1, n2;

    displayValue = displayValue + '' || '';

    n1 = displayValue.split('.');
    n2 = n1[1] || null;
    n1 = n1[0].replace(/(\d)(?=(\d\d)+\d$)/g, "$1,");

    displayValue = n2 ? n1 + '.' + n2 : n1;

    return displayValue;

  }

  roundToZero(value) {
    return Number(value).toFixed(0);
  }

  roundToTwo(value) {
    return Number(value).toFixed(2);
  }

  convertToCr(value) {
    return this.roundToTwo(value * 10000000);
  }

  convertFromCr(value) {
    return this.roundToTwo(value / 10000000);
  }

  convertToMillion(value) {
    return this.roundToTwo(value * 1000000);
  }

  convertFromMillion(value) {
    return this.roundToTwo(value / 1000000);
  }

  convertFromLakh(value) {
    return this.roundToTwo(value / 100);
  }

  convertToNormalFromLakh(value) {
    return this.roundToTwo(value * 100000);
  }

  getMaxDate(dates, format) {

    let maxDate = this.getMaxValue(dates);

    return maxDate == "-" ? maxDate : this.getDDMMMYYYDate(maxDate, format);

  }

  getSortedArray(unsortedArray, sortField, sortBy) {

    if (unsortedArray.length > 0) {

      if (this.identifyColumnDataType(unsortedArray, "number", sortField))
        return unsortedArray.sort(function (a, b) {

          if (a[sortField] == null)
            a[sortField] = 0;

          if (b[sortField] == null)
            b[sortField] = 0;

          if (a[sortField] > b[sortField])
            return sortBy == "D" ? -1 : 1;
          else if (a[sortField] < b[sortField])
            return sortBy == "D" ? 1 : -1;
          else
            return 0;

        });

      else if (this.identifyColumnDataType(unsortedArray, "date", sortField))
        return unsortedArray.sort(function (a, b) {

          if (a[sortField] == null)
            a[sortField] = "";

          if (b[sortField] == null)
            b[sortField] = "";

          if (moment(a[sortField]).isAfter(b[sortField].toLowerCase()))
            return sortBy == "D" ? -1 : 1;
          else if (moment(a[sortField]).isBefore(b[sortField].toLowerCase()))
            return sortBy == "D" ? 1 : -1;
          else
            return 0;

        });

      else
        return unsortedArray.sort((a, b) => {

          if (a[sortField] == null)
            a[sortField] = "";

          if (b[sortField] == null)
            b[sortField] = "";

          if (a[sortField].toLowerCase() > b[sortField].toLowerCase())
            return sortBy == "D" ? -1 : 1;
          else if (a[sortField].toLowerCase() < b[sortField].toLowerCase())
            return sortBy == "D" ? 1 : -1;
          else
            return 0;

        });

    }

    else
      return [];

  }

  identifyColumnDataType(array, fieldType, sortField) {

    let isFieldType = false;

    for (let i = 0; i < array.length && isFieldType == false; i++)
      if (fieldType == "number" && array[i][sortField] != null && this.isNumber(array[i][sortField]))
        isFieldType = true;
      else if (fieldType == "date" && array[i][sortField] != null && (moment(array[i][sortField], true).isValid() || sortField.includes("date")))
        isFieldType = true;

    return isFieldType;

  }

  isNumber(value) {

    if (!isNaN(value)) {

      if (typeof value === "string" && value == "")
        return false;

      else
        return true;

    }

    else if (value.match(/^-?\d+$/) || value.match(/^\d+\.\d+$/))
      return true;

    else
      return false;

  }

  padDigits(number, digits) {
    return Array(Math.max(digits - String(number).length + 1, 0)).join("0") + number;
  }

  getMaxValue(values) {

    if (values.length > 0) {

      values = values.sort((a, b) => {
        if (a > b)
          return -1;
        else if (a < b)
          return 1;
        else
          return 0;
      });

      return values[0];

    }

    else
      return "-";

  }

  public openConfirmationSweetAlertWithCustom(title, text) {

    return new Promise((resolve, reject) => {

      swal.fire({

        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, go ahead',
        cancelButtonText: 'No, let me think'

      }).then((result) => {

        resolve(result.value);

      });

    });

  }

  public openConfirmationForUpdatingAllOpportunityActivities(title) {

    return new Promise((resolve, reject) => {

      swal.fire({

        title: title,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes,Update For All',
        cancelButtonText: 'Update For This Activity Only'

      }).then((result) => {
        // console.log(result)
        if (result.value)
          resolve('All');
        else
          resolve('Single')
      });

    });

  }

  getDDMMMYYYDate(dateValue, format) {

    if (dateValue)
      return moment(dateValue).format(format);

    else
      return "-";

  }

  private msgSub = new Subject<any>();

  sendMsg(msg) {
    this.msgSub.next(msg)
  }

  getMsg() {
    return this.msgSub.asObservable();
  }

  pivotGridSortDate(date, dateFormat, endYear, fyStartMonth) {

    let currentYear = moment(date).year();

    let spaceCount = 0;

    while (currentYear < endYear) {

      spaceCount += 25;
      currentYear++;

    }

    let formattedDate = moment(date).format(dateFormat);
    let dateMonth = parseInt(moment(date).format("MM"));
    let getSpaceCountFTM = this.fyMonths(fyStartMonth, spaceCount, dateMonth);
    spaceCount = getSpaceCountFTM;

    let dateString = "";

    while (spaceCount > 0) {

      dateString += " ";
      spaceCount--;

    }

    dateString += formattedDate;

    return dateString;

  }

  fyMonths(fy_start_month, initialSpaceCount, dateMonth) {

    let total_months_in_a_year = 12;
    let counter = initialSpaceCount + (total_months_in_a_year - 1);
    // let months = [];
    let spacing_count;

    for (let i = 0; i < total_months_in_a_year; i++) {

      let month = fy_start_month + i;

      //To reduce the month when it crosses 12 
      if (month > 12)
        month = month - 12;

      //To get Spacing Count of the Period-Month
      if (dateMonth == month)
        spacing_count = counter;

      // months.push(
      //     {
      //         month: month,
      //         spaceCount: counter
      //     }
      // );
      counter--;
    }

    return spacing_count;

  }

  getDisplayValueInUSDFormat(displayValue) {

    var n1, n2;

    displayValue = displayValue + '' || '';

    n1 = displayValue.split('.');
    n2 = n1[1] || null;
    n1 = n1[0].replace(/(\d)(?=(\d\d\d)+$)/g, "$1,");

    displayValue = n2 ? n1 + '.' + n2 : n1;

    return displayValue;

  }

  getValueWithComma(isFullValue, value, currency, appName, misFlags) {

    let returnVal;

    if (currency == "INR") {

      returnVal = value / misFlags["divByDuringRetrievalForInr" + appName];

      if (isFullValue)
        returnVal = misFlags["valueWithComma"] ? Number(Number(returnVal).toFixed(0)).toLocaleString('en-IN') : returnVal.toFixed(0);

      else
        returnVal = returnVal.toFixed(2);

    }

    else if (currency == "USD") {

      returnVal = value / misFlags["divByDuringRetrievalForUsd" + appName];

      if (isFullValue)
        returnVal = misFlags["valueWithComma"] ? Number(Number(returnVal).toFixed(0)).toLocaleString('en-GB') : returnVal.toFixed(0);

      else
        returnVal = returnVal.toFixed(2);

    }

    returnVal = value != 0 ? (returnVal != 0 ? returnVal : "0.00") : "-";

    return returnVal;

  }

  getValueTooltipWithComma(value, currency, appName, misFlags) {

    let returnVal;

    if (currency == "INR") {

      returnVal = value / misFlags["divByDuringRetrievalForInr" + appName + "Tooltip"];

      if (misFlags["valueTooltipWithComma"])
        returnVal = Number(Number(returnVal).toFixed(0)).toLocaleString('en-IN');

      else
        returnVal = returnVal.toFixed(2);

    }

    else if (currency == "USD") {

      returnVal = value / misFlags["divByDuringRetrievalForUsd" + appName + "Tooltip"];

      if (misFlags["valueTooltipWithComma"])
        returnVal = Number(Number(returnVal).toFixed(0)).toLocaleString('en-GB');

      else
        returnVal = returnVal.toFixed(2);

    }

    return returnVal;

  }

  getAmountCurrencyFormat(value, currency) {

    if (currency == "INR")
      return Number(Number(value).toFixed(0)).toLocaleString('en-IN');

    else if (currency == "USD")
      return Number(Number(value).toFixed(0)).toLocaleString('en-GB');

    else
      return value.toFixed(2);

  }

  checkIfStringExistsInArrayOfStrings(array, string) {

    if (array && array.length > 0) {
      for (let arrayItem of array)
        if (arrayItem && (typeof arrayItem == "string") && arrayItem.includes(string))
          return true;

      return false;
    }
    else {
      return false
    }



  }

  convertToProperTimezone(time) {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return moment(localTime).format("DD-MMM-YYYY");
    //return localTime;
  }


  getItemsByRegex(pattern) {
    const regex = new RegExp(pattern);
    const items = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (regex.test(key)) {
        items.push({
          key: key,
          value: localStorage.getItem(key)
        })
      }
    }
  
    return items;
  }
  

}