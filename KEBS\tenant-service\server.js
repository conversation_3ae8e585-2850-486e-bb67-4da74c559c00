require('./secretsProcessor').setSecrets().then(()=>{
  const express = require("express");

const fs=require('fs');
const morgan=require('morgan');
require('./tracing.js')


var compression = require("compression");
const tenant = require("./routes/tenant-config");
const mongo_conn_native = require("./mongo_conn_native").Connection;
const path = require("path");
const ENV_FILE = path.join(__dirname, ".env");
require("dotenv").config({ path: ENV_FILE });

require("./logger").intialize();
const logger = require("./logger").logger;
var cors = require("cors");
const passport = require("passport");

// create express app
const app = express();
// let Sentry = require("@sentry/node")
// // const { ProfilingIntegration } = require("@sentry/profiling-node");

// //Sentry

// if (process.env.NODE_ENV == "prd" || process.env.NODE_ENV == "qual") {
//   Sentry.init({
//     dsn: "https://<EMAIL>/4505225218621440",
//     integrations: [
//       // enable HTTP calls tracing
//       new Sentry.Integrations.Http({ tracing: true }),
//       // enable Express.js middleware tracing
//       new Sentry.Integrations.Express({
//         // to trace all requests to the default router
//         app,
//         // alternatively, you can specify the routes you want to trace:
//         // router: someRouter,
//       }),
//       // new ProfilingIntegration()
//     ],

//     // We recommend adjusting this value in production, or using tracesSampler
//     // for finer control
//     tracesSampleRate: 1.0,
//     // profilesSampleRate: 1.0
//   });

//   // RequestHandler creates a separate execution context, so that all
//   // transactions/spans/breadcrumbs are isolated across requests
//   app.use(Sentry.Handlers.requestHandler());
//   // TracingHandler creates a trace for every incoming request
//   app.use(Sentry.Handlers.tracingHandler());

//   // the rest of your app

//   app.use(Sentry.Handlers.errorHandler());
// }

//Sentry End

// parse requests of content-type - application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: false }));

// parse requests of content-type - application/json
app.use(express.json());
//morgan
// if(process.env.NODE_ENV !='default')
// {
//   let accessLogStream = fs.createWriteStream(path.join(__dirname , '/morganlogs/inlogs.log'));
//   let accessLogStream1=fs.createWriteStream(path.join(__dirname , '/morganlogs/outlogs.log'));
// app.use(morgan('combined', {immediate:true ,stream: accessLogStream}));
// app.use(morgan('combined',{stream:accessLogStream1}))
// }
if (process.env.NODE_ENV != 'default') {

  const morgan_log_stream = fs.createWriteStream(path.join(__dirname, '/morganlogs/inlogs.log'));
  let accessLogStream1=fs.createWriteStream(path.join(__dirname , '/morganlogs/outlogs.log'));
  morgan.token('heapMemory', (req, res) => { return (process.memoryUsage().heapUsed / 1024 / 1024).toFixed(4) + " MB"; });

  const data_to_log = ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" Heap Memory\: :heapMemory';

  app.use(morgan(data_to_log, {stream: morgan_log_stream,immediate:true }));
  app.use(morgan('combined',{stream:accessLogStream1}))

}
//for compression
app.use(compression());

// passport configuration
require("./passport-config")(passport);
app.use(passport.initialize());
app.use(passport.session());

let healthcheckRouter = require("./healthcheck")
app.use("/api/tenant/health", healthcheckRouter)



app.use("/api/tenant/tenantConfigs", tenant);

app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "*");
  res.header("Access-Control-Allow-Headers", "*");
  res.header("Access-Control-Allow-Credentials", "*");
  next();
});
app.use(cors());



let port = process.env.PORT || 3801;

// listen for requests
const server = require("http").createServer(app);
server.listen(port, () => {
  logger.info(`Tenant server is listening on port ${port}`);
  console.log(`Tenant server is listening on port ${port}`);
  // mongo_conn._connect();
  mongo_conn_native.connectToMongo().then(()=>{
    process.send("ready")
  });
});
 //-------------------------->prometheus new<---------------------

//prometheus start
const prometheus = require('prom-client');
prometheus.collectDefaultMetrics()
const gcStats = require('prometheus-gc-stats'); //for gc-stats 
const startGcStats = gcStats(prometheus.register);//for gc-stats 
startGcStats();//for gc-stats 
// prometheus.register.clear()
// const apiMetrics = require('prometheus-api-metrics');
// app.use(apiMetrics())
// const responseTime = require('response-time')
const restResponseTime = new prometheus.Histogram({name:'tenant_service_rest_response_time',help:'REST API Response Time in Seconds',labelNames:['method','route','status_code']})
const errorCounter = new prometheus.Counter({ name: 'tenant_service_error_count', help: 'Total Error Count',labelNames: ['method', 'api']  });
const counter = new prometheus.Counter({
  name: 'tenant_service_requests_total',
  help: 'Total number of requests',
  labelNames: ['method', 'api'],
});
// app.use(responseTime((req,res,time)=>{console.log(req.route.path,' ',time*1000)})) for checking the response-time
// app.use(responseTime((req,res,time)=>{
//   if(req.route.path == '/metrics')
//   {
//     console.log('metrics scrapped')
//   }
//   else if(req?.route?.path)
//   {
//     restResponseTime.observe(
//       {
//         method: req.method,
//         route: req.route.path,
//         status_code: res.statusCode
//       }, time*1000
//     )
//   }
// }))
// Increment the counter on each request
app.use((req, res, next) => {
  counter.inc({ method: req.method ,api: req.path});
  next();
});
app.on('error', (err) => {
  // Increment error counter
  errorCounter.inc({ method: req.method ,api: req.path});
});

// Expose the metrics endpoint
app.get('/metrics', (req, res) => {
  prometheus.register.metrics().then((metrics) => {
    res.set('Content-Type', prometheus.register.contentType);
    res.end(metrics);
  }).catch((error) => {
    console.error('Error generating metrics:', error);
    res.status(500).send('Error generating metrics');
  });
});
//prometheus end
// ---------------------------prometheus new-------------------------------------

})
