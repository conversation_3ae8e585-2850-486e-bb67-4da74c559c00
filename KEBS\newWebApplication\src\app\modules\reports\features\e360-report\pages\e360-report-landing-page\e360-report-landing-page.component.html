<div class="row pt-3 pb-1">
  <div class="col-12 pl-0 pr-0 pt-15">
    <div class="row">
      <div class="col">
        <div class="d-flex justify-content-end"></div>
      </div>
      <div class="col-1">
        <button
          class="view-button-inactive"
          mat-icon-button
          (click)="openUdrfModal()"
          matTooltip="Apply Filters And Sort"
        >
          <mat-icon class="header-button-icon"> filter_list </mat-icon>
          <span class="header-button">Filter</span>
        </button>
        <div class="icon-v1"></div>
      </div>
    </div>
    <div class="row">       
      <div class="col-12">
        <udrf-header></udrf-header>
      </div>
    </div>
  </div>
</div>
<ng-container>
  <mat-card class="m-2">
    <form [formGroup]="ecReportForm">
      <div class="row">
        <div class="col-6">
          <div class="row field-title">
            Master Data Type <span class="required-star"> &nbsp;*</span>
          </div>
          <div class="row">
            <mat-form-field style="width: 100%" appearance="outline">
              <mat-label>Select One</mat-label>
              <mat-select formControlName="transactionTable" (selectionChange)="handleMasterDataType()">
                <mat-option *ngFor="let item of dataMaster" [value]="item.id" >
                  {{ item.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-6">
          <div class="row field-title">
            Start Date <span class="required-star"> &nbsp;*</span>
          </div>
          <div class="row">
            <mat-form-field style="width: 100%" appearance="outline">
              <div class="d-flex">
                <input
                  matInput
                  required="true"
                  placeholder="DD-MMM-YYYY"
                  formControlName="startDate"
                  [matDatepicker]="picker"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </div>
            </mat-form-field>
          </div>
        </div>
        <div class="col-6">
          <div class="row field-title">
            End Date <span class="required-star"> &nbsp;*</span>
          </div>
          <div class="row">
            <mat-form-field style="width: 100%" appearance="outline">
              <div class="d-flex">
                <input
                  matInput
                  required="true"
                  placeholder="DD-MMM-YYYY"
                  formControlName="endDate"
                  [min]="getStartDate"
                  [matDatepicker]="picker1"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker1"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker1></mat-datepicker>
              </div>
            </mat-form-field>
          </div>
        </div>
      </div>
    </form>
    <div class="col-12 mt-4">
      <div class="d-flex flex-row justify-content-center align-items-center">
        <button
          type="button"
          mat-raised-button
          class="ml-3 create-employee-btn"
          (click)="downloadReport()"
          [disabled]="ecReportForm.invalid || isReportDownloading"
        >
          Download Report
        </button>
      </div>
    </div>
  </mat-card>
</ng-container>
