-- Debug query for Location of Bench widget
-- This query helps identify why the API is returning zero counts

-- 1. Check total active employees
SELECT 'Total Active Employees' as check_type, COUNT(*) as count
FROM kebs_master.t_e360_employee_central teec
WHERE teec.is_active = 1;

-- 2. Check employees with employment details in date range
SELECT 'Employees with Employment Details' as check_type, COUNT(*) as count
FROM kebs_master.t_e360_employee_central teec
INNER JOIN kebs_master.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= CURDATE()
    AND teed.end_date >= CURDATE()
    AND teed.is_active = 1
WHERE teec.is_active = 1;

-- 3. Check employees with time details and available hours
SELECT 'Employees with Available Hours' as check_type, COUNT(*) as count
FROM kebs_master.t_e360_employee_central teec
INNER JOIN kebs_master.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= CURDATE()
    AND teed.end_date >= CURDATE()
    AND teed.is_active = 1
LEFT JOIN kebs_master.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= CURDATE()
    AND tetd.end_date >= CURDATE()
    AND tetd.is_active = 1
WHERE teec.is_active = 1 
    AND tetd.project_available_hours IS NOT NULL 
    AND tetd.project_available_hours > 0;

-- 4. Check unassigned employees (bench employees)
SELECT 'Bench Employees' as check_type, COUNT(*) as count
FROM kebs_master.t_e360_employee_central teec
INNER JOIN kebs_master.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= CURDATE()
    AND teed.end_date >= CURDATE()
    AND teed.is_active = 1
LEFT JOIN kebs_master.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= CURDATE()
    AND tetd.end_date >= CURDATE()
    AND tetd.is_active = 1
LEFT JOIN kebs_master.t_internal_stakeholders tis 
    ON teec.associate_id = tis.associate_id 
    AND tis.e360_end_date = '9999-12-31' 
    AND tis.is_active = 1
WHERE teec.is_active = 1 
    AND (tis.start_date IS NULL OR tis.end_date IS NULL)
    AND tetd.project_available_hours IS NOT NULL 
    AND tetd.project_available_hours > 0;

-- 5. Check regions available in the system
SELECT 'Available Regions' as check_type, 
       meor.region as region_name, 
       COUNT(*) as employee_count
FROM kebs_master.t_e360_employee_central teec
INNER JOIN kebs_master.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= CURDATE()
    AND teed.end_date >= CURDATE()
    AND teed.is_active = 1
LEFT JOIN kebs_master.t_e360_org_details teod
    ON teod.associate_id = teec.associate_id
    AND teod.start_date <= CURDATE()
    AND teod.end_date >= CURDATE()
    AND teod.is_active = 1
LEFT JOIN kebs_master.m_e360_org_region meor 
    ON meor.id = teod.region 
    AND meor.is_active = 1
LEFT JOIN kebs_master.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= CURDATE()
    AND tetd.end_date >= CURDATE()
    AND tetd.is_active = 1
LEFT JOIN kebs_master.t_internal_stakeholders tis 
    ON teec.associate_id = tis.associate_id 
    AND tis.e360_end_date = '9999-12-31' 
    AND tis.is_active = 1
WHERE teec.is_active = 1 
    AND (tis.start_date IS NULL OR tis.end_date IS NULL)
    AND tetd.project_available_hours IS NOT NULL 
    AND tetd.project_available_hours > 0
GROUP BY meor.region
ORDER BY employee_count DESC;

-- 6. Sample bench employees with their region details
SELECT 
    teec.associate_id,
    CONCAT(tepd.first_name, ' ', COALESCE(tepd.last_name, '')) as employee_name,
    meor.region as region_name,
    tetd.project_available_hours,
    tis.start_date as assignment_start,
    tis.end_date as assignment_end,
    CASE 
        WHEN tis.start_date IS NULL OR tis.end_date IS NULL THEN 'Unassigned'
        ELSE 'Assigned'
    END as status
FROM kebs_master.t_e360_employee_central teec
INNER JOIN kebs_master.t_e360_employment_details teed
    ON teed.associate_id = teec.associate_id
    AND teed.start_date <= CURDATE()
    AND teed.end_date >= CURDATE()
    AND teed.is_active = 1
LEFT JOIN kebs_master.t_e360_personal_details tepd
    ON tepd.associate_id = teed.associate_id
    AND tepd.start_date <= CURDATE()
    AND tepd.end_date >= CURDATE()
    AND tepd.is_active = 1
LEFT JOIN kebs_master.t_e360_org_details teod
    ON teod.associate_id = teec.associate_id
    AND teod.start_date <= CURDATE()
    AND teod.end_date >= CURDATE()
    AND teod.is_active = 1
LEFT JOIN kebs_master.m_e360_org_region meor 
    ON meor.id = teod.region 
    AND meor.is_active = 1
LEFT JOIN kebs_master.t_e360_time_details tetd
    ON tetd.associate_id = teec.associate_id
    AND tetd.start_date <= CURDATE()
    AND tetd.end_date >= CURDATE()
    AND tetd.is_active = 1
LEFT JOIN kebs_master.t_internal_stakeholders tis 
    ON teec.associate_id = tis.associate_id 
    AND tis.e360_end_date = '9999-12-31' 
    AND tis.is_active = 1
WHERE teec.is_active = 1 
    AND (tis.start_date IS NULL OR tis.end_date IS NULL)
    AND tetd.project_available_hours IS NOT NULL 
    AND tetd.project_available_hours > 0
LIMIT 10;
