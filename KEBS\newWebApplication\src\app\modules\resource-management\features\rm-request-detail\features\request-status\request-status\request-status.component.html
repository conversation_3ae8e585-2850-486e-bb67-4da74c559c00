<div *ngIf="!timeLineLoader">
    <div class="row mt-3 mb-3 justify-content-center">
      <mat-spinner diameter="25" matTooltip="Loading ..."> </mat-spinner>
    </div>
</div>
<div class="container request-status pt-3" *ngIf="timeLineLoader">
    <div class="timeline">
        <div class="Ocircle">
            <div class="circle" style="background: #515965; border: none;"><mat-icon
                    class="circle-item">done</mat-icon></div>
        </div>
        <div class="row" style="border-left: 1.9px solid #DADCE2; padding-left: 18px;display:block">
            <!-- <div class="circle" style="background: #515965; border: none;"><mat-icon class="circle-item">done</mat-icon></div> -->
            <h4 class="title">Requested On</h4>
            <h6 class="sub-title" style="color: #45546E; margin-bottom: 0px;">{{data.requested_on ? (data.requested_on | date: "dd-MMM-YYYY" )  : '-'}}</h6>
        </div>
        <div class="Ocircle">
            <div class="circle" style="border: 2px solid #515965; background: white;"></div>
        </div>
        <!-- <div class="row d-block" *ngIf="!is_final_resource" style="padding-left: 18px;">
            <h4 class="title">Finalise Resource</h4>
            <h6 class="sub-title" style="color: #F27A6C;" (click)="Pending()">Pending</h6>
        </div> -->
        <div>
            <div *ngFor="let data of data.action_items; let i = index">
                <div class="Ocircle">
                    <div *ngIf="data.workflow_status === 'A'" class="circle" style="background: #515965; border: none;">
                        <mat-icon class="circle-item">done</mat-icon>
                    </div>
                    <div *ngIf="data.workflow_status === 'R'" class="circle" style="background: #FF3A46;border: none;">
                        <mat-icon class="circle-item">close</mat-icon>
                    </div>
                    <div class="circle" *ngIf="data.workflow_status === 'S'" style="border: 2px solid #FFBD3D; background: white;">
                    </div>
                    <div
                  class="circle"
                  *ngIf="data.workflow_status === null"
                  style="border: 2px solid #515965; background: white"
                ></div>
                </div>
                <div class="OuterCircle" [ngStyle]="{'border-left': (len != i+1) ? '1.9px solid #DADCE2' :'none'}">
                    <div class="row" style="display: flex;">
                        <h4 class="title">{{data.item_name ? data.item_name : '-'}} </h4>
                         <!-- <h4 class="title" *ngIf="data.allocated_count > 1"> ({{data.allocated_count}})</h4> -->
                         <mat-icon class="arrowIcon" (click)="Collapse(i)"
                         *ngIf="!data.is_collapsed && data.sub_content != null">keyboard_arrow_up</mat-icon>
                     <mat-icon class="arrowIcon" (click)="Expand(i)"
                         *ngIf="data.is_collapsed && data.sub_content != null">keyboard_arrow_down</mat-icon>
                    </div>
                    <!-- <h6 class="sub-title" [ngStyle]="{'color': data.sub_title_color}">{{data.sub_title ? data.sub_title : '-'}}</h6> -->
                    <h6
                  class="sub-title"
                  [ngStyle]="{ color: data.sub_title_color }"
                  *ngIf="requestData.STATUS != 'Cancelled'"
                >
                  {{ data.sub_title ? data.sub_title : "-" }}
                </h6>
                <h6
                  class="sub-title"
                  [ngStyle]="{'color':(data.sub_title == 'Pending') ? requestData.status_color : data.sub_title_color }"
                  *ngIf="requestData.STATUS == 'Cancelled'"
                >
                  {{ (data.sub_title == 'Pending') ? 'Cancelled' : data.sub_title}}
                </h6>
                    <div class="container pl-0" *ngIf="!data.is_collapsed && data.sub_content != null">
                        <div class="row">
                            <span class="status_badge" *ngIf="data.status_badge != null" [ngStyle]="{'background': data.sub_title_color}">{{data.status_badge ?
                                data.status_badge : ""}}</span>
                        </div>
                        <div class="rejection" *ngIf="data.workflow_status === 'R'">
                            <div class="row d-flex">
                                <div *ngIf="data.rm_officier_oid">
                                    <app-user-profile type="small" [oid]="data.rm_officier_oid" [fetchProfileNameFromE360]="true">
                                    </app-user-profile>
                                </div>
                                <div *ngIf="!data.rm_officier_oid">
                                    
                                </div>
                            </div>
                            <span style="display: block;">{{data.reject_reason ? data.reject_reason : '-'}}</span>
                        </div>
                        <div class="row pt-1 pb-1 d-flex">
                            <app-user-image
              [id]="data.sub_content.resource_oid"
              imgWidth="34px"
              imgHeight="34px"
              [hasDefaultImg]="true"
            ></app-user-image>
                            <div class="row d-block pl-1">
                                <span class="res-item">{{data.sub_content.resource_name ? data.sub_content.resource_name : '-'}}</span>
                                <span class="res-subItem">{{data.sub_content.associate_id ? data.sub_content.associate_id : '-'}}</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row pb-2">
                            <div class="col-2 pl-0 pr-0">
                                <div class="row">
                                  <h4 class="colName">STATUS</h4>
                                </div>
                                <div class="row">
                                  <h4 class="colValue">
                                    {{
                                      data.sub_content.status
                                        ? data.sub_content.status
                                        : "-"
                                    }}
                                  </h4>
                                </div>
                              </div>
                              <!-- <div class="col-3">
                                                      <h4 class="colName">CURRENT PROJECT</h4>
                                                      <h4 class="colValue">{{data.sub_content.current_project}}</h4>
                                                  </div> -->
                              <!-- <div class="col-4 pl-0">
                                                      <h4 class="colName">YEARS OF EXPERIENCE</h4>
                                                      <h4 class="colValue">{{data.sub_content.years_of_exp ? data.sub_content.years_of_exp : '-'}}</h4>
                                                  </div> -->
                              <div class="col-3 pl-0 pr-0">
                                <div class="row">
                                  <h4 class="colName">Employment Type</h4>
                                </div>
                                <div class="row">
                                  <h4 class="colValue">
                                    {{
                                      data.sub_content.employment_type
                                        ? data.sub_content.employment_type
                                        : "-"
                                    }}
                                  </h4>
                                </div>
                              </div>
                              <div class="col-3 pl-0 pr-0">
                                <div class="row">
                                  <h4 class="colName">Utilization Capacity</h4>
                                </div>
                                <div class="row">
                                  <h4 class="colValue">
                                    {{
                                      data.sub_content.utilization_capacity
                                        ? data.sub_content.utilization_capacity + "%"
                                        : "-"
                                    }}
                                  </h4>
                                </div>
                              </div>
                              <div class="col-4 pl-0 pr-0">
                                <div class="row">
                                  <h4 class="colName">Scheduled Interview</h4>
                                </div>
                                <div class="row">
                                  <h4
                                    class="colValue"
                                    *ngIf="data.sub_content.is_interview_scheduled!= 0 && data.sub_content.is_interview_scheduled!= 1"
                                  >
                                    -
                                  </h4>
                                  <h4
                                    class="colValue"
                                    *ngIf="
                                     
                                      data.sub_content.is_interview_scheduled === 0
                                    "
                                  >
                                    NO
                                  </h4>
                                  <h4
                                    class="colValue"
                                    *ngIf="
                                    
                                      data.sub_content.is_interview_scheduled === 1
                                    "
                                  >
                                    YES
                                  </h4>
                                </div>
                              </div>
                            </div>
                            <div class="row pt-2 pb-2">
                              <div class="col-2 pl-0 pr-0">
                                <span class="itemHead">Skill &nbsp; Score</span>
                                <span
                                  class="itemSub"
                                  (click)="skillPopup(data.sub_content)"
                                  style="cursor: pointer; display: flex;padding-top: 3px;"
                                  *ngIf="data.sub_content.skill_score != null"
                                  ><p>
                                    {{
                                      data.sub_content.skill_score + "%"
                                    }}
                                  </p>
                                  <mat-icon class="infoOverlay material-symbols-outlined"
                                    >info</mat-icon
                                  ></span
                                >
                                <span
                                  class="itemSub"
                                  *ngIf="data.sub_content.skill_score === null"
                                  >{{
                                    "NA"
                                  }}</span
                                >
                              </div>
                              <div class="col-10 pl-0 pr-0">
                                <span class="itemHead">AVAILABLE &nbsp; FROM</span>
                                <span class="itemSub"
                                  >{{
                                    data.sub_content.allocated_start_date
                                      ? (data.sub_content.allocated_start_date
                                        | date : "dd-MMM-YYYY")
                                      : "-"
                                  }}
                                  &nbsp; To &nbsp;
                                  {{
                                    data.sub_content.allocated_end_date
                                      ? (data.sub_content.allocated_end_date
                                        | date : "dd-MMM-YYYY")
                                      : "-"
                                  }}</span
                                >
                              </div>
                              
                            </div>
                            <div class="row pt-1 pb-1 d-block">
                              <span class="itemHead" *ngIf="data.sub_content?.criteria?.length > 0">Acceptance Criteria</span>
                              <div
                                class="row pt-1"
                                *ngFor="let criteria of data.sub_content.criteria"
                              >
                                <div
                                  *ngIf="criteria.has_satisfied"
                                  class="circle-content"
                                  style="background: #52c41a; border: none"
                                >
                                  <mat-icon class="circle-item-content">done</mat-icon>
                                </div>
                                <div
                                  *ngIf="!criteria.has_satisfied"
                                  class="circle-content"
                                  style="background: #ff3a46; border: none"
                                >
                                  <mat-icon class="circle-item-content">close</mat-icon>
                                </div>
                                <h4 class="colCriteria">{{ criteria.criteria }}</h4>
                              </div>
                            </div>
                            <div class="notes">
                              <span class="noteHead">Notes</span>
                              <span class="noteContent" style="display: block">{{
                                data.sub_content.notes ? data.sub_content.notes : ""
                              }}</span>
                            </div>
                        <!-- <div class="row d-flex pl-3" *ngIf="data.workflow_status === 'R'">
                            <button (click)="allocateNewResource()" class="confirmButton" >Allocate New Resource</button>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>