import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'quote-card',
  templateUrl: './quote-card.component.html',
  styleUrls: ['./quote-card.component.scss']
})
export class QuoteCardComponent implements OnInit {

  @Input() quote: any;
  @Input() showDeleted: boolean = false;
  @Input() isCUDEnabled: boolean = true;
  @Input() isCRApplicable: boolean = false;
  @Input() showApprovalButtons: boolean = false;
  @Input() quoteStatus?: any;

  @Output() flagged: EventEmitter<any> = new EventEmitter<any>();
  @Output() changeRequestFlagged: EventEmitter<any> = new EventEmitter<any>();
  @Output() edit: EventEmitter<any> = new EventEmitter<any>();
  @Output() delete: EventEmitter<any> = new EventEmitter<any>();
  @Output() activityLog: EventEmitter<any> = new EventEmitter<any>();
  @Output() copy: EventEmitter<any> = new EventEmitter<any>();
  @Output() copyAsCR: EventEmitter<any> = new EventEmitter<any>();
  @Output() approveQuote: EventEmitter<any> = new EventEmitter<any>();
  @Output() rejectQuote: EventEmitter<any> = new EventEmitter<any>();
  @Output() openApproverPopUp: EventEmitter<any> = new EventEmitter<any>();

  constructor() { }

  ngOnInit(): void {
  }

  flagClick(quoteType) {
    if (quoteType !== 'CHANGE_REQUEST') this.flagged.emit("clicked");
    else this.changeRequestFlagged.emit("clicked");
  }

  editQuote() {
    this.edit.emit(true);
  }

  deleteQuote = () => {
    this.delete.emit(true);
  }

  openQuoteActivityLog = () => {
    this.activityLog.emit(true);
  }

  copyQuote = (quoteType) => {
    if (quoteType === 'CHANGE_REQUEST') this.copyAsCR.emit(true)
    else this.copy.emit(true)
  }

  triggerWorkflowQuoteApproval = (action?) => {
    this.openApproverPopUp.emit("clicked")
  }

  getStatusCss(status: number): any {
    switch (status) {
      case 4:
        return { 'background': '#E8E9EE', 'color': '#45546E' };
      case 1:
        return { 'background': '#FFF3E8', 'color': '#FA8C16' };
      case 2:
        return { 'background': '#EEF9E8', 'color': '#52C41A' };
      case 3:
        return { 'background': '#FFEBEC', 'color': '#FF3A46' };
      default:
        return {};
    }
  }

  getTooltipText(quote): string {
    if (quote.change_request_flag && quote.quote_type === 'CHANGE_REQUEST') {
      return 'Change Request (Active)';
    } else if (quote.quote_type === 'CHANGE_REQUEST') {
      return 'Change Request (In Active)';
    } else if (quote.flag) {
      return 'Active Quote';
    } else {
      return 'In Active Quote';
    }
  }

  getFontSet(quote): string {
    if (quote.quote_type === 'CHANGE_REQUEST' && quote.change_request_flag)
      return null; // Active Change Request

    return quote.flag ? null : 'material-symbols-outlined';
  }
}
