import { Activated<PERSON>oute, Router } from '@angular/router';
import sweetAlert from 'sweetalert2';
import {
  Component,
  EventEmitter,
  OnInit,
  Output,
  OnDestroy,
  ViewEncapsulation,
  HostListener
} from '@angular/core';
import {
  FormGroup,
  FormControl,
  FormArray,
  Validators,
  FormBuilder,
} from '@angular/forms';
import { CostDetailsDialogComponent } from '../cost-details-dialog/cost-details-dialog/cost-details-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { SubSink } from 'subsink';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import * as _ from 'underscore';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { LoginService } from 'src/app/services/login/login.service';
import { TmService } from '../../services/tm.service';
import { EmployeeDirectoryService } from 'src/app/modules/employee-directory/services/employee-directory.service';
import { accessObjectMaster } from '../../access-object.auth'
import { merge } from 'rxjs';
import moment from 'moment';
import { ProjectService } from 'src/app/modules/projects/services/project.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';

@Component({
  selector: 'teamMembers-request-form',
  templateUrl: './request-form.component.html',
  styleUrls: ['./request-form.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MMM-YYYY',
        },
        display: {
          dateInput: 'DD-MMM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
        useUtc: true,
      },
    }
  ],
})
export class RequestFormComponent implements OnInit,OnDestroy {
  constructor(
    private formBuilder: FormBuilder,
    private _router: Router,
    private _route: ActivatedRoute,
    public dialog: MatDialog,
    private _rmService: RmService,
    private _toaster: ToasterService,
    private _loginService: LoginService,
    private _tmService:TmService,
    private _edService: EmployeeDirectoryService,
    private _projectService: ProjectService,
    private _masterService: PmMasterService
  ) {}
  subs = new SubSink();
  @Output() goback = new EventEmitter();
  // sample: any = [
  //   { id: 1, name: 'one' },
  //   { id: 2, name: 'two' },
  // ];
  orgDetails: any = {
    entity:'-',
    division:'-',
    subdivision:'-'
  };
  disableSubmit = false;
  rmofficer_name : any;
  errtxt = false;
  rmofficerparams = {};
  rmofficerallocated = false;
  showcard = false;
  //costDetailsbtnclick = false;
  PriorityListMaster: any = [];
  ProjectRoleMaster: any = [];
  NationalityListMaster: any = [];
  EmploymentTypeMaster: any = [];
  WorkLocationMaster: any = [];
  OperationModelMaster: any = [];
  CommercialListMaster: any;
  ExpertiseMaster: any = [];
  PositionListMaster: any = [];
  SkillListMaster: any = [];
  SkillExperienceMaster: any = [];
  WorkExperienceMaster: any = [];
  RequestContextMaster: any = [];
  BookingTypeMaster: any = [];
  EntityListMaster: any= [];
  DivisionListMaster: any= [];
  SubDivisionListMaster: any= [];
  DivisionRateCardListMaster: any= [];
  SubDivisionRateCardListMaster: any= [];
  currencyListMaster: any = [];
  quoteUnitListMaster: any = [];
  projectIdentityList: any = [];
  regionList:any = [];
  itemId: any;
  isHybridWorkLocationActive: boolean = false
  isCommercialIdentity: boolean = false;
  //applicationRoleAccessList: any;

  loaderObject = {
    isComponentLoading: false,
    isFormSubmitLoading: false,
  };
  toggle:boolean;
  clickarrBookingType = [];
  clickarrCommercialType = [];
  clickarrOperationModel = [];
  fieldConfig = {}
  item_id:any
  OrgDetails : any;
  tenantSpecificFeatures = {
    hasCostDetails: false,
    hasRateCardDetails:false
  }
  RmFieldconfig:any;
  current_date = moment().format('YYYY-MM-DD');
  itemData:any;
  quoteData:any;
  isQuoteDataAvailable:boolean = false;
  rateCardDetails:any = [];
  rateCardPositions: any = [];
  //Entity Edit Component
  projectFormConfig: any=[];
  orgMappingList: any=[];
  selectedBusinessEntity: any;
  rateCardHeader:string;
  contactForm = new FormGroup({
    project_id: new FormControl(
      (this._router.url.split('/')[2] == 'project-management') ? parseInt(this._router.url.split('/')[5]) : parseInt(this._router.url.split('/')[7]),
      Validators.required
    ),
    opportunity_id: new FormControl(),
    quote_id:new FormControl(),
    quote_position_id: new FormControl(),
    // requested_by: new FormControl('', Validators.required),
    project_role: new FormControl(''),
    utilization_capacity: new FormControl(''),
    work_experience: new FormControl(),
    nationality: new FormControl(''),
    employment_type: new FormControl(''),
    request_context: new FormControl(1),
    work_location: new FormControl(''),
    position: new FormControl(),
    job_description: new FormControl(''),
    expected_closure_date: new FormControl(''),
    request_start_date: new FormControl(''),
    request_end_date: new FormControl(''),
    priority: new FormControl(),
    commercial: new FormControl(''),
    booking_type: new FormControl(''),
    operation_model: new FormControl(''),
    skill_arr: new FormArray([]),
    acceptance_criteria_arr: new FormArray([]),
    need_client_interview: new FormControl(),
    cost_details: new FormControl(''),
    entity: new FormControl('', Validators.required),
    division: new FormControl('', Validators.required),
    subdivision: new FormControl('', Validators.required),
    hybrid_work_location: new FormControl(),
    project_commercial_identity: new FormControl(),
    rate_details: new FormGroup({
      currency: new FormControl(''),
      location: new FormControl(''),
      position: new FormControl(''),
      entity: new FormControl(''),
      division: new FormControl(''),
      subdivision: new FormControl(''),
      rate_per_unit:new FormControl(''),
      rate_revenue: new FormControl(''),
      rate_cost: new FormControl(''),
      rate_card_id: new FormControl(''),
      opportunity_id: new FormControl(''),
    }),
    region: new FormControl(null),
    // has_cost_details: new FormControl(false),
    no_of_positions: new FormControl(''),
    customer: new FormControl(''),
    request_type: new FormControl(''),
    commercial_category: new FormControl('')
  });
  customerRoleMaster: any = []
  requestTypeMaster: any = []
  project_status: any = null;
  commercial_catageroies_list: any = [];
  nonBillableIdentity: any;
  opportunityMaster: any = [];

  get operationModelFormValue() {
    return this.contactForm.get('operation_model').value
  }

  get bookingTypeFormValue() {
    return this.contactForm.get('booking_type').value
  }

  get commercialFormValue() {
    return this.contactForm.get('commercial').value
  }
  formConfig: any

  async ngOnInit() {

    this.item_id  = (this._router.url.split('/')[2] == 'project-management') ? parseInt(this._router.url.split('/')[5]) : parseInt(this._router.url.split('/')[7])
    this.loaderObject.isComponentLoading = true;
    //Getting Item Data
    this.itemData = await this.getItemData();
    this.itemData = await this.utcDateFormat(this.itemData);
    this.project_status = this.itemData.project_status ? this.itemData.project_status : null
    await this.handleOrgModalData()
    this.calculateDynamicStyle();
    //Getting Organization details
    this.EntityListMaster = await this.getEntities();
    this.organizationDetailsFormListener();

    

    this.addskills();
    await this.handleFieldConfig()
    this.RmFieldconfig = await this.retrieveRmConfig()
    this.OperationModelMaster = await this.getOperationModel();
    this.BookingTypeMaster = await this.getBookingType();
    this.PositionListMaster = await this.getPositionList();
    this.currencyListMaster = await this.getCurrencyList();
    this.quoteUnitListMaster = await this.getQuoteUnitList();
    this.customerRoleMaster = await this.getCustomerList(); 
    this.requestTypeMaster = await this.getRequestType();
    // Patching Quote Details if through quote request form is opened !!!
    await this.getQuoteData();
    this.handleCostFieldChanges()
    await this.handleRmConfig()
    await this.handleProjectCommercial();
    this.loaderObject.isComponentLoading = false;
    this.regionList = await this.getRegionList();
    this.PriorityListMaster = await this.getPriorityList();
    this.ProjectRoleMaster = await this.getProjectRole();
    this.NationalityListMaster = await this.getNationalityList();
    this.EmploymentTypeMaster = await this.getEmploymentType();
    this.WorkLocationMaster = await this.getWorkLocation();
    this.SkillListMaster = await this.getSkillList();
    this.ExpertiseMaster = await this.getExpertiseList();
    this.SkillExperienceMaster = await this.getSkillExperience();
    this.WorkExperienceMaster = await this.getWorkExperience();
    this.RequestContextMaster = await this.getRequestContext();
    this.opportunityMaster = await this.getOpportunityMaster();
    
    
    await this.patchCostDetails()
    this.setValidatorsForDependentFields()
    await this.fetchDependentMasterData()
    this.criteriaNoWhiteSpaceValidator()
    this.jobDescNoWhiteSpaceValidator()
    await this.handlePrePatchValues();
  }

  setValidatorsForDependentFields() {
    this.handleOperationModelChange();
    this.handleCommercialIdentityChange();
  }

  async handleRmConfig() {
    this.tenantSpecificFeatures.hasCostDetails = this.RmFieldconfig['cost_details']['is_active']
    this.tenantSpecificFeatures.hasRateCardDetails = this.RmFieldconfig['isRateCardDetails'] ? this.RmFieldconfig['isRateCardDetails'] : false
    if(this.quoteData){
      this.rateCardHeader = this.RmFieldconfig['rateCardHeader']?.['quote'] ?? 'Quote Details'
    }
    else{
      this.rateCardHeader = this.RmFieldconfig['rateCardHeader']?.['rate_card'] ?? 'Rate Card Details'
    }
    if(this.tenantSpecificFeatures.hasCostDetails) {
      this.contactForm.get('cost_details').setValidators([Validators.required])
    }
    else {
      this.contactForm.get('cost_details').clearValidators()
      this.contactForm.get('cost_details').updateValueAndValidity()
    }
    if(this.tenantSpecificFeatures.hasRateCardDetails){
      this.getPositionDetails();
    }
    else{

    }
    if(this.RmFieldconfig['project_status_soft_booking_allowed'] && this.RmFieldconfig['project_status_soft_booking_allowed'].includes(this.project_status)){
      this.BookingTypeToggle(2)
    }
    this.contactForm.get('commercial').valueChanges.subscribe((value) => {
      this.handleCommercialIdentityChange();
    });
  }


  //Getting Org Details and Patching
  async prePatchFormDetails() {
    this.OrgDetails = await this.getorgdetails();
    // console.log("Org details", this.OrgDetails, this.OrgDetails.length)
    if(this.OrgDetails.length!=0){
      this.contactForm.get('entity').patchValue(this.OrgDetails[0].entity_id)
      this.contactForm.get('division').patchValue(this.OrgDetails[0].division_id)
      this.contactForm.get('subdivision').patchValue(this.OrgDetails[0].sub_division_id)
      console.log(this.OrgDetails[0])
      await this.getrmofficerstatus()
    }
  }

  handleCostFieldChanges() {
    merge(
      this.contactForm.get('position').valueChanges,
      this.contactForm.get('division').valueChanges,
      this.contactForm.get('subdivision').valueChanges,
      this.contactForm.get('entity').valueChanges,
      this.contactForm.get('nationality').valueChanges,
      this.contactForm.get('work_location').valueChanges,
      this.contactForm.get('operation_model').valueChanges
    ).subscribe(async () => {
      await this.patchCostDetails()
    })
  }

  async handleFieldConfig() {
    let fieldList: any = await this.retrieveFieldConfig('create_request')
    if(fieldList.length > 0) {
      fieldList.forEach((val,i) => {

        this.fieldConfig[val.field_key] = val

        let formField = this.contactForm.get(val.field_key)

        if(formField && val.is_active_field) {
          
          if(val.is_mandatory) {
            if(val.field_type == 'array'){
              for(let i=0;i<val.array_items.length;i++){
                if(val.array_items[i].is_mandatory){
                  const itemControls = <FormArray>this.contactForm.controls[val.field_key];
                  if(itemControls.controls.length > 0) {
                    const itemFormGroup = <FormGroup>itemControls.controls[0];
                    itemFormGroup.controls[val.array_items[i].field_key].setValidators([Validators.required])
                  }
                }
              }
            }
            else {
              formField.setValidators([Validators.required])
            }
          }
          else {
            formField.clearValidators()
            formField.updateValueAndValidity()
          }

          if(val.has_default_value) {
            if(val.field_type == 'toggle-button') {
              switch(val.field_key) {
                case 'operation_model': {
                  this.OperationModelToggle(val.field_default_value)
                  break
                }
                case 'commercial': {
                  this.CommercialToggle(val.field_default_value)
                  break
                }
                case 'booking_type': {
                  this.BookingTypeToggle(val.field_default_value)
                  break
                }
              }
            }
            // else if(val.field_type == 'toggle'){
            //   this.contactForm.get('need_client_interview').setValue(val.field_default_value);
            // }
            else if(val.field_key == 'expected_closure_date') {
              this.contactForm.get('expected_closure_date').patchValue(moment().add(2, 'weeks'))
            }
            else {
              formField.patchValue(val.field_default_value)
            }      
          }
          else {
            if(val.field_key == 'booking_type') {
              this.BookingTypeToggle(1)
            }
          }
        }
      })
    }
    
    //Patching Customer Details
    let customer_id = this.itemData.customer_id ? this.itemData.customer_id : null
    this.contactForm.get('customer').patchValue(customer_id)
  }

  getPriorityList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getPriorityList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getProjectRole() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getProjectRole().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getNationalityList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getNationalityList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getEmploymentType() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getEmploymentType().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getWorkLocation() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getWorkLocation().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getOperationModel() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getOperationModel().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getBookingType() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getBookingType().subscribe(
        (res: any) => {
          resolve(res.data);
          console.log('Booking Type', res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getPositionList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getPositionList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  getSkillList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getSkillList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getExpertiseList(){
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService.getSkillLevel().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getSkillExperience() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getSkillExperience().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getWorkExperience() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getWorkExperience().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getCurrencyList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getCurrencyList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getQuoteUnitList(){
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getUnitList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getRequestContext() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getRequestContext().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getOpportunityMaster() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getOpportunityMaster().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  retrieveFieldConfig(view_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.retrieveFieldConfig(view_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  determineCostDetails(val: any) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.determineCostDetails(val).subscribe(
        (res: any) => {
          // if (!res.err) {
          //   this._toaster.showSuccess(
          //     'Success',
          //     'Cost Details received successfully!',
          //     2000
          //   );
          // }
          // this.contactForm.get('cost_details').patchValue(res.data);
          resolve(res);
        },
        (err) => {
          console.log(err);
          // this._toaster.showError(
          //   'Failed',
          //   'Failed to calculate cost details',
          //   2000
          // );
          reject(err);
        }
      );
    });
  }

  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._tmService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err) {
          resolve(res.data)
        }
      },(err) => {
        console.log(err)
      })
    })
  }

  async handleOrgModalData() {
    await this._projectService.getProjectFormFieldConfig().then((res: any)=>{
      if(res['messType']=="S"){
        this.projectFormConfig = res['data']
      }
    })
    await this._projectService.getOrgMapping().then(async(res: any)=>{
      if(res['messType']=="S"){
        this.orgMappingList = res['data']
      }
    })
  }

  createResourceRequest(val: any) {
    // this._tmService.setIndexForCompilerSync(2);
    this.loaderObject.isFormSubmitLoading = true;
    this.handleDateFormatPayLoad(val)
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.createResourceRequest(val).subscribe(
        (res: any) => {
          if (!res.err) {
            if(res?.is_multiple){
              sweetAlert.fire(
                `${res?.request_count} Requests got Created!`,
                `Reference Request ID: ${res.request_id}`,
                'success'
              );
            }
            else{
              sweetAlert.fire(
              'Request Creation successful!',
              `Request ID: ${res.request_id}`,
              'success'
            );
          }
            this._toaster.showSuccess(
              'Success',
              'Request created Successfully !',
              2000
            );
          }
          this.loaderObject.isFormSubmitLoading = false;
          if(this.isQuoteDataAvailable){
            this._tmService.setIndexForCompilerSync(2)
          }
          else{
            this._tmService.setIndexForCompilerSync(1)
          }
          this.returnback();
          resolve(res.data);
        },
        (err) => {
          this.loaderObject.isFormSubmitLoading = false;
          console.log(err);
          reject(err);
        }
      );
    });
  }

  returnback = () => {
    this._router.navigate(['../'], { relativeTo: this._route });

  };


  onSubmit() {
    let requestData = [{ params: this.contactForm.value }];
    let clonedFormValues = { ...this.contactForm.value };

    if (this.fieldConfig && this.fieldConfig['commercial'] && this.fieldConfig['commercial']['is_project_based']) {
        let commercialValue = clonedFormValues['commercial'];

        if (commercialValue == '1') {
            clonedFormValues['commercial'] = 1;
            clonedFormValues['project_commercial_identity'] = null;
        } else if (commercialValue.includes('2')) {
            let billable_identity = commercialValue.split('_');

            clonedFormValues['commercial'] = 0;
            clonedFormValues['project_commercial_identity'] = billable_identity[1];
        }
    }
    console.log(clonedFormValues)
    // console.log('Request Data', requestData);
    // console.log('Form Status', this.contactForm.status);
    // console.log('Form Values', this.contactForm.value);
    if(this.contactForm.status === 'VALID'  && this.rmofficerallocated){
      this.errtxt = false
      this.disableSubmit = true;
      this.createResourceRequest(clonedFormValues);
    }
    else{
      this.showSubmitWarningToaster()
      this.errtxt = true
    }
  }

  handleSkillChange(targetIndex) {
    // console.log('skill changed',targetIndex)
    let skillArr = this.contactForm.get('skill_arr') as FormArray;
    let targetCtrl = skillArr.at(targetIndex).get('skill');
    skillArr.controls.forEach((e,i) => {
      if(i!=targetIndex) {
        if(e.get('skill').value === targetCtrl.value && e.get('skill').value  && targetCtrl.value ) {
          // console.log("Skill Value:",e.get('skill').value);
          // console.log("Target Value:",targetCtrl.value);
          targetCtrl.patchValue('')
          this._toaster.showWarning('Duplicate skills not allowed','Please select different skill to proceed')
        }
      }
    });
  }

  criteriaNoWhiteSpaceValidator() {
    let criteriaArr = this.contactForm.get('acceptance_criteria_arr') as FormArray;
    if(this.fieldConfig['criteria']['is_mandatory'] && 
    this.fieldConfig['criteria']['array_items'][0]['is_mandatory']) {
      for(let l of criteriaArr.controls) {
        l.get('criteria').setValidators([this.noWhiteSpaceValidator,Validators.pattern("(?! ).*[^ ]")])
        l.get('criteria').updateValueAndValidity()
      }
      // criteriaArr.updateValueAndValidity()
    }
    // console.log(criteriaArr)
  }

  jobDescNoWhiteSpaceValidator() {
    if(this.fieldConfig['job_description']['is_mandatory']) {
      this.contactForm.get('job_description').setValidators([this.noWhiteSpaceValidatorJD])
      this.contactForm.get('job_description').updateValueAndValidity()
    }
  }

  noWhiteSpaceValidator(formControl : FormControl) {
    return (formControl.value || '').trim().length
    ? null : {'whitespace': true}
  }

  noWhiteSpaceValidatorJD(formControl : FormControl) {
    return ((formControl.value).replace(/(<([^>]+)>)/ig, '') || '').trim().length
    ? null : {'whitespace': true}
  }
  


  showSubmitWarningToaster() {
    if(this.tenantSpecificFeatures.hasCostDetails && 
      !this.contactForm.get('cost_details').valid) {

      if(this.isValidCostDetailsParam()) {
          this._toaster.showWarning('Cost Details not found !','Kindly contact KEBS team !')
        }
      else {
        this._toaster.showWarning('Cost Details not found !','Kindly fill all mandatory fields to fetch cost details !')
      }
      
    }
    else {
      this._toaster.showWarning('Mandatory fields are not entered','Kindly fill all mandatory fields to proceed !')
    }
  }

  createItem(): FormGroup {
    return this.formBuilder.group({
      skill: '',
      expertise:''
    });
  }

  createAcceptanceCriteria(): FormGroup {
    return this.formBuilder.group({
      criteria: [null,[Validators.pattern("(?! ).*[^ ]")]],
    });
  }
  addskills() {
    this.addItem();
    this.addAcceptanceCriteria();
  }
  removeskills(i: any) {
    if ((this.contactForm.get('skill_arr') as FormArray).length > 1) {
      (this.contactForm.get('skill_arr') as FormArray).removeAt(i);
    }
  }
  removeAcceptanceCriteria(i: any) {
    if (
      (this.contactForm.get('acceptance_criteria_arr') as FormArray).length > 1
    ) {
      (this.contactForm.get('acceptance_criteria_arr') as FormArray).removeAt(
        i
      );
    }
  }
  addItem(): void {
    this.contactForm.value.skill_arr = this.contactForm.get(
      'skill_arr'
    ) as FormArray;
    this.contactForm.value.skill_arr.push(this.createItem());
    //console.log('new', this.contactForm.value);
  }
  addAcceptanceCriteria(): void {
    //console.log("criteria",this.contactForm.controls['acceptance_criteria_arr'])
    let criteria_status = this.contactForm.controls['acceptance_criteria_arr'].status
    // if(criteria_status == 'INVALID'){
    //   this._toaster.showWarning("Warning","Enter Acceptance Criteria with No Space at Start and End")
    // }
    if(criteria_status == 'VALID'){
      this.contactForm.value.acceptance_criteria_arr = this.contactForm.get(
        'acceptance_criteria_arr'
      ) as FormArray;
      this.contactForm.value.acceptance_criteria_arr.push(
        this.createAcceptanceCriteria()
      );
      // console.log('new', this.contactForm.value);
    }
  }

  async patchCostDetails() {
    this.isCostDetailsBtnActive()
    if (this.isValidCostDetailsParam()) {
      let bodyParams = {
        position: this.contactForm.get('position').value,
        entity: this.contactForm.get('entity').value,
        division: this.contactForm.get('division').value,
        sub_division: this.contactForm.get('subdivision').value,
        nationality: this.contactForm.get('nationality').value,
        project_location: this.contactForm.get('operation_model').value,
        work_location: this.contactForm.get('work_location').value
      };
      let costDetails = await this.determineCostDetails(bodyParams);
      if(costDetails['has_cost_details'])
        this.contactForm.get('cost_details').patchValue(costDetails['data'])
      else  
        this.contactForm.get('cost_details').patchValue('')
      console.log("Cost Details: ",costDetails);
    }
    else {
      this.contactForm.get('cost_details').patchValue('')
    }
  }

  isValidCostDetailsParam() {
    return (
      this.tenantSpecificFeatures.hasCostDetails &&
        this.contactForm.get('position').valid && this.contactForm.get('position').value &&
        this.contactForm.get('division').valid && this.contactForm.get('division').value &&
        this.contactForm.get('subdivision').valid && this.contactForm.get('subdivision').value &&
        this.contactForm.get('entity').valid && this.contactForm.get('entity').value &&
        this.contactForm.get('nationality').valid && this.contactForm.get('nationality').value &&
        this.contactForm.get('work_location').valid && this.contactForm.get('work_location').value &&
        this.contactForm.get('operation_model').valid && this.contactForm.get('operation_model').value
    )
  }

  openCostDetailDialog() {
    let hasAccess = this._tmService.checkAccessForObject(accessObjectMaster.costDetailsVisibility)
    if (hasAccess) {
      console.log(this.contactForm.get('cost_details').valid,this.contactForm.get('cost_details').value)
      if(this.contactForm.get('cost_details').valid) {
        // console.log(this.contactForm.get('cost_details').value, "Cost details")
        let dialogRef = this.dialog.open(CostDetailsDialogComponent, {
          height: '400px',
          width: '450px',
          data: { modalParams: this.contactForm.get('cost_details').value },
        });
      }
      else{
        this._toaster.showWarning("Cost Details not found !","Kindly contact KEBS team !")
      }
    }
    else {
      this._toaster.showWarning("Access Denied",
      "You don't have access to view cost details")
    }
  }

  OperationModelToggle(id) {
    this.contactForm.get('operation_model').patchValue(id)
    this.handleOperationModelChange()
  }

  BookingTypeToggle(id) {
    this.contactForm.get('booking_type').patchValue(id);
  }

  CommercialToggle(id) {
    this.contactForm.get('commercial').patchValue(id);
     this.handleCommercialIdentityChange()
  }

  getEntities() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService.getEntities().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getDivision(entity) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService.getDivision(entity).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getSubDivision(entity, division) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService
        .getSubDivision(entity, division)
        .subscribe(
          (res: any) => {
            resolve(res.data);
          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });
  }

  async organizationDetailsFormValueListener() {
    // console.log(this.contactForm);
    let entityRes = this.getEntityValue;
    this.rmofficer_name = '-'
    this.showcard = false
    this.rmofficerallocated = false
    this.DivisionListMaster = []
    this.SubDivisionListMaster = []
    if(entityRes){
        this.DivisionListMaster = await this.getDivision(entityRes);
        //console.log(this.DivisionListMaster);
    }
    let divisionRes = this.contactForm.get('division').value;
    let entity_id = this.contactForm.get('entity').value;
    this.SubDivisionListMaster = []
    if(divisionRes && entity_id){
      this.SubDivisionListMaster = await this.getSubDivision(entity_id, divisionRes);
    }
    _.find(this.EntityListMaster, x=>{ 
        if(x.id === this.OrgDetails[0].entity_id && this.orgDetails.entity){
          this.orgDetails.entity =  x.name;
        }
       });

       //console.log(this.DivisionListMaster);
      _.find(this.DivisionListMaster, x=>{ 
        if(x.id === this.OrgDetails[0].division_id && this.orgDetails.division){
          this.orgDetails.division =  x.name;
        }
       });
       //console.log(this.SubDivisionListMaster);
      _.find(this.SubDivisionListMaster, x=>{ 
        if(x.id === this.OrgDetails[0].sub_division_id && this.orgDetails.subdivision){
          this.orgDetails.subdivision =  x.name;
        }
       });
      // console.log(this.contactForm);
      // console.log(this.orgDetails);
      this.getrmofficerstatus();
  }
  organizationDetailsFormListener() {
    //console.log(this.contactForm);
    this.contactForm
      .get('entity')
      .valueChanges.subscribe(async (res: any) => {
        this.contactForm.get('division').reset();
        this.contactForm.get('subdivision').reset();
        this.rmofficer_name = '-'
        this.showcard = false
        this.rmofficerallocated = false
        this.DivisionListMaster = []
        this.SubDivisionListMaster = []
        if(res){
          this.DivisionListMaster = await this.getDivision(res);
        }
      });
    this.contactForm
      .get('division')
      .valueChanges.subscribe(async (res: any) => {
        let entity_id = this.contactForm.get('entity').value;
        this.SubDivisionListMaster = []
        if(res && entity_id){
          this.SubDivisionListMaster = await this.getSubDivision(entity_id, res);
        }
      });
      //console.log(this.contactForm);
  }

  async fetchDependentMasterData() {
    let entity = this.contactForm.get('entity').value;
    let division = this.contactForm.get('division').value;
    if(entity)
      this.DivisionListMaster = await this.getDivision(entity)
    if(entity && division)
      this.SubDivisionListMaster =  await this.getSubDivision(entity,division)
  }

  get getstartdate(){
    return this.contactForm.get('request_start_date').value;
  }
  get getenddate(){
    return this.contactForm.get('request_end_date').value;
  }
  get getEntityValue(){
    return this.contactForm.get('entity').value;
  }
  get getDivisionValue(){
    return this.contactForm.get('division').value;
  }
  get getSubdivisionValue(){
    return this.contactForm.get('subdivision').value;
  }
  // get getItemStartDate(){
  //   console.log("Start Date Called!!!",this.itemData['start_date'])
  //   return this.itemData['start_date'];
  // }
  // get getItemEndDate(){
  //   console.log("End Date Called!!!",this.itemData['end_date'])
  //   return this.itemData['end_date'];
  // }

  
  getrmofficerstatus(){
    if((this.contactForm.get('entity').status &&
        this.contactForm.get('division').status &&
        this.contactForm.get('subdivision').status) === 'VALID'){
          this.rmofficerparams['entity']=this.contactForm.get('entity').value
          this.rmofficerparams['division']=this.contactForm.get('division').value
          this.rmofficerparams['sub_division']=this.contactForm.get('subdivision').value
          this.rmofficerparams['region']=this.contactForm.get('region').value
          // console.log(this.rmofficerparams);
          return new Promise((resolve, reject) => {
            this.subs.sink = this._rmService.getrmofficerstatus(this.rmofficerparams).subscribe(
              (res: any) => {
                this.showcard = true
                // console.log("rmofficer status",res.has_rm_officier)
                this.rmofficerallocated = res.has_rm_officier
                if(this.rmofficerallocated){
                     this.rmofficer_name = res.data[0].rmofficer_name
                }
                // console.log("rmofficer ",this.rmofficer_name)
                resolve(res);
              },
              (err) => {
                console.log(err);
                reject(err);
              }
            );
          });
        }
  }

  isCostDetailsBtnActive() {
    return (this.isValidCostDetailsParam() &&
    this._tmService.checkAccessForObject(accessObjectMaster.costDetailsVisibility))
  }

  getorgdetails(){
    return new Promise((resolve, reject) => {
      this.subs.sink =  this._rmService.getOrgFromProject(this.item_id).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  async addOrganisation(mode){
    // console.log("Entity Value:",this.getEntityValue);
    // console.log("FormConfig:",this.projectFormConfig);
    // console.log("orgMappingList:",this.orgMappingList);
    // console.log("selectedEntity:",this.selectedBusinessEntity);

    if(mode === 'edit'){
      let val={
        "entity_id":this.getEntityValue,
        "entity_name":this.orgDetails.entity,
        "division_id":this.getDivisionValue,
        "division_name":this.orgDetails.division,
        "sub_division_id":this.getSubdivisionValue,
        "sub_division_name":this.orgDetails.subdivision
      }
      this.selectedBusinessEntity= val
    }

    const { BusinessDivisionEntityComponent } = await import(
      'src/app/modules/projects/features/project-item/components/business-division-entity/business-division-entity.component'
    );
    const dialogRef = this.dialog.open(BusinessDivisionEntityComponent, {
      height: '75%',
      width: '75%',
      data: {
        mode: mode,
        formConfig:this.projectFormConfig,
        data:this.orgMappingList,
        selectedEntity:this.selectedBusinessEntity
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log(result);
      if (result['messType']=="S") {
        this.selectedBusinessEntity= result['data']
        // console.log("Result Data:", result['data']);
        // console.log("Entity:",result['data'].entity_id)
        this.contactForm.get('entity').patchValue(result['data'].entity_id);
        this.contactForm.get('division').patchValue(result['data'].division_id) 
        this.contactForm.get('subdivision').patchValue(result['data'].sub_division_id)
        this.orgDetails.entity = result['data'].entity_name;
        this.orgDetails.division = result['data'].division_name;
        this.orgDetails.subdivision= result['data'].sub_division_name;
        //console.log(this.contactForm);
        this.getrmofficerstatus();
      }
    })
  }

  handleDateFormatPayLoad(response){
    response.expected_closure_date = response.expected_closure_date ? moment(response.expected_closure_date).format('YYYY-MM-DD') : '';
    response.request_start_date = response.request_start_date ? moment(response.request_start_date).format('YYYY-MM-DD') : '';
    response.request_end_date = response.request_end_date ? moment(response.request_end_date).format('YYYY-MM-DD') : ''
    // response.expected_closure_date = response.expected_closure_date ? moment(response.expected_closure_date)
    // .startOf('day').add(moment().local().utcOffset(),'minutes') : ''

    // response.request_start_date = response.request_start_date ? moment(response.request_start_date)
    // .startOf('day').add(moment().local().utcOffset(),'minutes') : ''

    // response.request_end_date = response.request_end_date ? moment(response.request_end_date)
    // .startOf('day').add(moment().local().utcOffset(),'minutes') : ''
  }

  preventDefaultCapacity(e:any){
        // console.log(e);
        // console.log(this.contactForm.value.utilization_capacity)
        let val = this.contactForm.value.utilization_capacity;
       if(val > 100 && e != null){
         this.contactForm.get('utilization_capacity').patchValue(100);
         this._toaster.showWarning(
             'Warning',
            'Utilization Capacity Must Not be greater than 100'
          );
       }
       else if(val === 0 && e != null){
        
          this.contactForm.get('utilization_capacity').patchValue(null);
          this._toaster.showWarning(
              'Warning',
              'Utilization Capacity Must Not be Less than 1'
            );
        }
   }

   preventNoofPositions(e:any){
    // console.log(e);
    // console.log(this.contactForm.value.utilization_capacity)
    let val = this.contactForm.value.no_of_positions;
   if(val > 100 && e != null){
     this.contactForm.get('no_of_positions').patchValue(100);
     this._toaster.showWarning(
         'Warning',
        'You can request up to 100 positions only'
      );
   }
   else if(val === 0 && e != null){
    
      this.contactForm.get('no_of_positions').patchValue(null);
      this._toaster.showWarning(
          'Warning',
          'Request for atleast one Position'
        );
    }
  }

  handleOperationModelChange() {
    let operationModel = this.contactForm.get('operation_model').value;
    // Hybrid
    // console.log('operation model',operationModel)
    if(operationModel == 3) {
      this.isHybridWorkLocationActive = true
      if(this.fieldConfig['hybrid_work_location'] && 
        this.fieldConfig['hybrid_work_location']['is_active_field'] && 
        this.fieldConfig['hybrid_work_location']['is_mandatory']) {
          this.contactForm.get('hybrid_work_location').setValidators([Validators.required])
      }
      else {
        this.contactForm.get('hybrid_work_location').clearValidators()
        this.contactForm.get('hybrid_work_location').updateValueAndValidity()
      }
    }
    else {
      this.isHybridWorkLocationActive = false
      this.contactForm.get('hybrid_work_location').clearValidators()
      this.contactForm.get('hybrid_work_location').updateValueAndValidity()
      this.contactForm.get('hybrid_work_location').patchValue('')
    }
    // console.log(this.contactForm.get('hybrid_work_location'))
  }
  resetClosureDate(){
    //console.log(this.contactForm.get('expected_closure_date').value,this.contactForm.get('request_end_date').value)
    if(this.contactForm.get('expected_closure_date').value > this.contactForm.get('request_end_date').value){
      this.contactForm.get('expected_closure_date').reset()
    }
    
  }
  getItemData=()=>{
    return new Promise((resolve, reject) => {
      this.subs.sink =  this._tmService.getItemLevelData(this.item_id).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  utcDateFormat=(itemData)=>{
    itemData['start_date'] = moment(itemData['start_date']).utc()
    itemData['end_date'] = moment(itemData['end_date']).utc()
    return itemData
  }

  async getQuoteData(){
    this.subs.sink  = this._tmService.selectedQuote$.subscribe((value) =>{
      this.quoteData = value;
      // console.log(this.quoteData);
    })
    if(this.quoteData){
      this.quoteData.start_date = moment(this.quoteData.start_date).format('YYYY-MM-DD');
      this.quoteData.end_date = moment(this.quoteData.end_date).format('YYYY-MM-DD');
      this.contactForm.get('opportunity_id').patchValue(this.quoteData.opportunity_id);
      this.contactForm.get('quote_id').patchValue(this.quoteData.quote_id);
      this.contactForm.get('quote_position_id').patchValue(this.quoteData.quote_position_id);
      this.contactForm.get('work_location').patchValue(this.quoteData.work_location);
      this.contactForm.get('work_experience').patchValue(this.quoteData.work_experience);
      this.contactForm.get('entity').patchValue(this.quoteData.entity);
      this.contactForm.get('division').patchValue(this.quoteData.division);
      this.contactForm.get('subdivision').patchValue(this.quoteData.sub_division);
      this.contactForm.get('position').patchValue(this.quoteData.position_id);
      // this.contactForm.get('request_start_date').patchValue(this.quoteData.start_date);
      // this.contactForm.get('request_end_date').patchValue(this.quoteData.end_date);
      // console.log(this.contactForm.value);

      if(this.fieldConfig && this.fieldConfig['region'] &&  this.fieldConfig['region']['is_active_field'] && this.RmFieldconfig['disable_edit_org_details']){
        let sales_region = this.itemData.sales_region_id ? this.itemData.sales_region_id : null
        this.contactForm.get('region').patchValue(sales_region);
      }

      this.organizationDetailsFromQuote()
      if(this.quoteData){
        this.isQuoteDataAvailable = true;
      }
    }
    
  }

  async organizationDetailsFromQuote() {
    
    let entity_id = this.contactForm.get('entity').value;
    let division_id = this.contactForm.get('division').value;
    let sub_division_id = this.contactForm.get('subdivision').value;
    this.rmofficer_name = '-'
    this.showcard = false
    this.rmofficerallocated = false
    this.DivisionListMaster = []
    this.SubDivisionListMaster = []
    if(entity_id){
        this.DivisionListMaster = await this.getDivision(entity_id);
    }
    
    this.SubDivisionListMaster = []
    if(division_id && entity_id){
      this.SubDivisionListMaster = await this.getSubDivision(entity_id, division_id);
    }

    console.log("Sub Division:",this.SubDivisionListMaster);

    _.find(this.EntityListMaster, x=>{ 
        if(x.id === entity_id && entity_id){
          this.orgDetails.entity =  x.name;
        }
       });

      _.find(this.DivisionListMaster, x=>{ 
        if(x.id === division_id && division_id){
          this.orgDetails.division =  x.name;
        }
       });
       
      _.find(this.SubDivisionListMaster, x=>{ 
        if(x.id === sub_division_id && sub_division_id){
          this.orgDetails.subdivision =  x.name;
        }
       });
      
      this.getrmofficerstatus();
  }

  bookingType(value){
    // console.log(value)
    this.contactForm
      .get('booking_type')
      .patchValue(parseInt(value));
  }

  /**
   *@description function to fetch the position details of the rate card
   */
   async getPositionDetails(){
    let positionValue;
    if(this.isQuoteDataAvailable && this.quoteData){
      positionValue = this.quoteData.position_id;
      console.log(this.quoteData);
      let currencyId;
      _.find(this.currencyListMaster, x=>{ 
        if(x.name === this.quoteData.rate_currency_code && this.quoteData.rate_currency_code){
          currencyId =  x.id;
        }
       });
      console.log(currencyId);
      let value = {
        currency:currencyId,
        location:this.quoteData.work_location,
        position:positionValue,
        entity:this.quoteData.entity,
        division:this.quoteData.division,
        subdivision: this.quoteData.sub_division,
        rate_per_unit: this.quoteData.rate_unit,
        rate_revenue: this.quoteData.rate_revenue,
        rate_cost: this.quoteData.rate_cost,
        rate_card_id: this.quoteData.quote_position_id
      }
      await this.patchRateCardDetails(value)
    }
    else{
      // console.log('Item data:',this.itemData);
      if(this.itemData.customer_id){
        this.rateCardDetails = await this.getRateCardPositions(this.itemData.customer_id);
        if(this.rateCardDetails.length > 0){
          this.rateCardPositions = [];
          for(let item of this.rateCardDetails){
            // console.log('Item Id:',item.id);
            _.find(this.PositionListMaster, x=>{ 
              if(x.id === item.position){
                // x['rate_card_id'] = item.id;
                // console.log('X:',x)
                let val = {
                  id: item.position,
                  name:x.name,
                  rate_card_id:item.id
                }
                console.log(val);
                this.rateCardPositions = this.rateCardPositions.concat(val)
              }
             });
          }
          // console.log('Rate Card Positions After Filtering:',this.rateCardPositions)
        }
        else{
          this._toaster.showWarning(this.rateCardHeader + ' Not Found !','Kindly update '+this.rateCardHeader);
        }
      }
      else{
        this._toaster.showWarning("Customer Details Not Found !","Kindly update Customer Details !");
      }
    }
   }

   /**
    * @description 
    */
   async patchRateCardDetails(value) {
    // Patching a value for the nested form group
    console.log(value)
    this.DivisionRateCardListMaster = [];
    this.SubDivisionRateCardListMaster = [];
    if(value.entity){
      this.DivisionRateCardListMaster = await this.getDivision(value.entity);
      //console.log(this.DivisionListMaster);
    }
    if(value.division && value.subdivision){
      this.SubDivisionRateCardListMaster = await this.getSubDivision(value.entity, value.division);
    }
    this.contactForm.get('rate_details').patchValue({
      currency: value.currency,
      location: value.location,
      position: value.position,
      entity: value.entity,
      division: value.division,
      subdivision: value.subdivision,
      rate_per_unit:value.rate_per_unit,
      rate_revenue: value.rate_revenue,
      rate_cost: value.rate_cost,
      rate_card_id: value.rate_card_id
    });

    console.log(this.SubDivisionRateCardListMaster);
    console.log(this.DivisionRateCardListMaster);
    console.log('After Patching Rate Card Details:',this.contactForm.value);
  }

  /**
   * @description Fetching the Position Details for Rate Card
   */
  getRateCardPositions(customerId) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService
        .getRateCardPositions(customerId)
        .subscribe(
          (res: any) => {
            resolve(res.data);
          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });
  }

  /**
   * @description Patching the rate Card detail
   */
  async handleRateCard(val){
    console.log('Rate Card Changed:',val);
    let rateCardValue;
    _.find(this.rateCardDetails, x=>{ 
      if(x.id === val.rate_card_id){
        rateCardValue = x
      }
     });
    console.log('Rate Card Value:',rateCardValue)
    let value = {
      currency:rateCardValue.currency,
      location:rateCardValue.work_location,
      position:rateCardValue.position,
      entity:rateCardValue.entity,
      division:rateCardValue.division,
      subdivision: rateCardValue.sub_division,
      rate_per_unit: rateCardValue.quote_unit,
      rate_revenue: rateCardValue.revenue,
      rate_cost: rateCardValue.cost,
      rate_card_id: rateCardValue.id
    }
    await this.patchRateCardDetails(value)
  }

  handleCommercialIdentityChange() {
    let identity = this.contactForm.get('commercial').value;
    console.log(identity,"identity")
    // Hybrid
    // console.log('operation model',operationModel)
    if(identity == 2) {
      this.isCommercialIdentity = true
      if(this.fieldConfig['project_commercial_identity'] && 
        this.fieldConfig['project_commercial_identity']['is_active_field'] && 
        this.fieldConfig['project_commercial_identity']['is_mandatory']) {
          this.contactForm.get('project_commercial_identity').setValidators([Validators.required])
      }
      else {
        this.contactForm.get('project_commercial_identity').clearValidators()
        this.contactForm.get('project_commercial_identity').updateValueAndValidity()
      }
      this.contactForm.get('commercial_category').clearValidators()
      this.contactForm.get('commercial_category').updateValueAndValidity()
      
    }
    else if(identity == "2_1") {
      this.isCommercialIdentity = true
      if(this.fieldConfig['commercial_category'] && 
        this.fieldConfig['commercial_category']['is_active_field'] && 
        this.fieldConfig['commercial_category']['is_mandatory']) {
          this.contactForm.get('commercial_category').setValidators([Validators.required])
      }
      else {
        this.contactForm.get('commercial_category').clearValidators()
        this.contactForm.get('commercial_category').updateValueAndValidity()
      }
      this.contactForm.get('project_commercial_identity').clearValidators()
      this.contactForm.get('project_commercial_identity').updateValueAndValidity()
    }
    else {
      this.isCommercialIdentity = false
      this.contactForm.get('project_commercial_identity').clearValidators()
      this.contactForm.get('project_commercial_identity').updateValueAndValidity()
      this.contactForm.get('project_commercial_identity').patchValue('')
      this.contactForm.get('commercial_category').clearValidators()
      this.contactForm.get('commercial_category').updateValueAndValidity()
      this.contactForm.get('commercial_category').patchValue('')
    }
    // console.log(this.contactForm.get('project_commercial_identity'))
  }

  getRegionList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getRegionList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getCustomerList(){
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getCustomerDetails().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getRequestType(){
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getRequestTypeDetails().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  isBookingTypeDisabled(value:boolean){
    let enable_booking = this.RmFieldconfig['allow_booking_type_change'] && this.RmFieldconfig['allow_booking_type_change'].includes(this.project_status)
    if(value){
      if(enable_booking){
        return false
      }
      return value
    }
    else{
      return false
    }
  }

  async handlePrePatchValues(){
    if(this.fieldConfig && this.fieldConfig['priority'] &&  this.fieldConfig['priority']['is_active_field'] && this.fieldConfig['priority']['aging_based']){
      await this.priorityBasedOnRequestAging();
    }
    this.contactForm.get('expected_closure_date').valueChanges.subscribe(async (res: any) => {
        if(res){
          if(this.fieldConfig && this.fieldConfig['priority'] &&  this.fieldConfig['priority']['is_active_field'] && this.fieldConfig['priority']['aging_based']){
            await this.priorityBasedOnRequestAging();
          }
        }
    });
  }

  priorityBasedOnRequestAging(){
    const expectedClosureDate = this.contactForm.get('expected_closure_date').value;
    
    if (expectedClosureDate) {
        const currentDate = moment().format('YYYY-MM-DD');  // Use current date as moment object
        const closureDate = moment(expectedClosureDate).format('YYYY-MM-DD');
        
        // Calculate the difference in days
        let agingDays = moment(closureDate).diff(moment(currentDate), 'days');
        
        // If the dates are different, add 1 to make it inclusive
        if (moment(closureDate).isAfter(moment(currentDate))) {
          agingDays += 1;
        }
        
        // Find the priority based on aging
        let selectedPriority = this.PriorityListMaster.find(limit => 
            agingDays >= limit.aging_min_limit && agingDays <= limit.aging_max_limit
        );

        // If no matching priority is found, select the one with the highest max limit
        if (!selectedPriority) {
            selectedPriority = this.PriorityListMaster.reduce((highest, current) => 
                current.aging_max_limit > highest.aging_max_limit ? current : highest
            );
        }
        let priority = (selectedPriority && selectedPriority.id) ?  selectedPriority.id : null;
        this.contactForm.get('priority').patchValue(priority)
    }
  }

  enableOrgDetails(){
    if(this.quoteData && this.RmFieldconfig['disable_edit_org_details']){
      return false;
    }
   return true;
  }

  async handleProjectCommercial(){
    await this._masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });
    let commercialAvailable = _.findWhere(this.formConfig, {
      type: 'add-member',
      field_name: 'commercial',
      is_active: true,
    });

   
    if (commercialAvailable && this.fieldConfig && this.fieldConfig['commercial'] && 
      this.fieldConfig['commercial']['is_project_based']) {
      let billableList : any = []
      let show_identity = _.findWhere(this.formConfig, {type:"project-team", field_name:"show-identity", is_active: true})
      this.nonBillableIdentity = show_identity && show_identity.config && show_identity.config.value 
      ? show_identity.config.value 
      : 2;
      await this._masterService.getISABillingTypeList().then((res) => {
        billableList = res;
      });
      // this.CommercialListMaster = billableList;
      let commercial_list = [];
      this.commercial_catageroies_list =  this._masterService.commercialCatageroiesList;
      await this._masterService
        .getProjectIdentityList()
        .then(async (res: any) => {
          this.projectIdentityList = res;

          for (let commercial of billableList) {
           
            if (commercial['id'] == 0) {
              for (let identity of this.projectIdentityList) {
                let val = {
                  id: '2_' + (identity['id'] + ''),
                  name: commercial['name'] + ' ' + identity['name'],
                };

                commercial_list.push(val);
              }
            } else {
              let val = {
                id: '1',
                name: commercial['name'],
              };

              commercial_list.push(val);
            }
          }

          
        });
      this.CommercialListMaster =  commercial_list
    }
    else{
      this.CommercialListMaster = [
        {
          id: 1,
          name: 'Billable',
        },
        {
          id: 2,
          name: 'Non Billable',
        },
      ];
      await this._masterService.getProjectIdentityList().then((res)=>{
        this.projectIdentityList =res
        // console.log('Identity List:',this.projectIdentityList)
      })
    }
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle() {
    let headerHeight = window.innerHeight - 205 + 'px'
    document.documentElement.style.setProperty(
      '--formHeight',
      headerHeight
    )
  }

  ngOnDestroy () {
    this.subs.unsubscribe();
    this._tmService.setQuoteData(null);
  }
}
