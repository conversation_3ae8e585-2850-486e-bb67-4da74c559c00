import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { LoginService } from "src/app/services/login/login.service";
import { ProjectAccessService } from "../services/project-access.service"
import { PmAuthorizationService } from '../../../services/pm-authorization.service';
import * as _ from 'underscore';

@Injectable({
  providedIn: 'root'
})
export class ProjectAccessGuard implements CanActivate {

  itemId: any;
  projectId: any;

  constructor(
    private loginService: LoginService,
    private toaster: ToasterService,
    private _router:Router,
    private accessService: ProjectAccessService,
    private authService: PmAuthorizationService,
    private route: ActivatedRoute
  ) {}

  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot){

    let url = state['url'];



    let hasAccess = <boolean>await this.canAccess(url)

    console.log(hasAccess)

    if(hasAccess == false)
    {
      this.toaster.showError("You not having access to this Project!","Access Restricted",10000)
      this._router.navigateByUrl("/main/project-management");
    };

    return hasAccess
  }


  canAccess(url){

    return new Promise(async (resolve, reject)=>{
      try{

        await this.authService.getUserRoleAccessProjectList().then((res)=>{
          console.log(url, url.split("/"))
          this.itemId = parseInt(url.split("/")[5])
          this.projectId = parseInt(url.split("/")[3])
          console.log(this.itemId, this.projectId)
          
          let projectList = this.authService.projectListAccess; 
          console.log(this.authService.projectListAccess)
    
          let accessList = _.where(projectList, {item_id: this.itemId, project_id: this.projectId})
          resolve(accessList.length>0 ? true: false);
  
        },(err)=>{
          resolve(false);
        })
        
      }
      catch(err)
      {
        resolve(false);
      }
    })
   
  }
  
}
