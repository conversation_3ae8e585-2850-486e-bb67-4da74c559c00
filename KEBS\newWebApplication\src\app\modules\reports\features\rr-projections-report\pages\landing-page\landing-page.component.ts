import { Component, OnInit, ViewChild } from '@angular/core';
import {RrProjectionsService} from '../../services/rr-projections.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatMenuTrigger } from '@angular/material/menu';
import { DxDataGridComponent } from 'devextreme-angular';
import { MatDialog } from "@angular/material/dialog";
import {SaveLiveVersionComponent} from "../../components/save-live-version/save-live-version.component";
import swal from "sweetalert2";
import { MatMenu } from '@angular/material/menu';
import *  as _ from 'underscore';
import { SatPopover } from '@ncstate/sat-popover';
import { Router } from '@angular/router';
import { MatSelect } from '@angular/material/select';
import { takeUntil, debounceTime } from "rxjs/operators";
import { Subject } from 'rxjs';
import { FormControl, Validators } from '@angular/forms';
@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss']
})
export class LandingPageComponent implements OnInit {
  @ViewChild('MenuTrigger') MenuTrigger: MatMenuTrigger;
  @ViewChild('menu') menuTrigger: MatMenuTrigger;
  @ViewChild('openMenu') openMenu!: MatMenu;
  @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
  rrProjectionsReportColConfig:any;
  reportColumnsDefinitions:any;
  dataSource:any;
  isActiveIcon:any;
  title:any;
  versionId:any;
  startIndex:any = 0;
  offSet:any = 10;
  menuItems:any =[];
  reportId:any;
  type:any;
  savedTitle:any;
  progressPercentage = 0
  isCurrentVersion: boolean = false
  activeVersion = [];
  IsRRIsaBasedManualRunCheckAccess = false;
  startIndexForProjectList = 0;
  offSetForProjectList = 50;
  list: any = [];
  searchCtrl = new FormControl();
  selectedValCtrl = new FormControl(null,[Validators.required]);
  protected _onDestroy = new Subject<void>();
  @ViewChild('onScroll', { static: false }) onScrollProjectList: MatSelect;
  constructor(private _rrProjectionsService: RrProjectionsService,
    private _spinnerService: NgxSpinnerService,
    private _snackBar : MatSnackBar,
    private dialog: MatDialog,
    private $route: Router) { }

  async ngOnInit() {
    console.log("aaaaaaaaaaaaaaaaaaaaaaaa")
    await this.fetchData();
    if(this.list.length > 0){
      this.selectedValCtrl.patchValue(this.list[0].id);
    }
    await this.fetchReportData(1,0,'live');
    let activeVersion = await this.getActiveVersion();
    this.menuItems = await this.fetchVersionMaster(this.startIndex,this.offSet);
    console.log(this.menuItems)
    this.rrIsaBasedManualRunCheckAccess();

      this.searchCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy), debounceTime(700))
      .subscribe(async val => {
        if (val != null && val != '') {
          this.list=[];
          this.startIndexForProjectList=0;
          this.offSetForProjectList=50;
          let searchValue = await this.fetchData();
        }
      })
  }

  getActiveVersion(){
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getActiveVersion().subscribe((res:any) => {
        this.activeVersion = res.data
        console.log(this.activeVersion)
        resolve(res.data)       
      }, err => {
        reject(err);
      })
    })
  }

  getColumnConfigForRRReport(reportId) {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getColumnConfigForRRReport(reportId).subscribe((res:any) => {
        resolve(res.data)       
      }, err => {
        reject(err);
      })
    })
  }

  async fetchReportData(reportId,versionId,type){
    this.reportId = reportId;
    this.versionId = versionId;
    this.type = type;
    this.isCurrentVersion = false;
    this.rrProjectionsReportColConfig = await this.getColumnConfigForRRReport(reportId); 
    this.reportColumnsDefinitions =[]
    console.log(this.reportColumnsDefinitions)
    let colDef = this.rrProjectionsReportColConfig[0]?.column_config
    console.log(colDef)
    for(let l of colDef ){
      this.reportColumnsDefinitions.push({
        "report_column": l.valueExpr,
        "display_name" :l.column,
        "is_visible" : l.isVisible
      })
    }

    this.loadData(reportId,versionId,type)

  }

  getEmpRevenueProjectionLive(selectedVal) {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getEmpRevenueProjectionLive(selectedVal).subscribe((res:any) => {
        resolve(res.data) 
        this._spinnerService.hide();      
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve employee revenue projection live records","Dismiss",{duration:2000})
        reject(err);
      })
    })
  }

//   async getEmpRevenueProjectionLive() {
//     let totalRecords = await this.getTotalRecords(1);
//     console.log("totalRecords 83")
//     console.log(totalRecords)
//     let fetchedRecords = 0;
//     let startIndex = 0;
//     let pageSize = 5;
//     const fetchedData = [];
//     while (fetchedRecords < totalRecords) {
//         try {
//             // Fetch data for the current chunk
//             const response = await this._rrProjectionsService.getEmpRevenueProjectionLive(startIndex, pageSize).toPromise();
//             console.log("response")
//             console.log(response)
//             const chunkData = response['data'];

//             // Update fetched records count
//             fetchedRecords += chunkData.length;

//             // Update UI with the progress information
//             this.progressPercentage = Math.min((fetchedRecords / totalRecords) * 100, 100);
//             console.log(`Progress: ${this.progressPercentage}% completed`);

//              // Push chunkData into fetchedData array
//              fetchedData.push(...chunkData);

//             // Increment startIndex for the next chunk
//             startIndex += pageSize;
//         } catch (err) {
//             // Handle errors
//             this._spinnerService.hide();  
//             this._snackBar.open("Failed to retrieve employee revenue projection live records","Dismiss",{duration:2000})
//             throw err;
//         }
//     }
//     return fetchedData;
// }

  getEmpRevenueProjectionInactive() {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getEmpRevenueProjectionInactive().subscribe((res:any) => {
        resolve(res.data)
        this._spinnerService.hide();         
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve employee revenue projection inactive records","Dismiss",{duration:2000})
        reject(err);

      })
    })
  }
  getEmpRevenueProjectionVersionHistory() {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getEmpRevenueProjectionVersionHistory().subscribe((res:any) => {
        resolve(res.data)
        this._spinnerService.hide();         
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve employee revenue projection version history","Dismiss",{duration:2000})
        reject(err);
      })
    })
  }

  getPositionBasedProjection(){
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getPositionBasedProjection(this.selectedValCtrl.value).subscribe((res:any) => {
        resolve(res.data) 
        this._spinnerService.hide();      
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve position based revenue projection records","Dismiss",{duration:2000})
        reject(err);
      })
    })
  }

  getEmpRevenueProjectionActiveVersion(versionId) {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getEmpRevenueProjectionActiveVersion(versionId).subscribe((res:any) => {
        resolve(res.data)
        this._spinnerService.hide();         
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve employee revenue projection active version","Dismiss",{duration:2000})
        reject(err); 
      })
    })
  }

  getEmpProjectionErrorLogsHistory() {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getEmpProjectionErrorLogsHistory().subscribe((res:any) => {
        resolve(res.data)
        this._spinnerService.hide();         
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve employee projection error logs history","Dismiss",{duration:2000})
        reject(err); 
      })
    })
  }

  getProjectionRunLogs() {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getProjectionRunLogs(this.selectedValCtrl.value).subscribe((res:any) => {
        resolve(res.data)
        this._spinnerService.hide();         
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve projection logs history","Dismiss",{duration:2000})
        reject(err); 
      })
    })
  }

  getEmpProjectionManualRunHistory() {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getEmpProjectionManualRunHistory().subscribe((res:any) => {
        resolve(res.data)
        this._spinnerService.hide();         
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve employee projection manual log log history","Dismiss",{duration:2000})
        reject(err); 
      })
    })
  }

  fetchVersionMaster(startIndex,offSet) {
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getMasterVersions(startIndex,offSet).subscribe((res:any) => {
        resolve(res.data)       
      }, err => {
        reject(err);
      })
    })
  }

  async fetchVersionMasterOnSave(){
    this.menuItems = [];
    let activeVerisonData = await this.getActiveVersion();
    let data = await this.fetchVersionMaster(this.startIndex,this.offSet);

    this.menuItems = this.menuItems.concat(data)
    let element = _.filter(this.activeVersion,{version_id: this.versionId});
    console.log("activeVersion")
    console.log(this.activeVersion)
    console.log(this.versionId)
    console.log("element")
    console.log(element)
    if(element.length > 0){
      this.setTitle(element[0]);
    }
  }

  async onScroll(){
    this.startIndex = this.startIndex + this.offSet
    let data = await this.fetchVersionMaster(this.startIndex,this.offSet);
    this.menuItems = this.menuItems.concat(data)
  }

  menuItemsData(){
    if(this.menuItems.length == 0){
      this._snackBar.open("No Versions Found","Dismiss",{duration:2000})
    }
  }

  async loadData(reportId,versionId,type){
    if(reportId == 1){
      this.isActiveIcon = 1;
      this.title = 'Employee Revenue Projection Live'
      // this._spinnerService.show();
      this.dataSource = await this.getEmpRevenueProjectionLive(this.selectedValCtrl.value)
    }
    if(reportId == 2){
      this.isActiveIcon = 2;
      // this._spinnerService.show();
      this.title = 'Employee Revenue Projection Inactive'
      this.dataSource = await this.getEmpRevenueProjectionInactive()
    }
    if(reportId == 3){
      this.isActiveIcon = 3;
      // this._spinnerService.show();
      this.title = 'Employee Revenue Projection Version History'
      this.dataSource = await this.getEmpRevenueProjectionVersionHistory()
    }
    if(reportId == 7){
      this.isActiveIcon = 7;
      this.title = 'Quote Position Based Revenue Projection'
      this.dataSource = await this.getPositionBasedProjection()
    }
    if(type == 'active'){
      this.isActiveIcon = 4;
      // this._spinnerService.show();
      // this.title = 'Employee Revenue Projection Active Version'
      this.dataSource = await this.getEmpRevenueProjectionActiveVersion(versionId)
    }
    if(reportId == 6){
      this.isActiveIcon = 6;
      this.title = "Internal Stakeholders Projection Update Report";
      // this._spinnerService.show();
      this.dataSource = await this.getEmpProjectionErrorLogsHistory();
    }
    if(reportId == 5){
      this.isActiveIcon = 5;
      this.title = "Employee Projection Manual Run Logs"
      // this._spinnerService.show();
      this.dataSource = await this.getEmpProjectionManualRunHistory();
    }
    if(reportId == 8){
      this.isActiveIcon = 8;
      this.title = "RR Projection Logs"
      // this._spinnerService.show();
      this.dataSource = await this.getProjectionRunLogs();
    }
  }

  setTitle(item){
    this.title = item.title;
    this.isCurrentVersion = item.is_current_version == 1 ? true: false
  }

  async getTotalRecords(reportId): Promise<number> {
    // Implement the logic to fetch total number of records from the API
    return new Promise((resolve, reject) => {
      this._rrProjectionsService.getTotalRecords(reportId).subscribe((res:any) => {
        resolve(res.data)
        this._spinnerService.hide();         
      }, err => {
        this._spinnerService.hide();  
        this._snackBar.open("Failed to retrieve employee revenue projection inactive records","Dismiss",{duration:2000})
        reject(err);

      })
    })
}

async openLiveVersionSaveDialog(){
  let result = await this.dialog.open(SaveLiveVersionComponent,{
    height: '32%',
    width: '30%',
    data: {}
  })
  result.afterClosed().subscribe(result => {
    if (result === 'saved') {
      this.startIndex = 0;
      this.offSet = 10
      this.fetchVersionMasterOnSave();
    }
  });
}

async changeToCurrentVersion(){
  // this._spinnerService.show();
  console.log("changeToCurrentVersion")
  let saveCurrentVersion = await this._rrProjectionsService.changeSavedVersionToCurrentVersion(this.versionId);
  this._snackBar.open(saveCurrentVersion['messText'], 'Dismiss', { duration: 2000 });
  this.startIndex = 0;
  this.offSet = 10
  this.fetchVersionMasterOnSave();
}

async runOptionClicked(type:any){
  if(type =='All'){
    swal.fire({
      title: 'Do you want to run projections for all projects?',
      icon: 'info',
      showCancelButton: true,
      cancelButtonText: 'No',
      confirmButtonText: 'Yes',
      cancelButtonColor: '#aca8a8',
      confirmButtonColor: '#EE4961',
      allowOutsideClick: false,
      allowEscapeKey: false
    }).then((result) => {
      if (result.isConfirmed) {
        this.runAllClicked();
      } 
    });
  }
  else{
  const { RunInternalStakeholdersComponent } = await import('../../components/run-internal-stakeholders/run-internal-stakeholders.component')

  let dialogRef = this.dialog.open(RunInternalStakeholdersComponent, {
    width: "30%",
    height: "30%",
    autoFocus: false,
    maxWidth: "90vw",
    data: {
      type: type
    }
  });

  dialogRef.afterClosed().subscribe(result => {
    console.log('Dialog result:', result);
    if(result && result != null){
      this.insertProjetionLiveManualRun(type, result);
    }
  });
      
}
}

async insertProjetionLiveManualRun(type, value){
  if(value){
    if(type == "Projects"){
      let insertProjetionLiveData = await this._rrProjectionsService.insertRRProjectionsLive("Portfolio",value);
      this._snackBar.open(insertProjetionLiveData["messText"],"Dismiss",{duration:2000})
      this.fetchReportData(5,0,'manual_run');
    }
    if(type == "Items"){
      let insertProjetionLiveData = await this._rrProjectionsService.insertRRProjectionsLive("Project",value);
      this._snackBar.open(insertProjetionLiveData["messText"],"Dismiss",{duration:2000})
      this.fetchReportData(5,0,'manual_run');
    }
    if(type == "Isa"){
      let insertProjetionLiveData = await this._rrProjectionsService.insertRRProjectionsLive("Internal Stakeholder",value);
      this._snackBar.open(insertProjetionLiveData["messText"],"Dismiss",{duration:2000})
      this.fetchReportData(5,0,'manual_run');
    }
    if(type == "Billing Advice"){
      let insertProjetionLiveData = await this._rrProjectionsService.insertRRProjectionsLive("Billing Advice",value);
      this._snackBar.open(insertProjetionLiveData["messText"],"Dismiss",{duration:2000})
      this.fetchReportData(5,0,'manual_run');
    }
    if(type == "Billed Invoices"){
      let insertProjetionLiveData = await this._rrProjectionsService.insertRRProjectionsLive("Billed Invoices",value);
      this._snackBar.open(insertProjetionLiveData["messText"],"Dismiss",{duration:2000})
      this.fetchReportData(5,0,'manual_run');
    }
  }
}

async runAllClicked(){
  let insertRRProjectionsLiveAll = await this._rrProjectionsService.insertRRProjectionsLive("All",0);
  this._snackBar.open(insertRRProjectionsLiveAll["messText"],"Dismiss",{duration:2000})
  this.fetchReportData(5,0,'manual_run');
  console.log("run all clicked")
}

fetchReport(id, versionId, status, popover: SatPopover){
  popover.close();
  this.fetchReportData(id, versionId, status);
}

onCloseReport(){
  this.$route.navigateByUrl("/main/reports");
}

rrIsaBasedManualRunCheckAccess(){
    this.IsRRIsaBasedManualRunCheckAccess = this._rrProjectionsService.rrIsaBasedManualRunCheckAccess()
}

onOpenedChange(event: any, select: string) {
  if (event) {
    this[select].panel.nativeElement.addEventListener(
      'scroll',
      (event: any) => {
        let scrollTop = Math.ceil(this[select].panel.nativeElement.scrollTop);
        let scrollHeight = this[select].panel.nativeElement.scrollHeight - this[select].panel.nativeElement.offsetHeight;
        if (
          scrollTop-1==scrollHeight || scrollTop==scrollHeight || scrollTop+1== scrollHeight
        ) {
          this.startIndexForProjectList= this.startIndexForProjectList + this.offSetForProjectList;
          // this.offSet+=15;
          let searchResults = this.fetchData();
        }
      }
    );
  }
}

async fetchData () {

    let getItemList = await this._rrProjectionsService.getItemList(this.searchCtrl.value,this.startIndexForProjectList,this.offSetForProjectList);
    let itemData = getItemList['data']
    this.list=this.list.concat(getItemList['data']);
    console.log("list proj")
    console.log(this.list)

}

cancelSearchValue(){
  this.searchCtrl.patchValue('');
  this.startIndexForProjectList = 0;
  this.offSetForProjectList = 50;
  this.fetchData();
  
}

async onDataChange(event){
  if (this.selectedValCtrl.valid) {
    if(this.reportId == 1){
      this.isActiveIcon = 1;
      this.title = 'Employee Revenue Projection Live'
      // this._spinnerService.show();
      this.dataSource = await this.getEmpRevenueProjectionLive(this.selectedValCtrl.value)
    }
    if(this.reportId == 7){
      this.isActiveIcon = 7;
      this.title = 'Quote Position Based Revenue Projection'
      this.dataSource = await this.getPositionBasedProjection()
    }
    if(this.reportId == 8){
      this.isActiveIcon = 8;
      this.title = 'RR Projection Logs'
      this.dataSource = await this.getProjectionRunLogs();
    }
  }
}
}

