import { Component, OnInit } from '@angular/core';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import {RrProjectionsPositionServiceService} from '../../services/rr-projections-position-service.service';
import { MatDialog } from '@angular/material/dialog';
import { UtilityService } from 'src/app/services/utility/utility.service';
import * as moment from 'moment';
import { NgxSpinnerService } from 'ngx-spinner';
import { MatMenu } from '@angular/material/menu';
import { MatMenuTrigger } from '@angular/material/menu';
import {ColumnCustomizationComponent} from '../../components/column-customization/column-customization.component'
import * as _ from "underscore";
import { Router } from '@angular/router';
@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss']
})
export class LandingPageComponent implements OnInit {
  projectExpanded: boolean = false
  applicationId = 1031;
  filterConfig: any;
  pageSize = 8;
  startIndex = 0;
  durationRanges: any;
  hoursChecked: boolean = true;
  revenueChecked: boolean = true;
  costChecked: boolean = true;
  gmChecked: boolean = true;
  gmPercentageChecked: boolean = true;
  isToShowBudget: boolean = true;
  isToShowAllocated: boolean = true;
  isToShowActual: boolean = true;
  isToShowBalance: boolean = true;
  isToShowBilled: boolean = true;
  isToShowUBR: boolean = true;
  isToShowAR: boolean = true;
  isToShowMonth: boolean = true;
  months: string[] = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  projectData = [];
  positionData = [];
  empData = [];
  empExpanded: boolean = false;
  loading: boolean = false;
  billedData = {};
  billedWithTaxData = {};
  arData = {};
  summaryData: any;
  showSummary = false;
  constructor(public _udrfService: UdrfService,
    private _rrService: RrProjectionsPositionServiceService,
    private dialog: MatDialog,
    private _utilityService: UtilityService,
    private _spinnerService: NgxSpinnerService, private $route: Router,
  ) { }

  loadSummary = false;
  async showSummaryFunc(){
    this.showSummary = !this.showSummary;
    if(this.showSummary && !this.summaryData){
      this.loadSummary = true;
      // this._spinnerService.show();
      let summaryData = await this.getSummaryData();
      this.loadSummary = false;
      // this._spinnerService.hide();
    }
  }

  fyStartMonth = 4;
  fyEndMonth = 3;
  async getFinancialYearDetails(){
    let getFinancialYearDetails = await this._rrService.getFinancialYearDetails();
    this.fyStartMonth = (getFinancialYearDetails['data'][0]['fy_start_month']);
    this.fyEndMonth = (getFinancialYearDetails['data'][0]['fy_end_month']);
  }

  duration: any;
  async ngOnInit() {
    this._spinnerService.show()
    let fyData = await this.getFinancialYearDetails();
    
    this._udrfService.resetUdrfData();
    let data = await this.getTenantInfo();
    let billed_data = await this.getBilledData();
    let ar_data = await this.getARData();
    let billed_with_tax = await this.getBilledWithTaxData();
    let startMonth = 1; // Example: April
    let endMonth = 12;
    if(data && data['messType'] == 'S'){
      let date = data['data']
      startMonth = date['tenantFY'][0].fy_start_month
      endMonth =  date['tenantFY'][0].fy_end_month
    }

    const currentYear = moment().year();
    const previousYear = moment().subtract(1, 'year').year();

    let currentYearStart
    let currentYearEnd
    let previousYearStart
    let previousYearEnd
// Determine the start and end dates for the current year
if(startMonth == this.fyStartMonth){
   currentYearStart = moment().year(currentYear).month(startMonth - 1).endOf('month');
   currentYearEnd = moment().year(endMonth < startMonth ? currentYear+1 : currentYear).month(endMonth - 1).endOf('month');
  
  // Determine the start and end dates for the previous year
   previousYearStart = moment().year(previousYear).month(startMonth - 1).endOf('month');
   previousYearEnd = moment().year(endMonth < startMonth ? previousYear+1 : previousYear).month(endMonth - 1).endOf('month');
}
else{
   currentYearStart = moment().year(currentYear).month(startMonth - 1).endOf('month');
   currentYearEnd = moment().year(endMonth < startMonth ? currentYear+1 : currentYear).month(endMonth - 1).endOf('month');
  
  // Determine the start and end dates for the previous year
   previousYearStart = moment().year(previousYear).month(startMonth - 1).endOf('month');
   previousYearEnd = moment().year(endMonth < startMonth ? previousYear+1 : previousYear).month(endMonth - 1).endOf('month');
}

await this.initUdrf();

this.durationRanges = [
  {
    checkboxId: 'CDCRD',
    checkboxName: `FY ${moment(currentYearStart).subtract(4,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).subtract(4,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).subtract(4,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  },
  {
    checkboxId: 'CDCRD1',
    checkboxName: `FY ${moment(currentYearStart).subtract(3,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).subtract(3,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).subtract(3,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  },
  {
    checkboxId: 'CDCRD2',
    checkboxName: `FY ${moment(currentYearStart).subtract(2,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).subtract(2,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).subtract(2,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  },
  {
    checkboxId: 'CDCRD3',
    checkboxName: `FY ${moment(currentYearStart).subtract(1,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).subtract(1,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).subtract(1,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  },
  {
    checkboxId: 'CDCRD4',
    checkboxName: `FY ${moment(currentYearStart).format("YY")}`,
    checkboxStartValue: moment(currentYearStart).format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: true,
  },
  {
    checkboxId: 'CDCRD5',
    checkboxName: `FY ${moment(currentYearStart).add(1,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).add(1,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).add(1,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  },
  {
    checkboxId: 'CDCRD6',
    checkboxName: `FY ${moment(currentYearStart).add(2,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).add(2,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).add(2,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  },
  {
    checkboxId: 'CDCRD7',
    checkboxName: `FY ${moment(currentYearStart).add(3,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).add(3,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).add(3,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  },
  {
    checkboxId: 'CDCRD8',
    checkboxName: `FY ${moment(currentYearStart).add(4,"years").format("YY")}`,
    checkboxStartValue: moment(currentYearStart).add(4,"years").format("YYYY-MM-DD"),
    checkboxEndValue: moment(currentYearEnd).add(4,"years").format("YYYY-MM-DD"),
    isCheckboxDefaultSelected: false,
  }
];
console.log("durationRanges")
console.log(this.durationRanges)
console.log(moment(currentYearStart).subtract(3,"years").format("YY"))
console.log(currentYearStart.subtract(3,"years").format("YY"))
console.log(currentYearStart)

this._udrfService.udrfFunctions.constructCustomRangeData(
  1,
  'date',
  this.durationRanges
);
  }

  async getBilledWithTaxData(){
    let getBilledDataWithTax = await this._rrService.getBilledDataWithTax();
    if(getBilledDataWithTax["messType"] == "S"){
      this.billedWithTaxData = getBilledDataWithTax["data"];
      console.log("this.billedWithTaxData")
      console.log(this.billedWithTaxData)
    }
  }
  
  async getBilledData(){
    let getBilledData = await this._rrService.getRRBilledData();
    if(getBilledData["messType"] == "S"){
      this.billedData = getBilledData["data"];
      console.log("this.billedData")
      console.log(this.billedData)
    }
  }

  async getARData(){
    let getARData = await this._rrService.getRRARData();
    if(getARData["messType"] == "S"){
      this.arData = getARData["data"];
      console.log("this.arData")
      console.log(this.arData)
    }
  }

  async getSummaryData(){
    let getRRSummaryData = await this._rrService.getRRSummaryData(this.filterConfig);
    if(getRRSummaryData["messType"] == "S"){
      this.summaryData = getRRSummaryData["data"];
      console.log("summaryData")
      console.log(this.summaryData)
    
    let getRRSummaryBilledValue = await this._rrService.getRRSummaryBilledValue(this.filterConfig);
    if(getRRSummaryBilledValue["messType"] == "S"){
      console.log("summaryData")
      console.log(this.summaryData)
      this.summaryData.reporting_currency_1_billed_value = getRRSummaryBilledValue["data"][0].reporting_currency_1_value;
      this.summaryData.reporting_currency_2_billed_value = getRRSummaryBilledValue["data"][0].reporting_currency_2_value;
      this.summaryData.reporting_currency_1_billed_value_in_millions = getRRSummaryBilledValue["data"][0]["reporting_currency_1_value_in_millions"];
      this.summaryData.reporting_currency_2_billed_value_in_millions = getRRSummaryBilledValue["data"][0]["reporting_currency_2_value_in_millions"];
      // this.summaryData = this.summaryData.concat(getRRSummaryBilledValue["data"]);
    }

    let getRRSummaryARValue = await this._rrService.getRRSummaryARValue(this.filterConfig);
    if(getRRSummaryARValue["messType"] == "S"){
      this.summaryData.reporting_currency_1_ar_value = getRRSummaryARValue["data"]["reporting_currency_1_value"];
      this.summaryData.reporting_currency_2_ar_value = getRRSummaryARValue["data"]["reporting_currency_2_value"];
      this.summaryData.reporting_currency_1_ar_value_in_millions = getRRSummaryARValue["data"]["reporting_currency_1_value_in_millions"];
      this.summaryData.reporting_currency_2_ar_value_in_millions = getRRSummaryARValue["data"]["reporting_currency_2_value_in_millions"];
      // this.summaryData = this.summaryData.concat(getRRSummaryARValue["data"]);
    }

    let getRRActualRevenueValue = await this._rrService.getRRActualRevenueValue(this.filterConfig);
    if(getRRSummaryARValue["messType"] == "S" && getRRSummaryARValue["data"].length > 0){
      this.summaryData.reporting_currency_1_actual_revenue_value = getRRSummaryARValue["data"]["reporting_currency_1_actual_revenue_value"];
      this.summaryData.reporting_currency_2_actual_revenue_value = getRRSummaryARValue["data"]["reporting_currency_2_actual_revenue_value"];
      // this.summaryData = this.summaryData.concat(getRRSummaryARValue["data"]);
    }

    let ar_reporting_currency_1 = this.summaryData.reporting_currency_1_actual_revenue_value ?
    this.summaryData.reporting_currency_1_actual_revenue_value.replace(/[^0-9.-]+/g,"") : 0;
    let ar_reporting_currency_2 = this.summaryData.reporting_currency_2_actual_revenue_value ?
    this.summaryData.reporting_currency_2_actual_revenue_value.replace(/[^0-9.-]+/g,"") : 0;

    ar_reporting_currency_1 = ar_reporting_currency_1 ?
    parseFloat(ar_reporting_currency_1) : 0;
    ar_reporting_currency_2 = ar_reporting_currency_2 ?
    parseFloat(ar_reporting_currency_2) : 0;

    let billed_reporting_currency_1 = this.summaryData.reporting_currency_1_billed_value ?
    this.summaryData.reporting_currency_1_billed_value.replace(/[^0-9.-]+/g,"") : 0;
    let billed_reporting_currency_2 = this.summaryData.reporting_currency_2_billed_value ?
    this.summaryData.reporting_currency_2_billed_value.replace(/[^0-9.-]+/g,"") : 0;

    billed_reporting_currency_1 = billed_reporting_currency_1 ?
    parseFloat(billed_reporting_currency_1) : 0;
    billed_reporting_currency_2 = billed_reporting_currency_2 ?
    parseFloat(billed_reporting_currency_2) : 0;

    let usd_formatter = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    let inr_formatter = new Intl.NumberFormat('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });

    let usd_formatter_with_symbol = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    let inr_formatter_with_symbol = new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });

    this.summaryData.reporting_currency_1_ubr_value = ar_reporting_currency_1 - billed_reporting_currency_1;
    this.summaryData.reporting_currency_2_ubr_value = ar_reporting_currency_2 - billed_reporting_currency_2;

    this.summaryData.reporting_currency_1_ubr_value_in_millions = this.summaryData.reporting_currency_1_ubr_value ?
    (this.summaryData.reporting_currency_1_ubr_value/1000000) : 0.00;
    this.summaryData.reporting_currency_2_ubr_value_in_millions = this.summaryData.reporting_currency_2_ubr_value ?
    (this.summaryData.reporting_currency_2_ubr_value/10000000) : 0.00;

    this.summaryData.reporting_currency_1_ubr_value_in_millions = usd_formatter.format(this.summaryData.reporting_currency_1_ubr_value_in_millions);
    this.summaryData.reporting_currency_2_ubr_value_in_millions = inr_formatter.format(this.summaryData.reporting_currency_2_ubr_value_in_millions);

    this.summaryData.reporting_currency_1_ubr_value = usd_formatter_with_symbol.format(this.summaryData.reporting_currency_1_ubr_value);
    this.summaryData.reporting_currency_2_ubr_value = inr_formatter_with_symbol.format(this.summaryData.reporting_currency_2_ubr_value);
  }
  }

  getValueForSelectedCurrency(value_arr){
    let value = 0;
    if(value_arr){
      let get_selected_currency = _.filter(value_arr,{currency: this.selectedCurrency});
      value = get_selected_currency.length > 0 ? get_selected_currency[0].value_in_millions : 0;
    }
    return value ? value : "0.00";
  }

  getTooltipValueForSelectedCurrency(value_arr){
    let value = 0;
    if(value_arr){
      let get_selected_currency = _.filter(value_arr,{currency: this.selectedCurrency});
      value = get_selected_currency.length > 0 ? get_selected_currency[0].value : 0;
    }
    return value ? value : "0.00"
  }

  calculateUBR(project, billedValue){
    let usd_formatter = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
  });

  let inr_formatter = new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
  });

    let value = 0;
    if(billedValue){
      let get_selected_currency = _.filter(billedValue,{currency: this.selectedCurrency});
      let data = get_selected_currency.length > 0 ? get_selected_currency[0].value : 0;
      value = data ? parseFloat(data.replace(/[^0-9.-]+/g,"")) : 0;
    }
    let revenue = 0;
    if(this.selectedCurrencyId == 1){
      revenue = project.reporting_currency_1_actual_revenue_value ? parseFloat(project.reporting_currency_1_actual_revenue_value.replace(/[^0-9.-]+/g,"")) : 0;
    }
    else{
      revenue = project.reporting_currency_2_actual_revenue_value ? parseFloat(project.reporting_currency_2_actual_revenue_value.replace(/[^0-9.-]+/g,"")) : 0;
    }

    let ubr = revenue - value;
    
    return ubr ? this.selectedCurrencyId == 1 ? usd_formatter.format(ubr/1000000) : inr_formatter.format(ubr/10000000) : "0.00";
  }

  calculateTooltipUBR(project,billedValue,project_id){
    let usd_formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
  });

  let inr_formatter = new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
  });

    let value = 0;
    if(billedValue){
      let get_selected_currency = _.filter(billedValue,{currency: this.selectedCurrency});
      let data = get_selected_currency.length > 0 ? get_selected_currency[0].value : 0;
      value = data ? parseFloat(data.replace(/[^0-9.-]+/g,"")) : 0;
    }
    let revenue = 0;
    if(this.selectedCurrencyId == 1){
      revenue = project?.reporting_currency_1_actual_revenue_value ? parseFloat(project.reporting_currency_1_actual_revenue_value.replace(/[^0-9.-]+/g,"")) : 0;
    }
    else{
      revenue = project?.reporting_currency_2_actual_revenue_value ? parseFloat(project.reporting_currency_2_actual_revenue_value.replace(/[^0-9.-]+/g,"")) : 0;
    }

    let ubr = revenue - value;
    
    return ubr ? this.selectedCurrencyId == 1 ? usd_formatter.format(ubr) : inr_formatter.format(ubr) : "0.00";
  }

  async initUdrf() {
    this._udrfService.udrfData.applicationId = this.applicationId;
    this._udrfService.udrfData.isItemDataLoading = true;
    this._udrfService.udrfUiData.showItemDataCount = false;
    this._udrfService.udrfUiData.showSearchBar = false;
    this._udrfService.udrfUiData.showActionButtons = true;
    this._udrfService.udrfUiData.showUdrfModalButton = false;
    this._udrfService.udrfUiData.showColumnConfigButton = false;
    this._udrfService.udrfUiData.showSettingsModalButton = false;
    this._udrfService.udrfUiData.showNewReleasesButton = false;
    this._udrfService.udrfUiData.showReportDownloadButton = false;
    this._udrfService.udrfUiData.isReportDownloading = false;
    this._udrfService.udrfUiData.itemHasOpenInNewTab = false;
    this._udrfService.udrfUiData.horizontalScroll = false;
    this._udrfService.udrfUiData.itemHasQuickCta = false;
    this._udrfService.udrfUiData.collapseAll = false;
    this._udrfService.udrfUiData.showCollapseButton = false;
    this._udrfService.udrfUiData.countForOnlyThisReport = false;
    this._udrfService.udrfUiData.toggleChecked = false;
    this._udrfService.udrfUiData.countFlag = false;
    this._udrfService.udrfUiData.isMultipleView = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.emailPluginVisible = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.itemHasAttachFileButton = false;
    this._udrfService.udrfUiData.itemHasComments = false;
    this._udrfService.udrfUiData.isMoreOptionsNeeded = false;
    this._udrfService.udrfUiData.completeProfileBtn = false;
    this._udrfService.getAppUdrfConfig(
      this.applicationId,
      this.initReport.bind(this)
    );
  }

  isBilledArEntriesAdded = false;
  projectCount = 0;
  isFilterApplied = false;
  async initReport() {
    this.projectData = null;
    this.positionData = null;
    this.empData = null;
    this.projectData = [];
    this.positionData = [];
    this.empData = [];
    this.startIndex = 0;
    this.filterConfig = {
      startIndex: 0,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainFilterArray: this._udrfService.udrfData.mainFilterArray,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
    };
    this.months = await this.getMonthYearPairs(this.filterConfig.startDate,this.filterConfig.endDate)
    console.log("this.months")
    console.log(this.months)
    console.log("filterConfig")
    console.log(this.filterConfig)
    this._spinnerService.show()

    let get_rr_data_count = await this._rrService.getProjectTotalCount(this.filterConfig);
    if(get_rr_data_count["data"]){
      this.projectCount = get_rr_data_count["data"];
    }
    else{
      this.projectCount = 0;
    }
    this.isFilterApplied = this.filterConfig?.mainFilterArray && this.filterConfig?.mainFilterArray?.length > 0 ? true : false;
    let data =
      await this._rrService.getRRProjectData(
        this.startIndex,
        this.pageSize,
        this.filterConfig,
        this.selectedCurrency
      );
    console.log(data);
    this._spinnerService.hide()
    if(data["data"]){
      for(let item of data["data"]){
        let monthWiseData = item.monthwise_details;
        monthWiseData = monthWiseData ? monthWiseData : [];
        let monthData = [];
          for (let month of this.months) {
            let val = _.filter(monthWiseData,{"month": parseInt(moment(month, "MMM - YYYY").format("MM"))})
            if(val.length == 0){
              let dataMonth = [{month: parseInt(moment(month, "MMM - YYYY").format("MM"))}];
              monthData.push(dataMonth);
            }
            else{
              monthData = monthData.concat(val);
            }
          }
          item.monthwise_details = monthData;
      }

      if(data["data"].length == 0 && this.isBilledArEntriesAdded == false){
        let itemIdsInArr1 = this.projectData.map(obj => obj.item_id);

      // Find items in arr2 not found in arr1
      const itemsNotFoundInArr1 = Object.values(this.billedData).filter(obj => !itemIdsInArr1.includes(obj["item_id"]));
      console.log("itemsNotFoundInArr1")
      console.log(itemsNotFoundInArr1)
      this.isBilledArEntriesAdded = true;
      }
    }
    this.projectData = data["data"];
    console.log(this.projectData)
    console.log(data)
    console.log(data["data"])
    // let projectData = await this.getRRProjectData(
    //   this.filterConfig
    // );
  }

  async getRRProjectData(filterConfig) {
    this._spinnerService.show()
    this.isFilterApplied = this.filterConfig?.mainFilterArray && this.filterConfig?.mainFilterArray?.length > 0 ? true : false;
    let data =
      await this._rrService.getRRProjectData(
        this.startIndex,
        this.pageSize,
        filterConfig,
        this.selectedCurrency
      );
    console.log(data);
    this._spinnerService.hide()
    if(data["data"]){
      for(let item of data["data"]){
        let monthWiseData = item.monthwise_details;
        monthWiseData = monthWiseData ? monthWiseData : [];
        let monthData = null;
        monthData = [];
          for (let month of this.months) {
            let val = _.filter(monthWiseData, (p) => p.month == parseInt(moment(month, "MMM - YYYY").format("MM")))
            if(val.length == 0){
              let dataMonth = [{month: parseInt(moment(month, "MMM - YYYY").format("MM"))}];
              monthData.push(dataMonth);
            }
            else{
              monthData = monthData.concat(val);
            }
          }
          item.monthwise_details = monthData;
      }

      if(data["data"].length == 0 && this.isBilledArEntriesAdded == false){
        let itemIdsInArr1 = this.projectData.map(obj => obj.item_id);

      // Find items in arr2 not found in arr1
      const itemsNotFoundInArr1 = Object.values(this.billedData).filter(obj => !itemIdsInArr1.includes(obj["item_id"]));
      console.log("itemsNotFoundInArr1")
      console.log(itemsNotFoundInArr1)
      this.isBilledArEntriesAdded = true;
      }
    }
    this.projectData = this.projectData.concat(data["data"]);
    console.log(this.projectData)
    console.log(data)
    console.log(data["data"])
    
  }


  async getRRPositionData(id) {
    let data =
      await this._rrService.getRRPositionData(
        id,
        this.selectedCurrency,
        this.filterConfig
      );
    console.log(data);
    if(data["data"]){
      for(let item of data["data"]){
        let monthWiseData = item.monthwise_details;
        monthWiseData = monthWiseData ? monthWiseData : [];
        let monthData = [];
          for (let month of this.months) {
            let val = _.filter(monthWiseData,{"month": parseInt(moment(month, "MMM - YYYY").format("MM"))})
            console.log("valval")
            console.log(val)
            if(val.length == 0){
              let dataMonth = [{month: parseInt(moment(month, "MMM - YYYY").format("MM"))}];
              monthData.push(dataMonth);
            }
            else{
              monthData = monthData.concat(val);
            }
          }
          item.monthwise_details = monthData;
      }
    }
    this.positionData = this.positionData.concat(data["data"]);
    console.log(this.positionData)
    console.log(data)
    console.log(data["data"])
  }

  async getRREmployeeData(position_id, project_id) {
    let data =
      await this._rrService.getRREmployeeData(
        position_id,
        project_id,
        this.selectedCurrency,
        this.filterConfig
      );
    console.log(data);
    if(data["data"]){
      for(let item of data["data"]){
        let monthWiseData = item.monthwise_details;
        monthWiseData = monthWiseData ? monthWiseData : [];
        let monthData = [];
          for (let month of this.months) {
            let val = _.filter(monthWiseData,{"month": parseInt(moment(month, "MMM - YYYY").format("MM"))})
            console.log("valval")
            console.log(val)
            if(val.length == 0){
              let dataMonth = [{month: parseInt(moment(month, "MMM - YYYY").format("MM"))}];
              monthData.push(dataMonth);
            }
            else{
              monthData = monthData.concat(val);
            }
          }
          item.monthwise_details = monthData;
      }
    }
    this.empData = this.empData.concat(data["data"]);
    console.log(this.empData)
    console.log(data)
    console.log(data["data"])
  }

  getTenantInfo() {
    return new Promise((resolve, reject) => {
      this._rrService.getTenantInfo().subscribe(
        (res: any) => {
          resolve(res);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  async openUdrfModal() {
    const { UdrfModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/udrf-modal/udrf-modal.component'
    );

    this.dialog.open(UdrfModalComponent, {
      minWidth: '100%',
      height: '80%',
      position: { top: '0px', left: '77px' },
      disableClose: true,
    });
  }

  async toggleProjectExpansion(project: any) {
    console.log("loader")
    let id = project.project_id
    if (!this.positionData.some((data: any) => data.project_id === id) && !project.loading) { 
      project.loading = true; 
      await this.getRRPositionData(id);   
      project.loading = false;
    }
    
    project.expanded = !project.expanded;
  }

  roundAmount(amount: any) {
    // if (amount && !isNaN(amount) && amount !=0 ) {
    //   return `${parseFloat(amount).toFixed(2)}`;
    // }
    // return `${0}`;

    return amount;
  }
  
  async togglePositionExpansion(position: any, project: any) {
    if (position.emp_total > 0) {
      let id = position.position_id
      let project_id = project.project_id
      if (!this.empData.some((data: any) => data.position_id === id && data.project_id == project_id) && !position.loading) {
        position.loading = true;
        await this.getRREmployeeData(id, project_id);
        position.loading = false;
      }
      // if(_.filter(this.empData, {"project_id": project_id}))
      position.expanded = !position.expanded;
    }
  }

  selectedCurrency: string = 'USD'; // Default to INR

  selectedCurrencyId = 1;
  async selectCurrency(currency: string) {
    this.selectedCurrency = currency;
    this.selectedCurrencyId = currency == "USD" ? 1 : 2;
    // await this.getRRProjectData(this.filterConfig)
  }


  async openColumnCustomization(){
    // const { ColumnCustomizationComponent } = await import(
    //   '../../components/column-customization/column-customization.component'
    // ); 
    console.log("inside dialog open")
    let columnConfig = {
      isToShowBudget: this.isToShowBudget,
      isToShowAllocated: this.isToShowAllocated,
      isToShowActual: this.isToShowActual,
      isToShowBalance: this.isToShowBalance,
      isToShowBilled: this.isToShowBilled,
      isToShowUBR: this.isToShowUBR,
      isToShowAR: this.isToShowAR,
      isToShowMonth: this.isToShowMonth
    };

    let dialogref = this.dialog.open(ColumnCustomizationComponent, {
      width: '400px',
      height: '420px',
      disableClose: true,
      data: columnConfig
    });
    dialogref.afterClosed().subscribe(result => {
      // Check if result is not null (meaning the dialog was closed with data)
      if (result) {
        // Update the flags in the parent component with the result
        this.isToShowBudget = result.isToShowBudget;
        this.isToShowAllocated = result.isToShowAllocated;
        this.isToShowActual = result.isToShowActual;
        this.isToShowBalance = result.isToShowBalance;
        this.isToShowBilled = result.isToShowBilled;
        this.isToShowUBR = result.isToShowUBR;
        this.isToShowAR = result.isToShowAR;
        this.isToShowMonth = result.isToShowMonth;
      }});
  }
  
  spinner = false;
  async onScrollDown()
  { 
    console.log("inside scrolled")
    this.startIndex += 8;
    // this.spinner = true;
    this.isFilterApplied = this.filterConfig?.mainFilterArray && this.filterConfig?.mainFilterArray?.length > 0 ? true : false;
    let data =
      await this._rrService.getRRProjectData(
        this.startIndex,
        this.pageSize,
        this.filterConfig,
        this.selectedCurrency
      );
    console.log(data);
    // this.spinner = false;
    if(data["data"]){
      for(let item of data["data"]){
        let monthWiseData = item.monthwise_details;
        monthWiseData = monthWiseData ? monthWiseData : [];
        let monthData = null;
        monthData = [];
          for (let month of this.months) {
            let val = _.filter(monthWiseData, (p) => p.month == parseInt(moment(month, "MMM - YYYY").format("MM")))
            if(val.length == 0){
              let dataMonth = [{month: parseInt(moment(month, "MMM - YYYY").format("MM"))}];
              monthData.push(dataMonth);
            }
            else{
              monthData = monthData.concat(val);
            }
          }
          item.monthwise_details = monthData;
      }

      if(data["data"].length == 0 && this.isBilledArEntriesAdded == false){
        let itemIdsInArr1 = this.projectData.map(obj => obj.item_id);

      // Find items in arr2 not found in arr1
      const itemsNotFoundInArr1 = Object.values(this.billedData).filter(obj => !itemIdsInArr1.includes(obj["item_id"]));
      console.log("itemsNotFoundInArr1")
      console.log(itemsNotFoundInArr1)
      this.isBilledArEntriesAdded = true;
      }
    }
    this.projectData = this.projectData.concat(data["data"]);
    console.log(this.projectData)
    console.log(data)
    console.log(data["data"])
  }

  startMonth = 4;
  endMonth = 3;
  // Function to get an array of month-year pairs between start and end dates
  async getMonthYearPairs(startDate: string, endDate: string) {
  // Parse start and end dates using moment
  const startMoment = moment(startDate);
  const endMoment = moment(endDate);
  this.startMonth = parseInt(moment(startDate).format("MM"));
  this.endMonth = parseInt(moment(endDate).format("YYYY"));
  console.log("loop")
  console.log(startDate)
  console.log(endDate)
  console.log(startMoment)
  console.log(endMoment)

  // Initialize an array to store month-year pairs
  const monthYearPairs: string[] = [];

  // Loop through the months between start and end dates
  while (startMoment.isBefore(endMoment) || startMoment.isSame(endMoment, 'month')) {
    // Format the month-year pair and add it to the array
    monthYearPairs.push(startMoment.format('MMM - YYYY'));
    // Move to the next month
    startMoment.add(1, 'month');
  }

  // Return the array of month-year pairs
  return monthYearPairs;
}

onCloseReport(){
  this.$route.navigateByUrl("/main/reports");
}

indexCalculate(month,index){
  let monthNo = parseInt(index) + this.startMonth;
  if(monthNo == month){
    return true;
  }
  console.log("monthNo")
  console.log(monthNo)
  console.log("month")
  console.log(month)
  return false;
}

getDetailsForMonth(index,monthYear,monthwise_details): any {
  return monthwise_details.find(detail => detail.month === parseInt(moment(monthYear,"MMM - YYYY").format("MM")));
}

}
