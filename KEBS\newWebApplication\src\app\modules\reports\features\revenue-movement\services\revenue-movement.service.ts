import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
@Injectable({
  providedIn: 'root'
})
export class RevenueMovementService {

  constructor(private http: HttpClient) { }

  
  getMilestoneData = (milestone_id) => {
    return this.http.post("/api/invoice/v2/getMilestoneDetailsForRevenueMovement", {milestoneId: milestone_id});
  };

    
  getMilestoneMaster = (startIndex, offSet, searchValue) => {
    return this.http.post("/api/invoice/v2/getMilestoneMaster", {
      startIndex: startIndex,
      offSet:offSet,
      searchText: searchValue
    });
  };

  updateRevenueDetails = (updatedData, OriginalData) => {
    return this.http.post("/api/invoice/v2/updateRevenueDetails", {
      data: updatedData,
      OriginalData: OriginalData
    });
  };

  getRevenueChangeLog(row_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/v2/getRevenueChangeLog", {id:row_id}).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    });
  }

  revenueMovementAuthorizationValidation() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/v2/revenueMovementAuthorizationValidation", {}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }
  
} 
