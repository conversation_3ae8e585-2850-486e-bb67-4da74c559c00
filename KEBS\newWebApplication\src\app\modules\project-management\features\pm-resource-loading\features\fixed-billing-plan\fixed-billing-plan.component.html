<div class="fixed-billing-plan">
    <div class="loader-container" *ngIf="isComponentLoading">
        <div class="custom-loader"></div>
    </div>
    <div class="body-class" *ngIf="!isComponentLoading">
      <div class="header" >
        <div class="header-start">
            <div class="back-icon" (click)="routeBack()">
              <mat-icon class="icn-class">navigate_before</mat-icon>
            </div>
            <span class="header-text">{{'resource_loading_plan_name' | checkLabel : this.formConfig: 'quote-card': 'Billing Plan'}} </span>
          </div>
          <div class="header-end">

          </div>
      </div>
      <div class="card-class">
        <div class="header-card">
          <div class="row content-long-text">
            <div class="content-id">
              <span>#{{ data?.quote_id ? data.quote_id : "-" }}</span>
              <!-- <div class="content-button">
                    <div class="circle"></div>
                    <span class="active-text">Active</span>
              </div> -->
              <!-- <div class="content-button-save" *ngIf="statusId == 1">
                <div class="circle-save"></div>
                <span class="save-text">Saved</span>
              </div> -->
            </div>
            <span class="content-long-header">{{
              data?.quote_name ? data.quote_name : "-"
            }}</span>
          </div>
          <mat-divider [vertical]="true"></mat-divider>
          <div class="row content-text">
            <div class="icon-class">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="14" cy="14" r="14" fill="#E7F9F9"/>
                <path d="M15.541 9.54688H20.637" stroke="#292D32" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path opacity="0.4" d="M7.05664 9.54688H12.1526" stroke="#292D32" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path opacity="0.4" d="M15.541 16.3281H20.637" stroke="#292D32" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path opacity="0.4" d="M15.541 20.5742H20.637" stroke="#292D32" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.1094 12.087V6.99805" stroke="#292D32" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7.05664 20.9992L12.1526 15.9102" stroke="#292D32" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12.1526 20.9992L7.05664 15.9102" stroke="#292D32" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                
            </div>
            <div class="text-content-class">
              <span class="content-value"
                >{{ data?.currency ? data.currency : "-" }} {{ (data?.quote_value ? data.quote_value : 0) | number : "1.2-2" }}
                </span
              >
              <span class="content-head">Quote Value</span>
            </div>
          </div>
          <mat-divider [vertical]="true"></mat-divider>
        
        
          <div class="row content-text">
            <div class="icon-class">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="14" cy="14" r="14" fill="#EEF9E8"/>
                <path d="M13.8496 14C15.7826 14 17.3496 12.433 17.3496 10.5C17.3496 8.567 15.7826 7 13.8496 7C11.9166 7 10.3496 8.567 10.3496 10.5C10.3496 12.433 11.9166 14 13.8496 14Z" stroke="#52C41A" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path opacity="0.4" d="M7.83594 20.9996C7.83594 18.2906 10.5309 16.0996 13.8489 16.0996C14.5209 16.0996 15.1719 16.1906 15.7809 16.3586" stroke="#52C41A" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20.848 18.1984C20.848 18.7234 20.7011 19.2204 20.4421 19.6404C20.2951 19.8924 20.1061 20.1164 19.8891 20.2984C19.3991 20.7394 18.755 20.9984 18.048 20.9984C17.026 20.9984 16.137 20.4524 15.654 19.6404C15.395 19.2204 15.248 18.7234 15.248 18.1984C15.248 17.3164 15.654 16.5254 16.298 16.0144C16.781 15.6294 17.39 15.3984 18.048 15.3984C19.595 15.3984 20.848 16.6514 20.848 18.1984Z" stroke="#52C41A" stroke-width="1.05" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16.9551 18.1977L17.6481 18.8907L19.1391 17.5117" stroke="#52C41A" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>                                                 
            </div>
            <div class="text-content-class">
              <span class="content-value"
                >{{ data?.original_balance ? data.original_balance : 0 }}
                </span
              >
              <span class="content-head">Planned Quantity</span>
            </div>
          </div>
          <!-- <mat-divider [vertical]="true"></mat-divider> -->
          <!-- <div class="row content-text">
            <div class="icon-class">
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="14" cy="14" r="14" fill="#FFF8EC"/>
                <path d="M8.39805 9.8C7.52305 10.969 6.99805 12.425 6.99805 14C6.99805 17.864 10.134 21 13.998 21C17.862 21 20.998 17.864 20.998 14C20.998 10.136 17.862 7 13.998 7C12.997 7 12.038 7.21 11.177 7.595" stroke="#FFBD3D" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16.7954 14.0004H16.8017" stroke="#FFBD3D" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M13.9946 14.0004H14.0009" stroke="#FFBD3D" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M11.1938 14.0004H11.2001" stroke="#FFBD3D" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>                                                                 
            </div>
            <div class="text-content-class">
              <span class="content-value"
                >{{ data?.remaining_quantity ? data.remaining_quantity : 0 }}
                </span
              >
              <span class="content-head">Remaining Quantity</span>
            </div>
          </div> -->
        </div>
        <div class="quote-card" *ngFor="let quote of data?.quoteList">
          <app-quote-billing-plan [quoteData]="quote" [formConfig]="formConfig" [is_deliverable]="is_deliverable"></app-quote-billing-plan>
        </div>
      </div>
    </div>
</div>