import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ReportDashboardServiceService {
  gridData:any = null;
  gridLoader:boolean = false;
  gridLabel:string = '';
  units:string = 'Hrs';
  groupBySelected: string = '';
  constructor(private _http: HttpClient) { }

  getReportDashboardConfig(reportId){
    return this._http.post('/api/pm/report/getReportDashboardConfiguration',{report_id: reportId})
  }

  getReportSummaryCardData(card_config){
    return this._http[card_config?.method](card_config?.url, card_config?.params)
  }

  getReportGridData(grid_config){
    return this._http[grid_config?.method](grid_config?.url, grid_config?.params)
  }

  getSavedConfig(report_id){
    return this._http.post('/api/pm/report/getReportSavedConfig',{report_id: report_id})
  }

  saveUserConfig(report_id,reportConfig,current_date){
    return this._http.post('/api/pm/report/saveReportUserConfig',{
      report_id: report_id,
      reportConfig: reportConfig,
      current_date: current_date
    })
  }

  getRegionList(){
    return this._http.post('/api/pm/report/regionList',{})
  }

  getDivision(region) {
    return this._http.post('/api/pm/report/getDivision', {
      region: region
    });
  }

  getSubDivision(region, division) {
    return this._http.post('/api/pm/report/getSubDivision', {
      region: region,
      division: division
    });
  }

}
