import { Component, NgM<PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import Swal from "sweetalert2";

@Component({
  selector: 'app-forward-to-recruitment',
  templateUrl: './forward-to-recruitment.component.html',
  styleUrls: ['./forward-to-recruitment.component.scss']
})
export class ForwardToRecruitmentComponent implements OnInit,OnDestroy {

  loaderObject : any = {
    isPageLoading: true,
    isForwardReqOrgLoading: false,
    isForwardReqRecruitmentLoading: false
  };
  requestData:any;
  requestId: number;
  rmFieldconfig:any;
  subs = new SubSink();
  tenantSpecificFeatures = {
    hasForwardToRecruitment: false
  }
  recruitmentDetails : any;
  forwardToRecruitmentForm = new FormGroup({
    job_code: new FormControl('')
  });
  fieldConfig: any = {};
  jobCodeMaster:any = [];
  editMode: boolean = true;
  projectHeadName: string;
  constructor(
    private _rmService: RmService,
    private _router: Router,
    private _reqDetailService:RmRequestDetailService,
    private _toaster: ToasterService,
    private PmInternalStakeholderService: PmInternalStakeholderService
  ) { }

  async ngOnInit() {
    this.loaderObject.isPageLoading = true;
    this.requestId = parseInt(this._router.url.split('/')[5]);
    await this.getRequestData();
    this.jobCodeMaster = await this.getJobCodeList();
    this.editMode = (this.requestData && this.requestData['request_status_id'] &&  this.requestData['request_status_id'] == 2) ? false : true;
    await this.handleRmConfig()
    await this.fetchRecruitmentData()
    this.loaderObject.isPageLoading = false;
  }
  async handleRmConfig() {
    this.rmFieldconfig = await this.retrieveRmConfig();
    let fieldConfig = (this.rmFieldconfig && this.rmFieldconfig['forward_to_recruitment_field_config'] && this.rmFieldconfig['forward_to_recruitment_field_config']['form_params']) ? this.rmFieldconfig['forward_to_recruitment_field_config']['form_params'] : [];
    if(fieldConfig && fieldConfig.length > 0){
      fieldConfig.forEach((val,i) => {
        this.fieldConfig[val.field_key] = val
      })
    }
    
    let reports_to_list = []
    let currentDate = moment().format('YYYY-MM-DD');
    if(this.fieldConfig && this.fieldConfig['project_head'] && this.fieldConfig['project_head']['is_active']){
      await this.PmInternalStakeholderService.getreportsToMaster(
        this.requestData?.item_id,
        currentDate
      ).then((res) => {
        
        reports_to_list = res['data'];
      });
  
      await this.PmInternalStakeholderService.getHeadForISA(
        this.requestData?.item_id
      ).then((res) => {
        
        if (res['data'].length > 0) {
          let data = res["data"][0]
          const head_name = reports_to_list.find(option => option.associate_id === data['aid']);
          this.projectHeadName = head_name ? (`${head_name.name} (AID ${head_name.associate_id ? head_name.associate_id : ''}) `) : null;
        }
      });
    }
    console.log('this.fieldConfig:',this.fieldConfig)
  }
  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._rmService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err) {
          resolve(res.data)
        }
      },(err) => {
        console.log(err)
      })
    })
  }

  getRequestData() {
    this.subs.sink = this._reqDetailService.getRequestData().subscribe((res: any) => {
      console.log('forward to recruitment sub',res)
      if(res) this.requestData = res
    },(err) => {
      console.log(err)
    })
  }

  forwardRecrutiment() {
    if(this.rmFieldconfig && this.rmFieldconfig['forward_to_recruitment_field_config'] && this.rmFieldconfig['forward_to_recruitment_field_config']['is_active'] && this.fieldConfig && this.fieldConfig['job_code']){
      if((this.forwardToRecruitmentForm.value.job_code == null || this.forwardToRecruitmentForm.value.job_code == '' || this.forwardToRecruitmentForm.value.job_code == undefined)){
        this._toaster.showWarning('Warning', `${this.fieldConfig['job_code']['field_label']} is Mandatory`);
        this.loaderObject.isForwardReqRecruitmentLoading = false
        return;
      }
    }

    Swal.fire({
      title: 'Forward to recruitment',
      text: "Are you sure about forwarding this request to recruitment ?",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, forward it!'
    }).then(async (result) => {
      if (result.isConfirmed) {
        this.loaderObject.isForwardReqRecruitmentLoading = true
        if(this.rmFieldconfig.isOpenApi){
          await this._rmService.forwardToRecruitmentOpenApi(this.requestId,this.forwardToRecruitmentForm.value.job_code).subscribe(async (res: any) => {
            if(!res.err) {

              this._toaster.showSuccess('Success','Request forwarded to recruitment !',3000)
              window.location.reload();
            }
            else {
              this._toaster.showError('Error','Something went wrong',3000)
            }
            this.loaderObject.isForwardReqRecruitmentLoading = false;
          },(err)=> {
            this.loaderObject.isForwardReqRecruitmentLoading = false
            console.log(err)
            if(err['error']['err']['msg'])
              this._toaster.showError('Failed',err['error']['err']['msg'],3000)
            else
              this._toaster.showError('Failed','Something went wrong',3000)
          })
          


        }
        else{
        await this._rmService.forwardToRecruitment(this.requestId).subscribe(async (res: any) => {
          if(!res.err) {
            this.requestData['recruitment_request_id'] = res['recruitment_request_id']
            this.requestData['STATUS'] = 'Closed'
            this.requestData['request_status_id'] = 2
            this.requestData['status_color'] = '#52C41A'
            this._reqDetailService.setRequestData(this.requestData)
            await this.fetchRecruitmentData()
            this._toaster.showSuccess('Success','Request forwarded to recruitment !',3000)
          }
          else {
            this._toaster.showError('Error','Something went wrong',3000)
          }
          this.loaderObject.isForwardReqRecruitmentLoading = false;
        },(err)=> {
          this.loaderObject.isForwardReqRecruitmentLoading = false
          console.log(err)
          if(err['error']['err']['msg'])
            this._toaster.showError('Failed',err['error']['err']['msg'],3000)
          else
            this._toaster.showError('Failed','Something went wrong',3000)
          })}
      }
    })
  }

  async fetchRecruitmentData() {
    if(this.requestData.recruitment_request_id) {
      this.subs.sink = await this._rmService.fetchRecruitmentData(this.requestData.recruitment_request_id)
      .subscribe((res: any) => {
        if(res['messType'] == 'S') {
          this.recruitmentDetails = res['data'][0]
        }
      },(err) => {
        console.log(err)
        this._toaster.showError("Error","Failed to fetch recruitment details",3000)
      })
    }
  }
  
  // async handleRmConfig() {
  //   let config = await this.retrieveRmConfig()
  //   this.tenantSpecificFeatures.hasForwardToRecruitment = 
  //   config['recruitment_forward'] ? config['recruitment_forward']['is_active'] : false;
  // }

  // retrieveRmConfig() {
  //   return new Promise((resolve,reject) => {
  //     this.subs.sink = this._rmService.retrieveRmConfig()
  //     .subscribe((res: any) => {
  //       if(!res.err) {
  //         resolve(res.data)
  //       }
  //     },(err) => {
  //       console.log(err)
  //     })
  //   })
  // }
  getJobCode(id){
    if (id) {
      const job = this.jobCodeMaster.find(job => job.id == id);
      return job ? job.name : "-";
    }
    return "-";
  }

  getJobCodeList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService.getJobCodeList().subscribe(
        (res: any) => {
          if(!res.err && res.data && res.data.length > 0) {
            resolve(res.data)
         } 
         else{
           resolve([]);
         }
        },
        (err) => {
          console.log(err);
          resolve([]);
        }
      );
    });
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe()
  }

}

import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  MatDatepickerModule,
  MatDatepickerToggle,
} from '@angular/material/datepicker';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { CostDetailsDialogComponent } from 'src/app/modules/shared-lazy-loaded-components/team-members/pages/cost-details-dialog/cost-details-dialog/cost-details-dialog.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { RmRequestDetailService } from '../../services/rm-request-detail.service';
import { SubSink } from 'subsink';
import { PmInternalStakeholderService } from 'src/app/modules/project-management/features/pm-details/features/pm-project-team/features/project-internal-stakeholders/services/pm-internal-stakeholder.service';
import moment from 'moment';
import {MatTooltipModule} from '@angular/material/tooltip';


// import { RmService } from 'src/app/modules/resource-management/services/rm.service';
@NgModule({
  declarations: [ForwardToRecruitmentComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    SharedComponentsModule,
    MatFormFieldModule,
    MatRadioModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatTooltipModule
  ],
  exports: [],
})
export class ForwardToRecruitmentModule {}
