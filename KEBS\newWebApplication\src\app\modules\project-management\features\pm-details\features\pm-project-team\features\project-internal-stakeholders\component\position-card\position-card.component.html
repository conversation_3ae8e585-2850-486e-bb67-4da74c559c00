<div class="card position-card" *ngIf="data && data.isVisible" [ngStyle]="{'display':(noQuote) ? 'none' : 'flex' }">
    <div class="row card-col-arr">
      <mat-icon class="arrow-icn" *ngIf="!data?.expand && parentData.length > 0" (click)="onExpandClick()" 
        >keyboard_arrow_right</mat-icon
      >
      <mat-icon class="arrow-icn" *ngIf="data?.expand && parentData.length > 0" (click)="onExpandClick()"
        >keyboard_arrow_down</mat-icon
      >
    </div>
    <div class="row card-col text-class"  [ngStyle]="{'max-width': getMaxWidth('position_id', 'position-card')}" *ngIf="'position_id' | checkActive : this.formConfig : 'position-card' ">
      <div class="d-flex">
        <span class="data-id-header"
        >{{('position_id' | checkLabel : this.formConfig: 'position-card': 'Position')}} : {{ data.position_id ? data.position_id : "-" }}</span
      >
      <div class="status-chip" [ngStyle]="{color: data.primary_color, 'background-color': data.secondary_color}" *ngIf="'position_status' | checkActive : this.formConfig : 'position-card' " tooltip = "{{('position_status' | checkLabel : this.formConfig: 'position-card': 'Position')}}">
        {{data.status_name ? data.status_name : '-'}}
      </div>
      </div>
      <span class="data-id-body" tooltip ="{{data.position_name ? data.position_name : '-'}}">{{
        data.position_name ? data.position_name : "-"
      }}</span>
    </div>
    <!-- <mat-divider [vertical]="true"></mat-divider>
    <div class="row card-col">
      <span class="data-header">Rate</span>
      <span class="data-body"
        >{{ data.currency ? data.currency : "-" }}
        {{ data.rate ? data.rate : "-" }}</span
      >
    </div> -->
    <mat-divider [vertical]="true" *ngIf="'quantity' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('quantity', 'position-card')}"  *ngIf="'quantity' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('quantity' | checkLabel : this.formConfig: 'position-card': 'Quantity')}}</span>
      <span class="data-body" > <span tooltip ="{{parentData.length > 0 ? parentData.length : 0}}">{{ parentData.length > 0 ? parentData.length : 0 }}</span></span>
    </div>
    <mat-divider [vertical]="true" *ngIf="'position_quantity' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('position_quantity', 'position-card')}"  *ngIf="'position_quantity' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('position_quantity' | checkLabel : this.formConfig: 'position-card': 'Position Quantity')}}</span>
      <span class="data-body" ><span tooltip ="{{data.position_quantity ? (data.position_quantity | decimal:1 ) : 0}}">{{ data.position_quantity ? (data.position_quantity | decimal:1 ) : 0 }}</span></span>
    </div>
    <mat-divider [vertical]="true" *ngIf="'allocated_hrs' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('allocated_hrs', 'position-card')}"  *ngIf="'allocated_hrs' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('allocated_hrs' | checkLabel : this.formConfig: 'position-card': 'Allocated')}}</span>
      <span class="data-body" ><span tooltip ="{{data.consumed ? (data.consumed | decimal:1 ) : 0 }}">{{ data.consumed ? (data.consumed | decimal:1 ) : 0 }}</span></span>
    </div>
    <mat-divider [vertical]="true" *ngIf="'remaining_hrs' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('remaining_hrs', 'position-card')}"  *ngIf="'remaining_hrs' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('remaining_hrs' | checkLabel : this.formConfig: 'position-card': 'Remaining')}}</span>
      <div class="remaining-info-class"  (click)="viewBalanceLog.toggle();getRemainingLog()" [satPopoverAnchor]="viewBalanceLog" *ngIf="'remaining_info' | checkActive : this.formConfig : 'position-card' ">
        <span class="data-body"><span tooltip ="{{data.remaining ? (data.remaining | decimal:1 ) : 0}}">{{ data.remaining ? (data.remaining | decimal:1 ) : 0 }}</span></span>
        <div class="info-svg">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.4375 11.5625H8.5625V7.25H7.4375V11.5625ZM8 5.96637C8.17163 5.96637 8.3155 5.90831 8.43162 5.79219C8.54775 5.67606 8.60581 5.53219 8.60581 5.36056C8.60581 5.18894 8.54775 5.04506 8.43162 4.92894C8.3155 4.81294 8.17163 4.75494 8 4.75494C7.82837 4.75494 7.6845 4.81294 7.56838 4.92894C7.45225 5.04506 7.39419 5.18894 7.39419 5.36056C7.39419 5.53219 7.45225 5.67606 7.56838 5.79219C7.6845 5.90831 7.82837 5.96637 8 5.96637ZM8.00131 15.125C7.01581 15.125 6.0895 14.938 5.22237 14.564C4.35525 14.19 3.601 13.6824 2.95962 13.0413C2.31825 12.4002 1.81044 11.6463 1.43619 10.7795C1.06206 9.91275 0.875 8.98669 0.875 8.00131C0.875 7.01581 1.062 6.0895 1.436 5.22237C1.81 4.35525 2.31756 3.601 2.95869 2.95962C3.59981 2.31825 4.35375 1.81044 5.2205 1.43619C6.08725 1.06206 7.01331 0.875 7.99869 0.875C8.98419 0.875 9.9105 1.062 10.7776 1.436C11.6448 1.81 12.399 2.31756 13.0404 2.95869C13.6818 3.59981 14.1896 4.35375 14.5638 5.2205C14.9379 6.08725 15.125 7.01331 15.125 7.99869C15.125 8.98419 14.938 9.9105 14.564 10.7776C14.19 11.6448 13.6824 12.399 13.0413 13.0404C12.4002 13.6818 11.6463 14.1896 10.7795 14.5638C9.91275 14.9379 8.98669 15.125 8.00131 15.125ZM8 14C9.675 14 11.0938 13.4187 12.2563 12.2563C13.4187 11.0938 14 9.675 14 8C14 6.325 13.4187 4.90625 12.2563 3.74375C11.0938 2.58125 9.675 2 8 2C6.325 2 4.90625 2.58125 3.74375 3.74375C2.58125 4.90625 2 6.325 2 8C2 9.675 2.58125 11.0938 3.74375 12.2563C4.90625 13.4187 6.325 14 8 14Z" fill="#111434"/>
          </svg>            
        </div>
      </div>
      <span class="data-body" *ngIf="!('remaining_info' | checkActive : this.formConfig : 'position-card')"><span tooltip ="{{data.remaining ? (data.remaining | decimal:1 ) : 0}}">{{ data.remaining ? (data.remaining | decimal:1 ) : 0 }}</span></span>
    </div>
    <mat-divider [vertical]="true" *ngIf="'total_billed_hours' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('total_billed_hours', 'position-card')}"  *ngIf="'total_billed_hours' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('total_billed_hours' | checkLabel : this.formConfig: 'position-card': 'Total Billable hours')}}</span>
      <div class="info-class">
        <span class="data-body"><span tooltip ="{{data.total_billed_hours ? (data.total_billed_hours | decimal:1 ) : 0}}">{{ data.total_billed_hours ? (data.total_billed_hours | decimal:1 ) : 0 }}</span></span>
        <mat-icon class="info-icon-class" style="cursor: pointer;" *ngIf="data.month_wise_billed_hours?.length > 0"
        [matMenuTriggerFor]="viewActualMenu">info</mat-icon>
      </div>
    </div>
    <mat-divider [vertical]="true" *ngIf="'per_hour_rate' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('per_hour_rate', 'position-card')}"  *ngIf="'per_hour_rate' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('per_hour_rate' | checkLabel : this.formConfig: 'position-card': 'Per Hour Rate')}}</span>
      <span class="data-body" ><span [tooltip]="data?.quote_currency + ' ' + (data.per_hour_rate ? (data.per_hour_rate | decimal:2 ) : 0)">
        {{ data?.quote_currency }} {{ data.per_hour_rate ? (data.per_hour_rate | decimal:2 ) : 0 }}
      </span></span>
    </div>
    <mat-divider [vertical]="true" *ngIf="'work_location' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('work_location', 'position-card')}"  *ngIf="'work_location' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('work_location' | checkLabel : this.formConfig: 'position-card': 'Work Location')}}</span>
      <span class="data-body" ><span tooltip ="{{data.work_location ? data.work_location : '' }}">{{ data.work_location ? data.work_location : '' }}</span></span>
    </div>
    <mat-divider [vertical]="true" *ngIf="'service_line' | checkActive : this.formConfig : 'position-card' "></mat-divider>
    <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('service_line', 'position-card')}"  *ngIf="'service_line' | checkActive : this.formConfig : 'position-card' ">
      <span class="data-header">{{('service_line' | checkLabel : this.formConfig: 'position-card': 'Service Line')}}</span>
      <span class="data-body" ><span tooltip ="{{data.service_line ? data.service_line : '' }}">{{ data.service_line ? data.service_line : '' }}</span></span>
    </div>
    <mat-divider [vertical]="true" *ngIf="('milestone_name' | checkActive: formConfig : 'position-card') && isMilestoneVisible"></mat-divider>

    <div class="row card-col" 
         [ngStyle]="{'max-width': getMaxWidth('milestone_name', 'position-card')}" 
         *ngIf="('milestone_name' | checkActive: formConfig : 'position-card') && isMilestoneVisible">
         
      <span class="data-header">{{ 'milestone_name' | checkLabel: formConfig : 'position-card' : 'Milestone' }}</span>
    
      <span class="data-body">
        <span [tooltip]="data.milestone_name || '-'">{{ data.milestone_name || '-' }}</span>
      </span>
    </div>
    
    <mat-divider [vertical]="true"></mat-divider>
    <div class="row card-col-arr">
      <div class="add-people" *ngIf="!withOpportunity">
        <mat-icon tooltip="Allocate Resource" class="add-icon" (click)="addMember()"
          >person_add</mat-icon
        >
      </div>
    </div>
  </div>
  <div class="isa-detail" *ngIf="( data && data.isVisible && data?.expand && getChildData(data.position_id).length > 0 && parentData.length > 0) || noQuote">
    <div class="row sticky-top isa-header">
      <div class="column-expand">
        <span class="expand">
          <mat-icon class="expand-icon" (click)="collapseAll()" *ngIf="allExpand"
            >keyboard_arrow_down</mat-icon
          >
          <mat-icon class="expand-icon" (click)="expandAll()" *ngIf="!allExpand"
            >keyboard_arrow_right</mat-icon
          >
        </span>
      </div>
      <div class="column-AID" *ngIf="_isService.columns[1].isVisible">
        
        <span class="header-text">
          {{ _isService.columns[1].name }}
        </span>
      </div>
      <div class="column-external" *ngIf="_isService.columns[2]?.isVisible && enable_external_employee_id">
        <div class="external_id">
          {{ columns[2].name  }}
        </div>
      </div>
      <div class="column-name" *ngIf="_isService.columns[3].isVisible">
        <span class="header-text">
          {{ _isService.columns[3].name }}
        </span>
      </div>
      <div class="column-cc" *ngIf="_isService.columns[4].isVisible">
        <span class="header-text">
          {{ _isService.columns[4].name }}
        </span>
      </div>
      <div class="column-commercial" *ngIf="_isService.columns[5].isVisible">
        <span class="header-text">
          {{ _isService.columns[5].name }}
        </span>
      </div>
      <div class="column-role" *ngIf="_isService.columns[6].isVisible">
        <span class="header-text">
          {{ _isService.columns[6].name }}
        </span>
      </div>
      <div class="column-allocated" *ngIf="_isService.columns[7].isVisible">
        <span class="header-text" tooltip="{{_isService.columns[7].name}}">
          {{ _isService.columns[7].name }}
        </span>
      </div>
      <div class="column-start-date" *ngIf="_isService.columns[8].isVisible">
        <span class="header-text">
          {{ _isService.columns[8].name }}
        </span>
      </div>
      <div class="column-end-date" *ngIf="_isService.columns[9].isVisible">
        <span class="header-text">
          {{ _isService.columns[9].name }}
        </span>
      </div>
      <div class="column-status" *ngIf="_isService.columns[10].isVisible">
        <span class="header-text">
          {{ _isService.columns[10].name }}
        </span>
      </div>
      <div class="column-action" *ngIf="_isService.columns[11].isVisible">
        <span class="header-text">
          {{ _isService.columns[11].name }}
        </span>
      </div>
    </div>
    <div class="isa-body">
      <ng-template
            cdkConnectedOverlay
            [cdkConnectedOverlayOrigin]="triggerOrigin"
            [cdkConnectedOverlayOpen]="logPopUp"
            [cdkConnectedOverlayHasBackdrop]="true"
            (backdropClick)="logPopUp = false"
            cdkConnectedOverlayBackdropClass="overLayClass"
            [cdkConnectedOverlayFlexibleDimensions]="true"
            [cdkConnectedOverlayPositions]="[
              { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },
              { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' }
            ]"
          >
          <div class="card balance-planned-log-pop-over" [ngStyle]="{ 'min-height': isPlannedDataLoading ? '240px' : 'auto' }">
            <div *ngIf="popUpPlannedLogData && popUpPlannedLogData?.monthwise_planned_actual_data?.length > 0 && !isPlannedDataLoading">
              <div class="pop-up-class">
                <div class="header">
                  <span class="width-class"><span class="allocated-ellipsis" tooltip="{{('planned_info_header' | checkLabel : this.formConfig: 'position-card': 'Current Revenue Period')}}">{{('planned_info_header' | checkLabel : this.formConfig: 'position-card': 'Current Revenue Period')}}</span></span>
                  <span class="width-space-class"> :</span>
                  <span class="width-class sub-class" tooltip="{{popUpPlannedLogData.current_revenue_month ? popUpPlannedLogData.current_revenue_month : '-'}}"><span class="allocated-ellipsis" >{{popUpPlannedLogData.current_revenue_month ? popUpPlannedLogData.current_revenue_month : '-'}}</span></span>
                </div>
                <div class="header sub">
                  <span class="width-class"><span class="allocated-ellipsis" tooltip="{{('planned_info_sub_header' | checkLabel : this.formConfig: 'position-card': 'Planned Allocated Hours')}}">{{('planned_info_sub_header' | checkLabel : this.formConfig: 'position-card': 'Planned Allocated Hours')}}</span></span>
                  <span class="width-space-class"> :</span>
                  <span class="width-class sub-class"><span  class="allocated-ellipsis" tooltip="{{planned_allocated_hours ? (planned_allocated_hours | decimal:1 ) : 0}}">{{planned_allocated_hours ? (planned_allocated_hours | decimal:1 ) : 0}}</span></span>
                </div>
                <div class="scroll-grid-class">
                  <div class="row grid-class top">
                    <div class="col-3 p-0 m-0 grid-cell-header">
                      <span class="overflow-class"  tooltip ="{{('planned_info_month' | checkLabel : this.formConfig: 'position-card': 'Month-Year')}}">{{('planned_info_month' | checkLabel : this.formConfig: 'position-card': 'Month-Year')}}</span>
                      </div>
                    <div class="col-3 p-0 m-0 grid-cell-header">
                      <span  class="overflow-class"  tooltip ="{{('planned_info_plan' | checkLabel : this.formConfig: 'position-card': 'Planned')}}">{{('planned_info_plan' | checkLabel : this.formConfig: 'position-card': 'Planned')}}</span>
                    </div>
                    <div class="col-3  p-0 m-0 grid-cell-header">
                      <span  class="overflow-class"  tooltip ="{{('planned_info_actual' | checkLabel : this.formConfig: 'position-card': 'Actual')}}">{{('planned_info_actual' | checkLabel : this.formConfig: 'position-card': 'Actual')}}</span>
                    </div>
                    <div class="col-3  p-0 m-0 grid-cell-header last">
                      <span  class="overflow-class"  tooltip ="{{('planned_info_contribution' | checkLabel : this.formConfig: 'position-card': 'Contribution')}}">{{('planned_info_contribution' | checkLabel : this.formConfig: 'position-card': 'Contribution')}}</span>
                    </div>
                  </div>
                  <div class="row grid-class scroll" *ngFor="let plan of popUpPlannedLogData?.monthwise_planned_actual_data">
                    <div class="col-3 p-0 m-0 grid-cell start">
                      <span  class="overflow-class" tooltip="{{plan.month ? plan.month : '-'}}">{{plan.month ? plan.month : '-'}}</span>
                      </div>
                    <div class="col-3 p-0 m-0 grid-cell end " [ngClass]="plan.is_actual_given ? 'disabled' : ''">
                      <span  class="overflow-class text-right" tooltip="{{plan.planned_hours ? (plan.planned_hours  | decimal:1 ) : '-'}}">{{plan.planned_hours ? (plan.planned_hours  | decimal:1 ) : '-'}}</span>
                      </div>
                    <div class="col-3  p-0 m-0 grid-cell end">
                      <span  class="overflow-class text-right" tooltip="{{plan.actual_hours ? (plan.actual_hours  | decimal:1 ) : '-'}}">{{plan.actual_hours ? (plan.actual_hours  | decimal:1 ) : '-'}}</span>
                    </div>
                    <div class="col-3  p-0 m-0 grid-cell end last">
                      <span class="overflow-class text-right" tooltip="{{plan.contribution_to_remaining ? (plan.contribution_to_remaining   | decimal:1 ) : 0}}">{{plan.contribution_to_remaining ? (plan.contribution_to_remaining   | decimal:1 ) : 0}}</span>
                    </div>
                  </div>
                  <div class="row grid-class bottom">
                    <div class="col-3 p-0 m-0 grid-cell-header footer"></div>
                    <div class="col-3 p-0 m-0 grid-cell-header footer">
                      <span  class="overflow-class text-right" tooltip="{{popUpPlannedLogData.total_planned_hours ? (popUpPlannedLogData.total_planned_hours | decimal:1 ) : 0}}"> {{popUpPlannedLogData.total_planned_hours ? (popUpPlannedLogData.total_planned_hours | decimal:1 ) : 0}}</span>
                    </div>
                    <div class="col-3  p-0 m-0 grid-cell-header footer">
                      <span  class="overflow-class text-right"  tooltip="{{popUpPlannedLogData.total_actual_hours ? (popUpPlannedLogData.total_actual_hours | decimal:1 ) : 0}}">{{popUpPlannedLogData.total_actual_hours ? (popUpPlannedLogData.total_actual_hours | decimal:1 ) : 0}}</span>
                    </div>
                    <div class="col-3  p-0 m-0 grid-cell-header footer last">
                      <span  class="overflow-class text-right"  tooltip="{{popUpPlannedLogData.total_contribution_hours ? (popUpPlannedLogData.total_contribution_hours   | decimal:1 ) : 0}}">{{popUpPlannedLogData.total_contribution_hours ? (popUpPlannedLogData.total_contribution_hours   | decimal:1 ) : 0}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="no-log-class" *ngIf="(!popUpPlannedLogData || popUpPlannedLogData?.monthwise_planned_actual_data?.length == 0 ) && !isPlannedDataLoading">
              <svg width="80" height="80" viewBox="0 0 248 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M247.798 162.281H0V162.405H247.798V162.281Z" fill="#EBEBEB"/>
                <path d="M222.969 170.258H215.584V170.382H222.969V170.258Z" fill="#EBEBEB"/>
                <path d="M164.151 171.602H159.843V171.726H164.151V171.602Z" fill="#EBEBEB"/>
                <path d="M213.491 165.656H196.546V165.78H213.491V165.656Z" fill="#EBEBEB"/>
                <path d="M54.3318 168.215H32.9258V168.339H54.3318V168.215Z" fill="#EBEBEB"/>
                <path d="M66.4651 168.215H58.7451V168.339H66.4651V168.215Z" fill="#EBEBEB"/>
                <path d="M126.255 166.551H79.8301V166.675H126.255V166.551Z" fill="#EBEBEB"/>
                <path d="M117.463 140.178H21.7631C21.013 140.177 20.2939 139.879 19.7636 139.349C19.2332 138.818 18.9349 138.099 18.9341 137.349V2.828C18.9351 2.07812 19.2336 1.35927 19.7639 0.829121C20.2942 0.298968 21.0132 0.000793618 21.7631 0H117.463C118.213 0.00105816 118.931 0.299348 119.462 0.829472C119.992 1.3596 120.29 2.07829 120.291 2.828V137.347C120.291 138.097 119.993 138.817 119.463 139.347C118.932 139.878 118.213 140.177 117.463 140.178ZM21.7641 0.124001C21.0486 0.126111 20.3631 0.411253 19.8572 0.917145C19.3513 1.42304 19.0662 2.10857 19.0641 2.824V137.347C19.0662 138.062 19.3513 138.748 19.8572 139.254C20.3631 139.76 21.0486 140.045 21.7641 140.047H117.464C118.18 140.045 118.865 139.76 119.371 139.254C119.877 138.748 120.162 138.062 120.164 137.347V2.828C120.162 2.11256 119.877 1.42703 119.371 0.921143C118.865 0.415251 118.18 0.130113 117.464 0.128002L21.7641 0.124001Z" fill="#EBEBEB"/>
                <path d="M224.659 140.178H128.959C128.209 140.177 127.49 139.879 126.96 139.348C126.43 138.818 126.132 138.099 126.131 137.349V2.828C126.132 2.07821 126.43 1.35936 126.96 0.829182C127.49 0.299001 128.209 0.000794128 128.959 0H224.659C225.409 0.000793618 226.128 0.298968 226.658 0.829121C227.188 1.35927 227.487 2.07812 227.488 2.828V137.347C227.488 138.097 227.19 138.817 226.659 139.348C226.129 139.879 225.409 140.177 224.659 140.178ZM128.959 0.124001C128.243 0.126111 127.558 0.411253 127.052 0.917145C126.546 1.42304 126.261 2.10857 126.259 2.824V137.347C126.261 138.062 126.546 138.748 127.052 139.254C127.558 139.76 128.243 140.045 128.959 140.047H224.659C225.375 140.046 226.062 139.762 226.569 139.256C227.076 138.75 227.362 138.063 227.364 137.347V2.828C227.362 2.1117 227.076 1.42542 226.569 0.919384C226.062 0.41335 225.375 0.128793 224.659 0.128002L128.959 0.124001Z" fill="#EBEBEB"/>
                <path d="M106.293 162.277H115.522L115.522 98.9053H106.293V162.277Z" fill="#E6E6E6"/>
                <path d="M109.542 162.278H106.293V150.238H112.948L109.542 162.278Z" fill="#F0F0F0"/>
                <path d="M203.887 162.277H213.116V98.9053H203.887V162.277Z" fill="#E6E6E6"/>
                <path d="M106.293 158.992H206.722V98.9072L106.293 98.9072L106.293 158.992Z" fill="#F0F0F0"/>
                <path d="M203.477 162.278H206.726V150.238H200.067L203.477 162.278Z" fill="#F0F0F0"/>
                <path d="M173.612 103.878V153.582H203.383V103.878H173.612Z" fill="#E6E6E6"/>
                <path d="M185.553 105.628H191.443C193.936 105.653 196.348 104.747 198.207 103.086H178.79C180.649 104.746 183.061 105.653 185.553 105.628V105.628Z" fill="#F0F0F0"/>
                <path d="M141.622 103.878V153.582H171.393V103.878H141.622Z" fill="#E6E6E6"/>
                <path d="M153.262 105.628H159.152C161.644 105.653 164.056 104.746 165.915 103.086H146.498C148.357 104.747 150.769 105.653 153.262 105.628V105.628Z" fill="#F0F0F0"/>
                <path d="M109.632 103.878V153.582H139.403V103.878H109.632Z" fill="#E6E6E6"/>
                <path d="M121.272 105.628H127.162C129.654 105.653 132.067 104.747 133.926 103.086H114.508C116.367 104.747 118.779 105.653 121.272 105.628V105.628Z" fill="#F0F0F0"/>
                <path d="M34.6823 162.277H97.6523L97.6523 20.7253H34.6823L34.6823 162.277Z" fill="#F0F0F0"/>
                <path d="M34.6822 162.277H96.3232L96.3232 20.7253H34.6822L34.6822 162.277Z" fill="#F5F5F5"/>
                <path d="M92.5669 159.066L92.5669 23.9375L38.4379 23.9375L38.4379 159.066H92.5669Z" fill="#F0F0F0"/>
                <path d="M87.2483 49.0695C87.2483 47.1316 86.8666 45.2126 86.125 43.4222C85.3834 41.6318 84.2964 40.005 82.9261 38.6347C81.5558 37.2644 79.929 36.1774 78.1386 35.4358C76.3482 34.6942 74.4292 34.3125 72.4913 34.3125H58.5153C54.6015 34.3125 50.848 35.8673 48.0805 38.6347C45.3131 41.4022 43.7583 45.1557 43.7583 49.0695H87.2483Z" fill="#FAFAFA"/>
                <path d="M87.248 150.669V118.387H43.758V150.669H87.248Z" fill="#F5F5F5"/>
                <path d="M87.2479 58.1719H68.0649V114.221H87.2479V58.1719Z" fill="#F5F5F5"/>
                <path d="M62.9413 58.1719H43.7583V114.221H62.9413V58.1719Z" fill="#F5F5F5"/>
                <path d="M50.7152 85.9419H42.8582C42.5351 85.9411 42.2255 85.8124 41.9971 85.584C41.7686 85.3555 41.64 85.046 41.6392 84.7229V84.7229C41.6402 84.3999 41.769 84.0905 41.9974 83.8621C42.2257 83.6337 42.5352 83.505 42.8582 83.5039H50.7152C51.0381 83.505 51.3476 83.6337 51.576 83.8621C51.8043 84.0905 51.9331 84.3999 51.9342 84.7229V84.7229C51.9334 85.046 51.8047 85.3555 51.5762 85.584C51.3478 85.8124 51.0382 85.9411 50.7152 85.9419Z" fill="#E0E0E0"/>
                <path d="M39.4131 159.066L39.4131 23.9375H38.4391L38.4391 159.066H39.4131Z" fill="#F0F0F0"/>
                <path d="M137.482 83.3711L204.261 83.3711V20.7271L137.482 20.7271V83.3711Z" fill="#E6E6E6"/>
                <path d="M135.799 83.3711L202.578 83.3711V20.7271L135.799 20.7271V83.3711Z" fill="#F5F5F5"/>
                <path d="M131.9 86.9961L204.261 86.9961V83.3701L131.9 83.3701V86.9961Z" fill="#E6E6E6"/>
                <path d="M130.005 86.9961L198.639 86.9961V83.3701L130.005 83.3701V86.9961Z" fill="#F5F5F5"/>
                <path d="M199.556 80.1114V23.9844L138.82 23.9844V80.1114H199.556Z" fill="white"/>
                <path d="M170.871 80.1114V23.9844H167.504V80.1114H170.871Z" fill="#F5F5F5"/>
                <path d="M140.093 80.1114V23.9844H138.82V80.1114H140.093Z" fill="#E6E6E6"/>
                <path d="M172.144 80.1114V23.9844H170.871V80.1114H172.144Z" fill="#E6E6E6"/>
                <path d="M146.46 85.0322C145.232 72.3072 144.377 51.6582 145.041 44.7522C145.052 44.6335 145.089 44.5186 145.148 44.4152C145.207 44.3117 145.288 44.2221 145.385 44.1522C145.513 44.0582 145.665 44.0022 145.824 43.9906C145.983 43.9789 146.141 44.0121 146.282 44.0863C146.423 44.1605 146.54 44.2728 146.62 44.4104C146.7 44.5479 146.74 44.7051 146.735 44.8642C146.618 48.2862 146.483 56.8392 147.212 71.5942C147.575 78.9392 148.098 83.9692 147.739 85.0302L146.46 85.0322Z" fill="#E6E6E6"/>
                <path d="M141.881 55.7004C138.381 51.8174 131.242 49.0564 123.964 49.0704C121.107 49.0764 132.657 53.5874 137.464 58.5704C141.306 62.7083 144.468 67.4284 146.833 72.5554C145.905 64.9504 145.378 59.5814 141.881 55.7004Z" fill="#E6E6E6"/>
                <path d="M148.813 56.8837C151.713 52.8397 158.42 49.7298 165.673 49.3778C168.52 49.2398 157.687 54.3148 153.644 59.5168C150.419 63.8646 147.968 68.7359 146.397 73.9168C146.185 66.3038 145.909 60.9287 148.813 56.8837Z" fill="#E6E6E6"/>
                <path d="M141.725 41.5203C138.764 38.2323 132.716 35.8943 126.553 35.9063C124.134 35.9063 133.914 39.7323 137.981 43.9473C141.234 47.4511 143.912 51.4479 145.915 55.7893C145.133 49.3553 144.684 44.8083 141.725 41.5203Z" fill="#E6E6E6"/>
                <path d="M147.594 46.3818C150.053 42.9568 155.73 40.3238 161.871 40.0258C164.282 39.9088 155.109 44.2058 151.685 48.6108C148.953 52.2943 146.877 56.4215 145.548 60.8108C145.37 54.3588 145.135 49.8048 147.594 46.3818Z" fill="#E6E6E6"/>
                <path d="M146.953 98.9082C145.108 98.9082 143.332 98.2025 141.99 96.9356C140.649 95.6687 139.842 93.9366 139.736 92.0943L139.098 81.0312H154.808L154.17 92.0943C154.064 93.9366 153.258 95.6687 151.916 96.9356C150.574 98.2025 148.799 98.9082 146.953 98.9082Z" fill="#E6E6E6"/>
                <path d="M155.825 83.5453H138.084L137.576 78.9453H156.337L155.825 83.5453Z" fill="#E6E6E6"/>
                <path d="M123.899 184.665C176.969 184.665 219.99 182.153 219.99 179.053C219.99 175.954 176.969 173.441 123.899 173.441C70.8295 173.441 27.8081 175.954 27.8081 179.053C27.8081 182.153 70.8295 184.665 123.899 184.665Z" fill="#F5F5F5"/>
                <path d="M125.838 173.689L121.7 174.155L120.174 164.771L124.312 164.305L125.838 173.689Z" fill="#FFC3BD"/>
                <path d="M144.364 174.592H140.232L139.203 165.023H143.335L144.364 174.592Z" fill="#FFC3BD"/>
                <path d="M139.892 174.109H144.929C145.011 174.109 145.09 174.137 145.154 174.189C145.218 174.24 145.263 174.311 145.281 174.391L146.097 178.064C146.116 178.154 146.114 178.247 146.093 178.335C146.071 178.424 146.03 178.507 145.971 178.578C145.913 178.649 145.84 178.705 145.757 178.744C145.674 178.782 145.583 178.801 145.492 178.799C143.871 178.771 142.692 178.676 140.648 178.676C139.392 178.676 135.604 178.806 133.87 178.806C132.17 178.806 131.909 177.091 132.619 176.936C135.803 176.236 138.202 175.279 139.219 174.363C139.404 174.198 139.644 174.108 139.892 174.109Z" fill="#263238"/>
                <path d="M121.632 173.444L126.116 172.936C126.197 172.927 126.28 172.945 126.349 172.989C126.418 173.032 126.471 173.098 126.498 173.176L127.723 176.733C127.754 176.819 127.764 176.911 127.754 177.002C127.744 177.093 127.714 177.18 127.665 177.257C127.616 177.335 127.55 177.4 127.472 177.447C127.394 177.495 127.306 177.524 127.215 177.533C125.595 177.689 123.247 177.859 121.215 178.09C118.838 178.36 118.442 178.996 115.638 178.931C113.938 178.891 113.512 177.166 114.225 177.031C117.473 176.4 118.101 175.909 120.682 173.883C120.951 173.651 121.28 173.499 121.632 173.444V173.444Z" fill="#263238"/>
                <path d="M125.282 53.1746C123.894 54.3206 122.528 55.3166 121.115 56.3366C119.702 57.3566 118.259 58.3036 116.774 59.2226C115.289 60.1416 113.765 61.0226 112.167 61.8226C110.533 62.6689 108.832 63.3787 107.082 63.9446C106.619 64.0926 106.168 64.2146 105.648 64.3446C105.139 64.4548 104.625 64.5373 104.108 64.5916C103.177 64.6916 102.301 64.7366 101.428 64.7766C99.6876 64.8566 97.9806 64.8546 96.2756 64.8426C92.8656 64.8016 89.4846 64.6376 86.0886 64.3376L86.0376 61.2376C89.3726 60.8086 92.7226 60.4376 96.0376 60.0646C97.6946 59.8846 99.3476 59.6826 100.963 59.4716C101.763 59.3626 102.563 59.2346 103.301 59.0976C103.618 59.0416 103.932 58.9678 104.241 58.8766C104.532 58.7766 104.889 58.6626 105.219 58.5296C106.636 57.9542 108.011 57.2813 109.336 56.5156C110.707 55.7286 112.079 54.8836 113.427 53.9856C114.775 53.0876 116.117 52.1576 117.447 51.2086C118.777 50.2596 120.106 49.2546 121.354 48.3086L125.282 53.1746Z" fill="#FFC3BD"/>
                <path d="M129.321 49.6424C128.727 53.5484 120.503 59.5234 120.503 59.5234L114.603 52.2004C117.188 49.4421 119.928 46.8342 122.811 44.3894C125.466 42.2194 129.967 45.3924 129.321 49.6424Z" fill="#526179"/>
                <path d="M87.1758 61.5645L84.8298 59.8945L84.5728 65.1705C84.9274 65.1715 85.2768 65.0856 85.5906 64.9203C85.9044 64.7551 86.1729 64.5155 86.3728 64.2225L87.1758 61.5645Z" fill="#FFC3BD"/>
                <path d="M82.711 58.3594L82.231 63.5304L84.575 65.1714L84.832 59.8954L82.711 58.3594Z" fill="#FFC3BD"/>
                <path opacity="0.2" d="M120.176 164.775L120.962 169.612L125.101 169.146L124.315 164.309L120.176 164.775Z" fill="black"/>
                <path opacity="0.2" d="M143.337 165.027H139.203L139.735 169.959H143.869L143.337 165.027Z" fill="black"/>
                <path d="M143.396 44.5152C143.703 44.5842 143.994 44.7141 144.251 44.8972C144.508 45.0803 144.726 45.3129 144.891 45.5813C145.057 45.8498 145.167 46.1486 145.215 46.4603C145.264 46.772 145.249 47.0902 145.173 47.3962C144.276 51.1177 143.6 54.8889 143.149 58.6902C142.758 61.8902 142.524 64.9472 142.378 67.7182C142.035 74.2132 142.178 79.1642 141.984 80.8422C138.753 80.6182 126.577 79.7712 119.938 79.3072C117.697 61.7632 120.323 50.5672 121.798 46.0182C122.024 45.3094 122.446 44.6793 123.016 44.2013C123.586 43.7233 124.28 43.417 125.018 43.3182C125.864 43.2072 126.876 43.1002 127.944 43.0372C128.318 43.0132 128.697 43.0002 129.083 42.9922C132.217 43.0293 135.347 43.247 138.456 43.6442C139.046 43.7142 139.642 43.8012 140.222 43.8942C141.381 44.0882 142.483 44.3112 143.396 44.5152Z" fill="#6E7B8F"/>
                <path d="M138.265 33.0312C137.465 36.0052 136.487 41.4892 138.457 43.6492C133.347 47.0862 130.085 51.9253 129.144 50.8743C128.651 50.3243 128.437 43.9593 129.085 42.9953C132.504 42.4273 132.591 39.9262 132.169 37.5422L138.265 33.0312Z" fill="#FFC3BD"/>
                <path d="M138.061 42.418C138.061 42.418 129.734 46.103 128.63 54.343C130.924 51.687 135.991 48.797 135.991 48.797L133.756 48.155C135.177 47.6155 136.649 47.2196 138.149 46.973C137.901 45.835 139.223 43.565 139.223 43.565L138.061 42.418Z" fill="#526179"/>
                <path d="M130.548 42.2852C130.548 42.2852 131.658 46.9912 128.631 54.3432C128.034 51.7771 127.215 49.2677 126.185 46.8432L128.224 47.3982C128.224 47.3982 128.124 46.4492 126.473 45.1872C127.161 44.1879 128.019 43.317 129.008 42.6142L130.548 42.2852Z" fill="#526179"/>
                <path opacity="0.2" d="M135.78 34.875L132.172 37.543C132.279 38.0982 132.337 38.6617 132.345 39.227C133.645 39.127 135.505 37.844 135.736 36.509C135.849 35.9715 135.864 35.4178 135.78 34.875V34.875Z" fill="black"/>
                <path d="M127.684 24.4191C125.669 24.5061 124.557 27.9801 126.602 30.8411C128.647 33.7021 130.399 24.3021 127.684 24.4191Z" fill="#263238"/>
                <path d="M137.621 27.4779C137.86 31.5719 138.208 33.9549 136.406 36.2879C133.696 39.7969 128.456 38.5389 126.999 34.5989C125.688 31.0529 125.517 24.9539 129.299 22.8589C130.131 22.3925 131.068 22.1466 132.022 22.1445C132.976 22.1425 133.914 22.3844 134.748 22.8473C135.582 23.3101 136.284 23.9786 136.786 24.7891C137.289 25.5995 137.576 26.5251 137.621 27.4779V27.4779Z" fill="#FFC3BD"/>
                <path d="M138.222 30.6231C137.127 29.9813 136.171 29.1282 135.409 28.1134C134.647 27.0986 134.095 25.9424 133.785 24.7121C132.585 24.9961 126.604 27.7481 124.765 24.7121C122.926 21.6761 125.24 19.8951 128.681 21.7641C127.159 19.0781 128.543 17.7641 133.325 17.6481C138.107 17.5321 137.464 20.3041 137.464 20.3041C137.464 20.3041 141.084 18.7281 142.364 21.5041C143.847 24.6941 141.111 30.1411 138.222 30.6231Z" fill="#263238"/>
                <path d="M136.646 19.5693C136.69 19.5693 138.457 19.9943 140.493 17.9453C140.128 19.6933 136.966 20.8573 136.966 20.8573L136.646 19.5693Z" fill="#263238"/>
                <path d="M139.022 25.5609C137.115 24.9019 134.806 27.7269 135.659 31.1379C136.512 34.5489 141.591 26.4489 139.022 25.5609Z" fill="#263238"/>
                <path d="M140.101 30.8834C139.926 31.9001 139.356 32.8062 138.515 33.4034C137.394 34.1854 136.384 33.2914 136.315 31.9974C136.257 30.8334 136.769 29.0234 138.076 28.7564C138.363 28.7028 138.658 28.7217 138.936 28.8114C139.214 28.9012 139.465 29.0589 139.666 29.2702C139.867 29.4815 140.012 29.7398 140.088 30.0215C140.164 30.3033 140.169 30.5995 140.101 30.8834V30.8834Z" fill="#FFC3BD"/>
                <path d="M126.152 125.863C127.145 113.777 134.429 80.3196 134.429 80.3196L119.935 79.3086C119.935 79.3086 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z" fill="#526179"/>
                <path opacity="0.5" d="M126.152 125.863C127.145 113.777 134.429 80.3196 134.429 80.3196L119.935 79.3086C119.935 79.3086 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z" fill="white"/>
                <path opacity="0.2" d="M128.969 91.6484C126.047 95.3844 125.995 108.505 127.125 118.199C128.202 111.206 129.899 102.325 131.386 94.9224L128.969 91.6484Z" fill="black"/>
                <path d="M127.756 79.8516C127.756 79.8516 129.32 115.227 131.065 127.207C132.975 140.326 137.826 168.999 137.826 168.999H145.394C145.394 168.999 144.149 141.993 143.15 129.104C142.013 114.423 141.982 80.8436 141.982 80.8436L127.756 79.8516Z" fill="#526179"/>
                <path opacity="0.5" d="M127.756 79.8516C127.756 79.8516 129.32 115.227 131.065 127.207C132.975 140.326 137.826 168.999 137.826 168.999H145.394C145.394 168.999 144.149 141.993 143.15 129.104C142.013 114.423 141.982 80.8436 141.982 80.8436L127.756 79.8516Z" fill="white"/>
                <path d="M136.916 169.159H145.827V166.497L135.866 166.324L136.916 169.159Z" fill="#526179"/>
                <path d="M118.44 168.136L126.724 167.197L126.424 164.52L117.147 165.429L118.44 168.136Z" fill="#526179"/>
                <path d="M131.593 29.3547C131.633 29.6857 131.493 29.9747 131.273 30.0007C131.053 30.0267 130.849 29.7797 130.81 29.4487C130.771 29.1177 130.91 28.8287 131.13 28.8027C131.35 28.7767 131.554 29.0237 131.593 29.3547Z" fill="#263238"/>
                <path d="M127.854 29.8039C127.894 30.1349 127.754 30.4238 127.534 30.4498C127.314 30.4758 127.11 30.2289 127.07 29.8979C127.03 29.5669 127.17 29.2779 127.39 29.2519C127.61 29.2259 127.814 29.4769 127.854 29.8039Z" fill="#263238"/>
                <path d="M129.062 29.8242C128.789 30.8829 128.372 31.8991 127.822 32.8442C128.067 32.9786 128.339 33.0579 128.618 33.0763C128.897 33.0948 129.177 33.0521 129.438 32.9512L129.062 29.8242Z" fill="#ED847E"/>
                <path d="M132.61 28.0068C132.575 28.0173 132.538 28.0181 132.503 28.0091C132.468 28.0001 132.436 27.9817 132.41 27.9558C132.245 27.7739 132.038 27.6338 131.809 27.5469C131.579 27.46 131.331 27.4288 131.087 27.4558C131.036 27.4644 130.984 27.4529 130.941 27.4239C130.898 27.3948 130.869 27.3503 130.858 27.2998C130.853 27.2745 130.853 27.2484 130.858 27.223C130.863 27.1977 130.873 27.1736 130.887 27.1523C130.902 27.1309 130.921 27.1127 130.942 27.0987C130.964 27.0847 130.988 27.0752 131.014 27.0708C131.322 27.0308 131.636 27.0656 131.928 27.1721C132.22 27.2786 132.483 27.4537 132.693 27.6828C132.729 27.72 132.749 27.7697 132.749 27.8213C132.749 27.8729 132.729 27.9225 132.693 27.9598C132.67 27.9821 132.641 27.9983 132.61 28.0068V28.0068Z" fill="#263238"/>
                <path d="M131.173 34.2719C131.645 34.1727 132.086 33.9613 132.459 33.6557C132.832 33.35 133.126 32.9591 133.316 32.5159C133.325 32.4894 133.323 32.4603 133.311 32.435C133.299 32.4097 133.277 32.3902 133.251 32.3809C133.224 32.3717 133.195 32.3733 133.17 32.3855C133.144 32.3977 133.125 32.4194 133.116 32.4459C132.934 32.8536 132.658 33.2128 132.312 33.4945C131.966 33.7762 131.558 33.9727 131.122 34.0679C131.108 34.071 131.096 34.0767 131.084 34.0847C131.073 34.0927 131.063 34.1029 131.056 34.1146C131.049 34.1263 131.044 34.1393 131.042 34.1529C131.039 34.1665 131.04 34.1805 131.043 34.1939C131.046 34.2074 131.052 34.2201 131.06 34.2314C131.068 34.2426 131.078 34.2522 131.089 34.2595C131.101 34.2669 131.114 34.2718 131.128 34.2741C131.141 34.2764 131.155 34.276 131.169 34.2729L131.173 34.2719Z" fill="#263238"/>
                <path d="M126.095 28.7073C126.056 28.7205 126.013 28.7205 125.974 28.7073C125.926 28.6895 125.886 28.6536 125.863 28.6072C125.841 28.5608 125.837 28.5074 125.853 28.4583C125.947 28.1617 126.111 27.8921 126.331 27.6724C126.551 27.4527 126.821 27.2894 127.118 27.1963C127.168 27.185 127.221 27.1934 127.265 27.2198C127.309 27.2463 127.341 27.2887 127.355 27.3383C127.366 27.3884 127.358 27.4409 127.332 27.485C127.306 27.5291 127.263 27.5615 127.214 27.5753V27.5753C126.981 27.6532 126.769 27.7853 126.597 27.9609C126.424 28.1365 126.296 28.3505 126.223 28.5853C126.212 28.6141 126.195 28.6401 126.173 28.6613C126.151 28.6824 126.124 28.6982 126.095 28.7073Z" fill="#263238"/>
                <path d="M138.531 174.833C138.248 174.855 137.966 174.781 137.731 174.622C137.657 174.558 137.601 174.478 137.566 174.388C137.531 174.298 137.519 174.201 137.531 174.105C137.531 174.047 137.546 173.991 137.576 173.943C137.605 173.894 137.648 173.854 137.699 173.828C138.158 173.592 139.489 174.412 139.639 174.506C139.655 174.516 139.668 174.531 139.676 174.549C139.684 174.567 139.687 174.586 139.684 174.606C139.68 174.625 139.672 174.642 139.659 174.657C139.645 174.671 139.628 174.681 139.61 174.686C139.257 174.775 138.895 174.824 138.531 174.833V174.833ZM137.917 173.978C137.873 173.976 137.83 173.984 137.79 174.002C137.769 174.013 137.753 174.03 137.742 174.05C137.731 174.07 137.726 174.094 137.728 174.117C137.719 174.182 137.726 174.249 137.749 174.311C137.773 174.373 137.811 174.428 137.861 174.472C138.322 174.682 138.847 174.709 139.328 174.546C138.897 174.266 138.416 174.073 137.912 173.978H137.917Z" fill="#526179"/>
                <path d="M139.584 174.689C139.567 174.689 139.551 174.685 139.537 174.677C139.11 174.445 138.283 173.539 138.37 173.077C138.384 173.005 138.423 172.94 138.479 172.893C138.536 172.847 138.607 172.821 138.681 172.82C138.755 172.811 138.831 172.818 138.902 172.84C138.974 172.861 139.041 172.898 139.098 172.946C139.578 173.339 139.677 174.531 139.68 174.581C139.681 174.599 139.677 174.616 139.67 174.632C139.662 174.648 139.65 174.662 139.636 174.671C139.62 174.682 139.602 174.688 139.584 174.689ZM138.75 173.014H138.705C138.578 173.03 138.567 173.088 138.562 173.114C138.51 173.386 139.037 174.07 139.462 174.393C139.434 173.921 139.263 173.468 138.974 173.093C138.911 173.041 138.831 173.013 138.75 173.014V173.014Z" fill="#526179"/>
                <path d="M121.296 174.055L121.28 174.062C120.633 174.269 119.475 174.562 118.98 174.216C118.907 174.165 118.847 174.098 118.806 174.019C118.765 173.94 118.744 173.852 118.744 173.764C118.74 173.71 118.75 173.656 118.773 173.608C118.796 173.559 118.831 173.518 118.875 173.487C119.34 173.16 121.087 173.803 121.285 173.878C121.302 173.885 121.317 173.897 121.327 173.912C121.338 173.927 121.344 173.945 121.345 173.963C121.346 173.982 121.342 174 121.334 174.016C121.325 174.033 121.312 174.046 121.296 174.056V174.055ZM119.013 173.631L118.99 173.645C118.972 173.657 118.958 173.673 118.949 173.693C118.94 173.713 118.937 173.734 118.94 173.756C118.939 173.814 118.952 173.872 118.978 173.925C119.005 173.977 119.044 174.022 119.092 174.056C119.7 174.245 120.356 174.21 120.94 173.956C120.335 173.687 119.672 173.575 119.012 173.631H119.013Z" fill="#526179"/>
                <path d="M121.296 174.056C121.272 174.068 121.245 174.07 121.22 174.062C120.7 173.896 119.62 173.119 119.64 172.647C119.645 172.536 119.708 172.391 119.972 172.334C120.067 172.312 120.166 172.311 120.262 172.329C120.358 172.347 120.449 172.385 120.53 172.44C120.954 172.845 121.239 173.373 121.346 173.95C121.349 173.966 121.348 173.984 121.343 174C121.338 174.016 121.328 174.031 121.316 174.043C121.31 174.048 121.303 174.052 121.296 174.056V174.056ZM119.905 172.566C119.838 172.602 119.836 172.644 119.835 172.66C119.821 172.944 120.567 173.566 121.108 173.812C121.008 173.351 120.769 172.932 120.423 172.612C120.364 172.571 120.297 172.543 120.226 172.529C120.156 172.515 120.083 172.517 120.013 172.533C119.976 172.539 119.939 172.55 119.905 172.566Z" fill="#526179"/>
                <path d="M146.882 50.4757C146.777 52.0897 146.587 53.5927 146.39 55.1447C146.193 56.6967 145.928 58.2177 145.637 59.7527C145.061 62.8879 144.211 65.9664 143.096 68.9527L142.605 70.1097L142.481 70.3987L142.45 70.4707L142.399 70.5807L142.255 70.8657C142.064 71.212 141.836 71.536 141.574 71.8317C141.143 72.3168 140.639 72.7318 140.08 73.0617C139.627 73.3311 139.152 73.5616 138.661 73.7507C137.023 74.3415 135.31 74.699 133.573 74.8127C130.418 75.0483 127.247 74.9299 124.119 74.4597L124.232 71.3597L126.418 71.1127C127.151 71.0257 127.88 70.9127 128.605 70.8127C130.052 70.5997 131.484 70.3617 132.849 70.0447C134.111 69.7876 135.339 69.3807 136.505 68.8327C136.819 68.7005 137.094 68.4902 137.305 68.2217C137.318 68.1507 137.16 68.4617 137.346 67.9647L137.673 67.0087C138.474 64.268 139.086 61.4758 139.507 58.6517C139.742 57.2137 139.943 55.7627 140.137 54.3077C140.331 52.8527 140.491 51.3677 140.644 49.9727L146.882 50.4757Z" fill="#FFC3BD"/>
                <path d="M147.242 47.4803C149.265 50.8733 147.542 61.5373 147.542 61.5373L136.942 59.5783C136.942 59.5783 136.368 53.4783 138.386 48.7843C140.577 43.6753 144.791 43.3693 147.242 47.4803Z" fill="#526179"/>
                <path d="M121.264 80.9523L115.639 54.7883C115.369 53.671 114.743 52.6718 113.856 51.9413C112.968 51.2108 111.867 50.7885 110.719 50.7383H89.166C90.3145 50.7883 91.4156 51.2104 92.3033 51.941C93.1909 52.6715 93.817 53.6708 94.087 54.7883L99.712 80.9523C99.982 82.0696 100.608 83.0688 101.495 83.7993C102.383 84.5298 103.484 84.952 104.632 85.0023H126.184C125.036 84.952 123.935 84.5298 123.047 83.7993C122.16 83.0688 121.534 82.0696 121.264 80.9523V80.9523Z" fill="#263238"/>
                <path opacity="0.7" d="M121.264 80.9523L115.639 54.7883C115.369 53.671 114.743 52.6718 113.856 51.9413C112.968 51.2108 111.867 50.7885 110.719 50.7383H89.166C90.3145 50.7883 91.4156 51.2104 92.3033 51.941C93.1909 52.6715 93.817 53.6708 94.087 54.7883L99.712 80.9523C99.982 82.0696 100.608 83.0688 101.495 83.7993C102.383 84.5298 103.484 84.952 104.632 85.0023H126.184C125.036 84.952 123.935 84.5298 123.047 83.7993C122.16 83.0688 121.534 82.0696 121.264 80.9523V80.9523Z" fill="#526179"/>
                <path d="M89.1658 50.7383H110.718C109.614 50.7589 108.556 51.1819 107.743 51.9277C106.929 52.6735 106.416 53.6908 106.3 54.7883L100.709 115.588C100.592 116.686 100.079 117.703 99.2655 118.449C98.452 119.194 97.3942 119.617 96.2908 119.638H82.8388C82.3206 119.648 81.8064 119.547 81.3305 119.341C80.8546 119.136 80.4281 118.832 80.0795 118.448C79.7308 118.065 79.4682 117.611 79.309 117.118C79.1499 116.625 79.098 116.103 79.1568 115.588L84.7488 54.7883C84.8651 53.6909 85.3781 52.6738 86.1914 51.9281C87.0048 51.1823 88.0625 50.7592 89.1658 50.7383Z" fill="#526179"/>
                <path d="M92.6088 115.586C92.55 116.101 92.6019 116.622 92.7611 117.116C92.9202 117.609 93.1829 118.062 93.5315 118.446C93.8801 118.829 94.3066 119.134 94.7825 119.339C95.2584 119.544 95.7726 119.646 96.2908 119.636H74.7388C74.2206 119.646 73.7063 119.545 73.2303 119.339C72.7544 119.134 72.3278 118.83 71.9791 118.446C71.6304 118.063 71.3678 117.609 71.2087 117.116C71.0497 116.622 70.9979 116.101 71.0568 115.586H92.6088Z" fill="#263238"/>
                <path opacity="0.7" d="M92.6088 115.586C92.55 116.101 92.6019 116.622 92.7611 117.116C92.9202 117.609 93.1829 118.062 93.5315 118.446C93.8801 118.829 94.3066 119.134 94.7825 119.339C95.2584 119.544 95.7726 119.646 96.2908 119.636H74.7388C74.2206 119.646 73.7063 119.545 73.2303 119.339C72.7544 119.134 72.3278 118.83 71.9791 118.446C71.6304 118.063 71.3678 117.609 71.2087 117.116C71.0497 116.622 70.9979 116.101 71.0568 115.586H92.6088Z" fill="#526179"/>
                <g opacity="0.5">
                <path opacity="0.5" d="M102.565 57.5492H87.9807C87.8681 57.5508 87.7564 57.5284 87.6531 57.4834C87.5499 57.4385 87.4573 57.3722 87.3817 57.2887C87.306 57.2053 87.2489 57.1068 87.2143 56.9996C87.1796 56.8925 87.1682 56.7792 87.1807 56.6672V56.6672C87.2066 56.4285 87.3185 56.2073 87.4955 56.045C87.6725 55.8827 87.9026 55.7904 88.1427 55.7852H102.725C102.837 55.7837 102.949 55.8061 103.052 55.851C103.155 55.8959 103.248 55.9623 103.324 56.0457C103.399 56.1291 103.456 56.2277 103.491 56.3349C103.526 56.442 103.537 56.5553 103.525 56.6672V56.6672C103.499 56.9057 103.387 57.1266 103.211 57.2888C103.034 57.4511 102.804 57.5436 102.565 57.5492V57.5492Z" fill="white"/>
                <path opacity="0.5" d="M102.131 62.3188H87.5466C87.434 62.3203 87.3224 62.2979 87.2191 62.253C87.1158 62.208 87.0233 62.1417 86.9476 62.0583C86.8719 61.9749 86.8149 61.8763 86.7802 61.7691C86.7456 61.662 86.7341 61.5487 86.7466 61.4368C86.7725 61.198 86.8844 60.9768 87.0615 60.8145C87.2385 60.6522 87.4685 60.5599 87.7086 60.5548H102.293C102.405 60.5532 102.517 60.5756 102.62 60.6205C102.723 60.6655 102.816 60.7318 102.892 60.8152C102.967 60.8987 103.024 60.9972 103.059 61.1044C103.094 61.2115 103.105 61.3248 103.093 61.4368V61.4368C103.067 61.6756 102.955 61.8969 102.778 62.0592C102.601 62.2216 102.371 62.3138 102.131 62.3188V62.3188Z" fill="white"/>
                <path opacity="0.5" d="M98.1215 67.0922H87.1125C86.9999 67.0937 86.8883 67.0713 86.785 67.0264C86.6817 66.9815 86.5892 66.9151 86.5135 66.8317C86.4378 66.7483 86.3808 66.6497 86.3461 66.5426C86.3115 66.4354 86.3 66.3221 86.3125 66.2102V66.2102C86.3384 65.9714 86.4504 65.7503 86.6274 65.588C86.8044 65.4257 87.0344 65.3334 87.2745 65.3282H98.2835C98.3961 65.3267 98.5078 65.3491 98.6111 65.394C98.7143 65.4389 98.8069 65.5053 98.8825 65.5887C98.9582 65.6721 99.0152 65.7707 99.0499 65.8778C99.0846 65.985 99.096 66.0983 99.0835 66.2102V66.2102C99.0576 66.4489 98.9457 66.6701 98.7687 66.8324C98.5916 66.9947 98.3616 67.087 98.1215 67.0922V67.0922Z" fill="white"/>
                <path opacity="0.5" d="M90.4129 71.8617H86.6799C86.5673 71.8633 86.4557 71.8409 86.3524 71.7959C86.2491 71.751 86.1566 71.6847 86.0809 71.6012C86.0052 71.5178 85.9482 71.4193 85.9135 71.3121C85.8789 71.205 85.8674 71.0917 85.8799 70.9797V70.9797C85.9056 70.7411 86.0173 70.5199 86.1942 70.3576C86.371 70.1953 86.6009 70.1029 86.8409 70.0977H90.5749C90.6875 70.0962 90.7992 70.1186 90.9024 70.1635C91.0057 70.2084 91.0983 70.2748 91.1739 70.3582C91.2496 70.4416 91.3066 70.5402 91.3413 70.6474C91.3759 70.7545 91.3874 70.8678 91.3749 70.9797V70.9797C91.349 71.2185 91.2371 71.4396 91.06 71.6019C90.883 71.7642 90.653 71.8566 90.4129 71.8617V71.8617Z" fill="white"/>
                <path opacity="0.5" d="M100.829 76.6313H86.2409C86.1283 76.6328 86.0167 76.6104 85.9134 76.5655C85.8101 76.5206 85.7176 76.4542 85.6419 76.3708C85.5663 76.2874 85.5092 76.1888 85.4746 76.0816C85.4399 75.9745 85.4284 75.8612 85.4409 75.7493V75.7493C85.4669 75.5105 85.5788 75.2893 85.7558 75.127C85.9328 74.9647 86.1628 74.8724 86.4029 74.8673H100.987C101.1 74.8657 101.211 74.8881 101.314 74.9331C101.418 74.978 101.51 75.0443 101.586 75.1277C101.662 75.2112 101.719 75.3097 101.753 75.4169C101.788 75.524 101.799 75.6373 101.787 75.7493V75.7493C101.761 75.9874 101.65 76.208 101.474 76.3702C101.297 76.5324 101.068 76.6251 100.829 76.6313V76.6313Z" fill="white"/>
                <path opacity="0.5" d="M94.6517 81.4047H85.8117C85.6991 81.4063 85.5875 81.3838 85.4842 81.3389C85.3809 81.294 85.2884 81.2276 85.2127 81.1442C85.137 81.0608 85.08 80.9622 85.0454 80.8551C85.0107 80.7479 84.9992 80.6346 85.0117 80.5227V80.5227C85.0374 80.284 85.1491 80.0629 85.326 79.9006C85.5028 79.7383 85.7327 79.6459 85.9727 79.6407H94.8127C94.9253 79.6392 95.037 79.6616 95.1403 79.7065C95.2435 79.7514 95.3361 79.8178 95.4117 79.9012C95.4874 79.9846 95.5445 80.0832 95.5791 80.1903C95.6138 80.2975 95.6252 80.4108 95.6127 80.5227V80.5227C95.5868 80.7613 95.4751 80.9823 95.2983 81.1446C95.1214 81.3069 94.8917 81.3993 94.6517 81.4047V81.4047Z" fill="white"/>
                <path opacity="0.5" d="M99.9617 86.1742H85.3777C85.265 86.1758 85.1534 86.1534 85.0501 86.1084C84.9468 86.0635 84.8543 85.9972 84.7786 85.9137C84.703 85.8303 84.6459 85.7318 84.6113 85.6246C84.5766 85.5175 84.5652 85.4042 84.5777 85.2922V85.2922C84.6036 85.0535 84.7155 84.8323 84.8925 84.67C85.0695 84.5077 85.2996 84.4154 85.5397 84.4102H100.125C100.237 84.4087 100.349 84.4311 100.452 84.476C100.555 84.5209 100.648 84.5873 100.724 84.6707C100.799 84.7541 100.856 84.8527 100.891 84.9599C100.926 85.067 100.937 85.1803 100.925 85.2922V85.2922C100.899 85.5312 100.787 85.7524 100.609 85.9148C100.432 86.0771 100.202 86.1693 99.9617 86.1742V86.1742Z" fill="white"/>
                <path opacity="0.5" d="M95.9526 90.9438H84.9436C84.831 90.9453 84.7193 90.9229 84.616 90.878C84.5128 90.8331 84.4202 90.7667 84.3446 90.6833C84.2689 90.5999 84.2118 90.5013 84.1772 90.3941C84.1425 90.287 84.1311 90.1737 84.1436 90.0618V90.0618C84.1695 89.823 84.2814 89.6019 84.4584 89.4396C84.6355 89.2773 84.8655 89.1849 85.1056 89.1798H96.1146C96.2272 89.1782 96.3388 89.2006 96.4421 89.2456C96.5454 89.2905 96.6379 89.3568 96.7136 89.4403C96.7893 89.5237 96.8463 89.6222 96.881 89.7294C96.9156 89.8365 96.9271 89.9498 96.9146 90.0618V90.0618C96.8886 90.3005 96.7767 90.5217 96.5997 90.684C96.4227 90.8463 96.1927 90.9386 95.9526 90.9438V90.9438Z" fill="white"/>
                <path opacity="0.5" d="M88.2435 95.7133H84.5075C84.3949 95.7148 84.2833 95.6924 84.18 95.6475C84.0767 95.6026 83.9842 95.5362 83.9085 95.4528C83.8329 95.3694 83.7758 95.2708 83.7412 95.1637C83.7065 95.0565 83.695 94.9432 83.7075 94.8313V94.8313C83.7335 94.5925 83.8454 94.3714 84.0224 94.2091C84.1994 94.0468 84.4294 93.9544 84.6695 93.9493H88.4075C88.5202 93.9477 88.6318 93.9702 88.7351 94.0151C88.8384 94.06 88.9309 94.1264 89.0066 94.2098C89.0822 94.2932 89.1393 94.3918 89.1739 94.4989C89.2086 94.6061 89.22 94.7194 89.2075 94.8313C89.1816 95.0704 89.0694 95.2918 88.8919 95.4541C88.7145 95.6165 88.484 95.7086 88.2435 95.7133V95.7133Z" fill="white"/>
                <path opacity="0.5" d="M98.6599 100.487H84.0759C83.9633 100.488 83.8516 100.466 83.7484 100.421C83.6451 100.376 83.5525 100.31 83.4769 100.226C83.4012 100.143 83.3442 100.044 83.3095 99.9371C83.2749 99.83 83.2634 99.7167 83.2759 99.6047V99.6047C83.3018 99.366 83.4137 99.1448 83.5908 98.9825C83.7678 98.8202 83.9978 98.7279 84.2379 98.7227H98.8219C98.9345 98.7212 99.0462 98.7436 99.1494 98.7885C99.2527 98.8334 99.3453 98.8998 99.4209 98.9832C99.4966 99.0666 99.5536 99.1652 99.5883 99.2724C99.6229 99.3795 99.6344 99.4928 99.6219 99.6047V99.6047C99.596 99.8435 99.4841 100.065 99.307 100.227C99.13 100.389 98.9 100.482 98.6599 100.487V100.487Z" fill="white"/>
                <path opacity="0.5" d="M92.4828 105.256H83.6428C83.5302 105.258 83.4185 105.235 83.3153 105.19C83.212 105.146 83.1194 105.079 83.0438 104.996C82.9681 104.912 82.9111 104.814 82.8764 104.707C82.8418 104.599 82.8303 104.486 82.8428 104.374V104.374C82.8685 104.135 82.9804 103.914 83.1574 103.752C83.3345 103.589 83.5646 103.497 83.8048 103.492H92.6438C92.7564 103.491 92.8681 103.513 92.9713 103.558C93.0746 103.603 93.1672 103.669 93.2428 103.753C93.3185 103.836 93.3755 103.935 93.4102 104.042C93.4448 104.149 93.4563 104.262 93.4438 104.374V104.374C93.4179 104.613 93.3061 104.834 93.1293 104.996C92.9525 105.158 92.7227 105.251 92.4828 105.256V105.256Z" fill="white"/>
                <path opacity="0.5" d="M97.7917 110.026H83.2077C83.0951 110.027 82.9835 110.005 82.8802 109.96C82.7769 109.915 82.6844 109.849 82.6087 109.765C82.5331 109.682 82.476 109.583 82.4413 109.476C82.4067 109.369 82.3952 109.256 82.4077 109.144V109.144C82.4337 108.905 82.5456 108.684 82.7226 108.522C82.8996 108.359 83.1296 108.267 83.3697 108.262H97.9517C98.0643 108.26 98.176 108.283 98.2793 108.328C98.3825 108.373 98.4751 108.439 98.5508 108.522C98.6264 108.606 98.6835 108.704 98.7181 108.811C98.7528 108.919 98.7642 109.032 98.7517 109.144V109.144C98.7258 109.382 98.6142 109.603 98.4376 109.765C98.261 109.928 98.0315 110.02 97.7917 110.026Z" fill="white"/>
                </g>
                <path d="M125.503 71.7002L122.337 68.9492L120.853 74.0132C120.853 74.0132 124.069 75.7572 125.183 74.1562L125.503 71.7002Z" fill="#FFC3BD"/>
                <path d="M118.888 68.75L117.858 72.994L120.85 74.014L122.334 68.949L118.888 68.75Z" fill="#FFC3BD"/>
                </svg>
              <span class="no-data">No Info Available</span>
            </div>
            <div class="loader-class" *ngIf="isPlannedDataLoading">
              <mat-spinner class="green-spinner loader" diameter="20"></mat-spinner>
            </div>
          </div> 
          </ng-template>
      <div
        class="parent-data"
        *ngFor="let parent of parentData"
      >
        <div class="row item-header">
          <div class="column-expand">
            <span class="expand">
              <mat-icon class="expand-icon" (click)="collapse(parent['associate_id'])" *ngIf="parent['expand']"
                >keyboard_arrow_down</mat-icon
              >
              <mat-icon class="expand-icon" (click)="expand(parent['associate_id'])" *ngIf="!parent['expand']"
                >keyboard_arrow_right</mat-icon
              >
            </span>
          </div>
          <div class="column-AID" *ngIf="_isService.columns[1].isVisible">
            <span class="body-text">
              <span tooltip="{{parent['associate_id']}}">{{ parent["associate_id"] }}</span>
              <div style="margin-top: -1%" *ngIf="parent['reports_to_warning']">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_23160_6754" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                  <rect width="20" height="20" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_23160_6754)">
                  <path d="M1 21L12 2L23 21H1ZM12 18C12.2833 18 12.5208 17.9042 12.7125 17.7125C12.9042 17.5208 13 17.2833 13 17C13 16.7167 12.9042 16.4792 12.7125 16.2875C12.5208 16.0958 12.2833 16 12 16C11.7167 16 11.4792 16.0958 11.2875 16.2875C11.0958 16.4792 11 16.7167 11 17C11 17.2833 11.0958 17.5208 11.2875 17.7125C11.4792 17.9042 11.7167 18 12 18ZM11 15H13V10H11V15Z" fill="#FF3A46"/>
                  </g>
                  </svg>
              </div>
            </span>
            
          </div>
          <div class="column-external" *ngIf="_isService.columns[2]?.isVisible && enable_external_employee_id">
            <span class="body-text parent">
              <span tooltip="{{parent['external_employee_id']}}">{{ parent["external_employee_id"] }}</span>
            </span>
          </div>
          <div class="column-name" *ngIf="_isService.columns[3].isVisible" >
            <app-app-user-image
              class="profile-img"
              [id]="parent['oid']"
              imgWidth="18px"
              imgHeight="18px"
              style="margin-top: -4px"
            >
            </app-app-user-image>
            <span class="parent-name-txt" tooltip="{{ parent['name'] }}">{{ parent["name"] }}</span>
          </div>
          <div class="column-cc" *ngIf="_isService.columns[4].isVisible">
            <span class="body-text parent">
              {{ parent["split_percentage"] }}%
            </span>
          </div>
          <div class="column-commercial" *ngIf="_isService.columns[5].isVisible">
            <span class="body-text parent">
      
              {{parent['commercial']+" "+(this.showIdentity ? parent['identity_name'] :"")}}
            </span>
          </div>
          <div class="column-role" *ngIf="_isService.columns[6].isVisible">
            <span class="body-text parent">
              {{ parent["role"] }}
            </span>
          </div>
          <div class="column-allocated" *ngIf="_isService.columns[7].isVisible">
            <span class="body-text parent">
              {{ parent["allocated_hours"] }}
            </span>
          </div>
          <div class="column-start-date" *ngIf="_isService.columns[8].isVisible">
            <span class="body-text parent">
              {{ parent["start_date"] }}
            </span>
          </div>
          <div class="column-end-date" *ngIf="_isService.columns[9].isVisible">
            <span class="body-text parent">
              {{ parent["end_date"] }}
            </span>
          </div>
          <div class="column-status" *ngIf="_isService.columns[10].isVisible">
            <span
              class="body-text-status"
              [style.background]="parent['status_color'].palate"
              [style.color]="parent['status_color'].color"
            >
              {{ parent["status"] }}
            </span>
          </div>
        </div>
        <div class="item-body" *ngIf="parent['expand']">
          <div
            class="row"
            *ngFor="let child of getChildData(data.position_id); let i = index"
          >
            <div class="row  d-block pt-2 pb-2" *ngIf="parent['associate_id'] == child['associate_id']">
              <div class="row body-header">
                <div class="column-expand">
                  <!-- <mat-checkbox class="check-input" (change)="selectChildItem(child)"> </mat-checkbox> -->
                </div>
                <div class="column-AID" *ngIf="_isService.columns[1].isVisible">
                  <span class="body-text">
                    <!-- Show the person icon if head is true -->
                    <mat-icon *ngIf="child['head']" 
                              style="font-size: 140%; fill: currentColor; height: 20px; width: 20px;" 
                              class="person-icon" 
                              tooltip="Project Head">
                      person_outline
                    </mat-icon>
                    
                    <!-- Show associate_id with its tooltip -->
                    <span tooltip="{{ child['associate_id'] }}">
                      {{ child['associate_id'] }}
                    </span>
                    <div style="margin-top: -1%" tooltip="{{child['reports_message']}}" *ngIf="child['reports_to_warning']">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_23160_6754" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                        <rect width="20" height="20" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_23160_6754)">
                        <path d="M1 21L12 2L23 21H1ZM12 18C12.2833 18 12.5208 17.9042 12.7125 17.7125C12.9042 17.5208 13 17.2833 13 17C13 16.7167 12.9042 16.4792 12.7125 16.2875C12.5208 16.0958 12.2833 16 12 16C11.7167 16 11.4792 16.0958 11.2875 16.2875C11.0958 16.4792 11 16.7167 11 17C11 17.2833 11.0958 17.5208 11.2875 17.7125C11.4792 17.9042 11.7167 18 12 18ZM11 15H13V10H11V15Z" fill="#FF3A46"/>
                        </g>
                        </svg>
                    </div>
                  </span>
                  
                  
                </div>
                <div class="column-external" *ngIf="_isService.columns[2]?.isVisible && enable_external_employee_id">
                  <span class="body-text">
                    <span tooltip="{{child['external_employee_id']}}">{{ child["external_employee_id"] }}</span>
                  </span>
                </div>
                <div class="column-name" *ngIf="_isService.columns[3].isVisible">
                  <app-app-user-image
                    class="profile-img"
                    [id]="child['oid']"
                    imgWidth="18px"
                    imgHeight="18px"
                    style="margin-top: -4px"
                  >
                  </app-app-user-image>
                  <span class="name-text" tooltip="{{child['name']}}">{{ child["name"] }}</span>
                </div>
                <div class="column-cc" *ngIf="_isService.columns[4].isVisible">
                  <span class="body-text"> {{ (child["split_percentage"]) | decimal : 1 }}% </span>
                </div>
                <div
                  class="column-commercial"
                  *ngIf="_isService.columns[5].isVisible"
                >
                  <span class="body-text">
                    {{child['commercial']+" "+(this.showIdentity ? child['identity_name'] :"")}}
              
                  </span>
                </div>
                <div class="column-role" *ngIf="_isService.columns[6].isVisible">
                  <span class="body-text" tooltip="{{child['role']}}">
                    {{ child["role"] }}
                  </span>
                </div>
                <div
                  class="column-allocated"
                  *ngIf="_isService.columns[7].isVisible"
                >
                  <div class="log-class" (click)="toggleOverlay(trigger, data);getPlannedLog(child['isa_id'],child['allocated_hours'])" cdkOverlayOrigin #trigger="cdkOverlayOrigin" *ngIf="'planned_info' | checkActive : this.formConfig : 'position-card' ">
                    <span class="body-text info"  tooltip="{{child['allocated_hours'] | decimal:1}}">
                      <span class="info-class-overflow">{{ child["allocated_hours"] | decimal:1 }}</span>
                    </span>
                    <div class="info-svg">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7.4375 11.5625H8.5625V7.25H7.4375V11.5625ZM8 5.96637C8.17163 5.96637 8.3155 5.90831 8.43162 5.79219C8.54775 5.67606 8.60581 5.53219 8.60581 5.36056C8.60581 5.18894 8.54775 5.04506 8.43162 4.92894C8.3155 4.81294 8.17163 4.75494 8 4.75494C7.82837 4.75494 7.6845 4.81294 7.56838 4.92894C7.45225 5.04506 7.39419 5.18894 7.39419 5.36056C7.39419 5.53219 7.45225 5.67606 7.56838 5.79219C7.6845 5.90831 7.82837 5.96637 8 5.96637ZM8.00131 15.125C7.01581 15.125 6.0895 14.938 5.22237 14.564C4.35525 14.19 3.601 13.6824 2.95962 13.0413C2.31825 12.4002 1.81044 11.6463 1.43619 10.7795C1.06206 9.91275 0.875 8.98669 0.875 8.00131C0.875 7.01581 1.062 6.0895 1.436 5.22237C1.81 4.35525 2.31756 3.601 2.95869 2.95962C3.59981 2.31825 4.35375 1.81044 5.2205 1.43619C6.08725 1.06206 7.01331 0.875 7.99869 0.875C8.98419 0.875 9.9105 1.062 10.7776 1.436C11.6448 1.81 12.399 2.31756 13.0404 2.95869C13.6818 3.59981 14.1896 4.35375 14.5638 5.2205C14.9379 6.08725 15.125 7.01331 15.125 7.99869C15.125 8.98419 14.938 9.9105 14.564 10.7776C14.19 11.6448 13.6824 12.399 13.0413 13.0404C12.4002 13.6818 11.6463 14.1896 10.7795 14.5638C9.91275 14.9379 8.98669 15.125 8.00131 15.125ZM8 14C9.675 14 11.0938 13.4187 12.2563 12.2563C13.4187 11.0938 14 9.675 14 8C14 6.325 13.4187 4.90625 12.2563 3.74375C11.0938 2.58125 9.675 2 8 2C6.325 2 4.90625 2.58125 3.74375 3.74375C2.58125 4.90625 2 6.325 2 8C2 9.675 2.58125 11.0938 3.74375 12.2563C4.90625 13.4187 6.325 14 8 14Z" fill="#111434"/>
                      </svg>            
                    </div>
                  </div>
                  <span class="body-text"  *ngIf="!('planned_info' | checkActive : this.formConfig : 'position-card')">
                    <span tooltip="{{child['allocated_hours'] | decimal:1}}">{{ child["allocated_hours"] | decimal:1 }}</span>
                  </span>

                </div>
                <div
                  class="column-start-date"
                  *ngIf="_isService.columns[8].isVisible"
                >
                  <span class="body-text">
                    {{ child["start_date"] }}
                  </span>
                </div>
                <div
                  class="column-end-date"
                  *ngIf="_isService.columns[9].isVisible"
                >
                  <span class="body-text">
                    {{ child["end_date"] }}
                  </span>
                </div>
                <div
                  class="column-status"
                  *ngIf="_isService.columns[10].isVisible"
                >
                  <span
                    class="body-text-status"
                    [style.background]="child['status_color'].palate"
                    [style.color]="child['status_color'].color"
                  >
                    {{ child["status"] }}
                  </span>
                </div>
                <div
                  class="column-action"
                  *ngIf="_isService.columns[11].isVisible"
                >
                  <!-- <mat-icon class="action-icns" (click)="editMember(child, 'R')"
                    >delete_outline</mat-icon
                  > -->
                  <!-- <mat-icon class="action-icns" (click)="editMember(child, 'D')"
                    >library_add</mat-icon
                  > -->
                  <!-- <mat-icon class="action-icns" (click)="editMember(child, 'E')"
                    >edit
                  </mat-icon> -->
                  <div class="action-icns" (click)="editMember(child, 'R')">
                    <div *ngIf="!child['is_form_required']">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_19813_15919" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                          <rect width="16" height="16" fill="#D9D9D9" />
                        </mask>
                        <g mask="url(#mask0_19813_15919)">
                          <path
                            d="M7.99968 13.3315C8.59202 13.3315 9.16318 13.2379 9.71318 13.0507C10.2632 12.8636 10.7681 12.5897 11.2278 12.229C10.7595 11.8811 10.2525 11.6168 9.70668 11.436C9.1609 11.2552 8.59185 11.1649 7.99952 11.1649C7.40729 11.1649 6.83618 11.2531 6.28618 11.4295C5.73618 11.6061 5.23129 11.8726 4.77152 12.229C5.23129 12.5897 5.73618 12.8636 6.28618 13.0507C6.83618 13.2379 7.40735 13.3315 7.99968 13.3315ZM9.63818 7.75585L8.92535 7.04302C9.00224 6.94135 9.06163 6.82841 9.10352 6.70419C9.1454 6.57997 9.16635 6.45574 9.16635 6.33152C9.16635 6.01097 9.05202 5.73641 8.82335 5.50785C8.59479 5.27919 8.32024 5.16485 7.99968 5.16485C7.87546 5.16485 7.75124 5.1858 7.62702 5.22769C7.50279 5.26958 7.38985 5.32897 7.28818 5.40585L6.57535 4.69302C6.77179 4.51702 6.99157 4.38496 7.23468 4.29685C7.47768 4.20885 7.73363 4.16485 8.00252 4.16485C8.60229 4.16485 9.11291 4.37552 9.53435 4.79685C9.95568 5.2183 10.1663 5.72891 10.1663 6.32869C10.1663 6.59758 10.1223 6.85352 10.0343 7.09652C9.94624 7.33963 9.81418 7.55941 9.63818 7.75585ZM13.3227 11.4405L12.5933 10.711C12.8378 10.2956 13.0222 9.86008 13.1465 9.40452C13.2709 8.94897 13.333 8.48019 13.333 7.99819C13.333 6.5093 12.8163 5.24819 11.783 4.21485C10.7497 3.18152 9.48857 2.66485 7.99968 2.66485C7.51768 2.66485 7.0489 2.72702 6.59335 2.85135C6.13779 2.97569 5.70229 3.16008 5.28685 3.40452L4.55735 2.67519C5.0719 2.34519 5.62068 2.0943 6.20368 1.92252C6.78679 1.75074 7.38546 1.66485 7.99968 1.66485C8.87579 1.66485 9.69913 1.83113 10.4697 2.16369C11.2402 2.49613 11.9105 2.94735 12.4805 3.51735C13.0505 4.08735 13.5017 4.75763 13.8342 5.52819C14.1667 6.29874 14.333 7.12208 14.333 7.99819C14.333 8.61241 14.2471 9.21108 14.0753 9.79419C13.9036 10.3772 13.6527 10.926 13.3227 11.4405ZM7.99718 14.3315C7.12207 14.3315 6.29963 14.1651 5.52985 13.8322C4.76018 13.4993 4.09018 13.0479 3.51985 12.478C2.94952 11.9081 2.49802 11.238 2.16535 10.4677C1.83268 9.69724 1.66635 8.87408 1.66635 7.99819C1.66635 7.32985 1.76657 6.6803 1.96702 6.04952C2.16746 5.41874 2.46468 4.83197 2.85868 4.28919L0.853516 2.26735L1.56635 1.55469L14.574 14.5624L13.8612 15.275L3.59202 5.02252C3.28768 5.46352 3.05735 5.9353 2.90102 6.43785C2.74457 6.94041 2.66635 7.46052 2.66635 7.99819C2.66635 8.65874 2.78152 9.29035 3.01185 9.89302C3.24218 10.4956 3.57657 11.04 4.01502 11.5264C4.60224 11.0965 5.23263 10.7621 5.90618 10.5232C6.57974 10.2843 7.27757 10.1649 7.99968 10.1649C8.50735 10.1649 9.00757 10.2328 9.50035 10.3687C9.99313 10.5046 10.4651 10.6888 10.9163 10.9214L12.4792 12.484C11.8801 13.0875 11.1942 13.5462 10.4215 13.8604C9.64885 14.1745 8.84074 14.3315 7.99718 14.3315Z"
                            fill="#45546E" />
                        </g>
                      </svg>
                    </div>
                    <div *ngIf="child['is_form_required']">
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_21022_73625" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                        <rect width="16" height="16" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_21022_73625)">
                        <path d="M7.99968 13.3315C8.59202 13.3315 9.16318 13.2379 9.71318 13.0507C10.2632 12.8636 10.7681 12.5897 11.2278 12.229C10.7595 11.8811 10.2525 11.6168 9.70668 11.436C9.1609 11.2552 8.59185 11.1649 7.99952 11.1649C7.40729 11.1649 6.83618 11.2531 6.28618 11.4295C5.73618 11.6061 5.23129 11.8726 4.77152 12.229C5.23129 12.5897 5.73618 12.8636 6.28618 13.0507C6.83618 13.2379 7.40735 13.3315 7.99968 13.3315ZM9.63818 7.75585L8.92535 7.04302C9.00224 6.94135 9.06163 6.82841 9.10352 6.70419C9.1454 6.57997 9.16635 6.45574 9.16635 6.33152C9.16635 6.01097 9.05202 5.73641 8.82335 5.50785C8.59479 5.27919 8.32024 5.16485 7.99968 5.16485C7.87546 5.16485 7.75124 5.1858 7.62702 5.22769C7.50279 5.26958 7.38985 5.32897 7.28818 5.40585L6.57535 4.69302C6.77179 4.51702 6.99157 4.38496 7.23468 4.29685C7.47768 4.20885 7.73363 4.16485 8.00252 4.16485C8.60229 4.16485 9.11291 4.37552 9.53435 4.79685C9.95568 5.2183 10.1663 5.72891 10.1663 6.32869C10.1663 6.59758 10.1223 6.85352 10.0343 7.09652C9.94624 7.33963 9.81418 7.55941 9.63818 7.75585ZM13.3227 11.4405L12.5933 10.711C12.8378 10.2956 13.0222 9.86008 13.1465 9.40452C13.2709 8.94897 13.333 8.48019 13.333 7.99819C13.333 6.5093 12.8163 5.24819 11.783 4.21485C10.7497 3.18152 9.48857 2.66485 7.99968 2.66485C7.51768 2.66485 7.0489 2.72702 6.59335 2.85135C6.13779 2.97569 5.70229 3.16008 5.28685 3.40452L4.55735 2.67519C5.0719 2.34519 5.62068 2.0943 6.20368 1.92252C6.78679 1.75074 7.38546 1.66485 7.99968 1.66485C8.87579 1.66485 9.69913 1.83113 10.4697 2.16369C11.2402 2.49613 11.9105 2.94735 12.4805 3.51735C13.0505 4.08735 13.5017 4.75763 13.8342 5.52819C14.1667 6.29874 14.333 7.12208 14.333 7.99819C14.333 8.61241 14.2471 9.21108 14.0753 9.79419C13.9036 10.3772 13.6527 10.926 13.3227 11.4405ZM7.99718 14.3315C7.12207 14.3315 6.29963 14.1651 5.52985 13.8322C4.76018 13.4993 4.09018 13.0479 3.51985 12.478C2.94952 11.9081 2.49802 11.238 2.16535 10.4677C1.83268 9.69724 1.66635 8.87408 1.66635 7.99819C1.66635 7.32985 1.76657 6.6803 1.96702 6.04952C2.16746 5.41874 2.46468 4.83197 2.85868 4.28919L0.853516 2.26735L1.56635 1.55469L14.574 14.5624L13.8612 15.275L3.59202 5.02252C3.28768 5.46352 3.05735 5.9353 2.90102 6.43785C2.74457 6.94041 2.66635 7.46052 2.66635 7.99819C2.66635 8.65874 2.78152 9.29035 3.01185 9.89302C3.24218 10.4956 3.57657 11.04 4.01502 11.5264C4.60224 11.0965 5.23263 10.7621 5.90618 10.5232C6.57974 10.2843 7.27757 10.1649 7.99968 10.1649C8.50735 10.1649 9.00757 10.2328 9.50035 10.3687C9.99313 10.5046 10.4651 10.6888 10.9163 10.9214L12.4792 12.484C11.8801 13.0875 11.1942 13.5462 10.4215 13.8604C9.64885 14.1745 8.84074 14.3315 7.99718 14.3315Z" fill="#45546E"/>
                        </g>
                        <circle cx="13" cy="3" r="3" fill="red"/>
                        </svg>
                    </div>
                  </div>
                  <div class="action-icns" (click)="editMember(child, 'E')">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <mask id="mask0_19813_15925" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                        <rect width="16" height="16" fill="#D9D9D9" />
                      </mask>
                      <g mask="url(#mask0_19813_15925)">
                        <path
                          d="M3.33333 12.6654H4.26667L10.0167 6.91536L9.08333 5.98203L3.33333 11.732V12.6654ZM12.8667 5.9487L10.0333 3.1487L10.9667 2.21536C11.2222 1.95981 11.5361 1.83203 11.9083 1.83203C12.2806 1.83203 12.5944 1.95981 12.85 2.21536L13.7833 3.1487C14.0389 3.40425 14.1722 3.71259 14.1833 4.0737C14.1944 4.43481 14.0722 4.74314 13.8167 4.9987L12.8667 5.9487ZM11.9 6.93203L4.83333 13.9987H2V11.1654L9.06667 4.0987L11.9 6.93203Z"
                          fill="#45546E" />
                      </g>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- <mat-menu  #viewBalanceLog="matMenu" style="min-width: 106px !important" class="quote-position-card-menu-class">
    <div *ngIf="popUpLogData && popUpLogData.length > 0">
      <div class="row">
        <div class="col-4 p-0 m-0">
          <span class="pop-up-header-txt">Action</span>
        </div>
        <div class="col-4 p-0 m-0">
          <span class="pop-up-header-txt">Balance</span>
        </div>
        <div class="col-4 p-0 m-0">
          <span class="pop-up-header-txt">User</span>
        </div>
      </div>
      <div class="row" *ngFor="let action of popUpLogData">
        <div class="col-4 p-0 m-0">
          <span>
            <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0.333252 12.9194V1.07842C0.333252 1.00919 0.362307 0.962805 0.420419 0.93925C0.47853 0.915805 0.533252 0.925472 0.584585 0.96825L1.06142 1.38742C1.11953 1.43697 1.18453 1.46175 1.25642 1.46175C1.3282 1.46175 1.39314 1.43697 1.45125 1.38742L2.01025 0.91825C2.06836 0.868583 2.13331 0.84375 2.20509 0.84375C2.27686 0.84375 2.34181 0.868583 2.39992 0.91825L2.95892 1.38742C3.01703 1.43697 3.08197 1.46175 3.15375 1.46175C3.22553 1.46175 3.29047 1.43697 3.34859 1.38742L3.90758 0.91825C3.9657 0.868583 4.03064 0.84375 4.10242 0.84375C4.17431 0.84375 4.23931 0.868583 4.29742 0.91825L4.85642 1.38742C4.91453 1.43697 4.97947 1.46175 5.05125 1.46175C5.12303 1.46175 5.18797 1.43697 5.24608 1.38742L5.80509 0.91825C5.8632 0.868583 5.92814 0.84375 5.99992 0.84375C6.0717 0.84375 6.13664 0.868583 6.19475 0.91825L6.75375 1.38742C6.81186 1.43697 6.87681 1.46175 6.94858 1.46175C7.02036 1.46175 7.08531 1.43697 7.14342 1.38742L7.70242 0.91825C7.76053 0.868583 7.82553 0.84375 7.89742 0.84375C7.9692 0.84375 8.03414 0.868583 8.09225 0.91825L8.65125 1.38742C8.70936 1.43697 8.77431 1.46175 8.84609 1.46175C8.91786 1.46175 8.98281 1.43697 9.04092 1.38742L9.59992 0.91825C9.65803 0.868583 9.72297 0.84375 9.79475 0.84375C9.86653 0.84375 9.93147 0.868583 9.98958 0.91825L10.5486 1.38742C10.6067 1.43697 10.6716 1.46175 10.7434 1.46175C10.8153 1.46175 10.8803 1.43697 10.9384 1.38742L11.4153 0.96825C11.4666 0.925472 11.5213 0.915805 11.5794 0.93925C11.6375 0.962805 11.6666 1.00919 11.6666 1.07842V12.9194C11.6666 12.9886 11.6375 13.035 11.5794 13.0586C11.5213 13.082 11.4666 13.0724 11.4153 13.0296L10.9384 12.6104C10.8803 12.5609 10.8153 12.5361 10.7434 12.5361C10.6716 12.5361 10.6067 12.5609 10.5486 12.6104L9.98958 13.0796C9.93147 13.1293 9.86653 13.1541 9.79475 13.1541C9.72297 13.1541 9.65803 13.1293 9.59992 13.0796L9.04092 12.6104C8.98281 12.5609 8.91786 12.5361 8.84609 12.5361C8.77431 12.5361 8.70936 12.5609 8.65125 12.6104L8.09225 13.0796C8.03414 13.1293 7.9692 13.1541 7.89742 13.1541C7.82553 13.1541 7.76053 13.1293 7.70242 13.0796L7.14342 12.6104C7.08531 12.5609 7.02036 12.5361 6.94858 12.5361C6.87681 12.5361 6.81186 12.5609 6.75375 12.6104L6.19475 13.0796C6.13664 13.1293 6.0717 13.1541 5.99992 13.1541C5.92814 13.1541 5.8632 13.1293 5.80509 13.0796L5.24608 12.6104C5.18797 12.5609 5.12303 12.5361 5.05125 12.5361C4.97947 12.5361 4.91453 12.5609 4.85642 12.6104L4.29742 13.0796C4.23931 13.1293 4.17431 13.1541 4.10242 13.1541C4.03064 13.1541 3.9657 13.1293 3.90758 13.0796L3.34859 12.6104C3.29047 12.5609 3.22553 12.5361 3.15375 12.5361C3.08197 12.5361 3.01703 12.5609 2.95892 12.6104L2.39992 13.0796C2.34181 13.1293 2.27686 13.1541 2.20509 13.1541C2.13331 13.1541 2.06836 13.1293 2.01025 13.0796L1.45125 12.6104C1.39314 12.5609 1.3282 12.5361 1.25642 12.5361C1.18453 12.5361 1.11953 12.5609 1.06142 12.6104L0.584585 13.0296C0.533252 13.0724 0.47853 13.082 0.420419 13.0586C0.362307 13.035 0.333252 12.9886 0.333252 12.9194ZM2.66659 9.96042H9.33325C9.47514 9.96042 9.59392 9.91258 9.68958 9.81692C9.78536 9.72114 9.83325 9.60231 9.83325 9.46042C9.83325 9.31853 9.78536 9.19975 9.68958 9.10408C9.59392 9.00831 9.47514 8.96042 9.33325 8.96042H2.66659C2.5247 8.96042 2.40592 9.00831 2.31025 9.10408C2.21447 9.19975 2.16659 9.31853 2.16659 9.46042C2.16659 9.60231 2.21447 9.72114 2.31025 9.81692C2.40592 9.91258 2.5247 9.96042 2.66659 9.96042ZM2.66659 7.49892H9.33325C9.47514 7.49892 9.59392 7.45103 9.68958 7.35525C9.78536 7.25958 9.83325 7.14081 9.83325 6.99892C9.83325 6.85703 9.78536 6.73825 9.68958 6.64258C9.59392 6.54681 9.47514 6.49892 9.33325 6.49892H2.66659C2.5247 6.49892 2.40592 6.54681 2.31025 6.64258C2.21447 6.73825 2.16659 6.85703 2.16659 6.99892C2.16659 7.14081 2.21447 7.25958 2.31025 7.35525C2.40592 7.45103 2.5247 7.49892 2.66659 7.49892ZM2.66659 5.03742H9.33325C9.47514 5.03742 9.59392 4.98953 9.68958 4.89375C9.78536 4.79808 9.83325 4.67931 9.83325 4.53742C9.83325 4.39553 9.78536 4.27669 9.68958 4.18092C9.59392 4.08525 9.47514 4.03742 9.33325 4.03742H2.66659C2.5247 4.03742 2.40592 4.08525 2.31025 4.18092C2.21447 4.27669 2.16659 4.39553 2.16659 4.53742C2.16659 4.67931 2.21447 4.79808 2.31025 4.89375C2.40592 4.98953 2.5247 5.03742 2.66659 5.03742ZM1.33325 11.7323H10.6666V2.26558H1.33325V11.7323Z" fill="#FFBD3D"/>
              </svg>              
          </span>
          <span>{{ action.action ? action.action : '-' }}</span>
        </div>
        <div class="col-4 p-0 m-0">
          <span>{{action.balance ? action.balance : 0}}</span>
        </div>
        <div class="col-4 p-0 m-0">
          <span>{{action.action_by ? action.action_by : 0}}</span>
        </div>
      </div>
    </div>
    <div class="no-log-class" *ngIf="popUpLogData && popUpLogData.length == 0"></div>
    <div class="loader-class"></div>
  </mat-menu> -->

  <!-------------------------------Remaining Hours Log------------------->

  <sat-popover #viewBalanceLog horizontalAlign="start" verticalAlign="below" hasBackdrop>
    <div class="card balance-log-pop-over">
      <div *ngIf="popUpLogData && popUpLogData.length > 0  && !isRemainingDataLoading">
        <div class="scrollable-class" infinite-scroll [infiniteScrollDistance]="3" [infiniteScrollThrottle]="50"
          (scrolled)="onAPIDataScroll()" [scrollWindow]="false">
          <div class="row header">
            <div class="col-3 pl-0 pr-2 m-0">
              <span class="pop-up-header-txt">{{('action_header-pop-up' | checkLabel : this.formConfig: 'position-card': 'Action')}}</span>
            </div>
            <div class="col-3 pl-0 pr-2 m-0">
              <span class="pop-up-header-txt">{{('value-header-pop-up' | checkLabel : this.formConfig: 'position-card': 'Value in Hrs')}}</span>
            </div>
            <div class="col-3 pl-0 pr-2 m-0">
              <span class="pop-up-header-txt">{{('emp-header-pop-up' | checkLabel : this.formConfig: 'position-card': 'User')}}</span>
            </div>
            <div class="col-3 pl-0 pr-2 m-0">
              <span class="pop-up-header-txt">{{('date-header-pop-up' | checkLabel : this.formConfig: 'position-card': 'Action on')}}</span>
            </div>
          </div>
          <div class="row item-class" *ngFor="let action of popUpLogData">
            <div class="col-3 pl-0 pr-2 m-0 action-class">
              <span [innerHTML]="getActionItem(action.action_type,'svg') | safeHtml"></span>
              <span class="value-txt">
                <span class="overflow-class-txt"  tooltip="{{ getActionItem(action.action_type,'name') ? getActionItem(action.action_type,'name') : '-' }}">{{ getActionItem(action.action_type,'name') ? getActionItem(action.action_type,'name') : '-' }}</span>
              </span>
              <!-- <span class="value-txt" tooltip ="{{action.action_type ? action.action_type : '-'}}">{{ action.action_type ? action.action_type : '-' }}</span> -->
            </div>
            <div class="col-3 pl-0 pr-2 m-0">
              <span class="value-txt">
                <span  class="overflow-class-txt"  tooltip ="{{(action.remaining_allocated_hours ? action.remaining_allocated_hours : 0) | decimal : 1}}">{{(action.remaining_allocated_hours ? action.remaining_allocated_hours : 0) | decimal : 1}}</span>
                </span>
            </div>
            <div class="col-3 pl-0 pr-2 m-0">
              <span class="value-txt" >
                <span class="overflow-class-txt" tooltip ="{{action.action_by ? action.action_by : '-'}}">{{action.action_by ? action.action_by : '-'}}</span>
              </span>
            </div>
            <div class="col-3 pl-0 pr-2 m-0">
              <span class="value-txt">
                <span  class="overflow-class-txt" *ngIf="(action.action_date && action?.action_date != 'Invalid date'); else noDateTemplate"  tooltip ="{{action.action_date}}">{{action.action_date }}</span>
                <ng-template #noDateTemplate>
                  <span class="overflow-class-txt">-</span>
                </ng-template>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="no-log-class" *ngIf="popUpLogData && popUpLogData.length == 0 && !isRemainingDataLoading">
        <svg width="80" height="80" viewBox="0 0 248 185" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M247.798 162.281H0V162.405H247.798V162.281Z" fill="#EBEBEB"/>
          <path d="M222.969 170.258H215.584V170.382H222.969V170.258Z" fill="#EBEBEB"/>
          <path d="M164.151 171.602H159.843V171.726H164.151V171.602Z" fill="#EBEBEB"/>
          <path d="M213.491 165.656H196.546V165.78H213.491V165.656Z" fill="#EBEBEB"/>
          <path d="M54.3318 168.215H32.9258V168.339H54.3318V168.215Z" fill="#EBEBEB"/>
          <path d="M66.4651 168.215H58.7451V168.339H66.4651V168.215Z" fill="#EBEBEB"/>
          <path d="M126.255 166.551H79.8301V166.675H126.255V166.551Z" fill="#EBEBEB"/>
          <path d="M117.463 140.178H21.7631C21.013 140.177 20.2939 139.879 19.7636 139.349C19.2332 138.818 18.9349 138.099 18.9341 137.349V2.828C18.9351 2.07812 19.2336 1.35927 19.7639 0.829121C20.2942 0.298968 21.0132 0.000793618 21.7631 0H117.463C118.213 0.00105816 118.931 0.299348 119.462 0.829472C119.992 1.3596 120.29 2.07829 120.291 2.828V137.347C120.291 138.097 119.993 138.817 119.463 139.347C118.932 139.878 118.213 140.177 117.463 140.178ZM21.7641 0.124001C21.0486 0.126111 20.3631 0.411253 19.8572 0.917145C19.3513 1.42304 19.0662 2.10857 19.0641 2.824V137.347C19.0662 138.062 19.3513 138.748 19.8572 139.254C20.3631 139.76 21.0486 140.045 21.7641 140.047H117.464C118.18 140.045 118.865 139.76 119.371 139.254C119.877 138.748 120.162 138.062 120.164 137.347V2.828C120.162 2.11256 119.877 1.42703 119.371 0.921143C118.865 0.415251 118.18 0.130113 117.464 0.128002L21.7641 0.124001Z" fill="#EBEBEB"/>
          <path d="M224.659 140.178H128.959C128.209 140.177 127.49 139.879 126.96 139.348C126.43 138.818 126.132 138.099 126.131 137.349V2.828C126.132 2.07821 126.43 1.35936 126.96 0.829182C127.49 0.299001 128.209 0.000794128 128.959 0H224.659C225.409 0.000793618 226.128 0.298968 226.658 0.829121C227.188 1.35927 227.487 2.07812 227.488 2.828V137.347C227.488 138.097 227.19 138.817 226.659 139.348C226.129 139.879 225.409 140.177 224.659 140.178ZM128.959 0.124001C128.243 0.126111 127.558 0.411253 127.052 0.917145C126.546 1.42304 126.261 2.10857 126.259 2.824V137.347C126.261 138.062 126.546 138.748 127.052 139.254C127.558 139.76 128.243 140.045 128.959 140.047H224.659C225.375 140.046 226.062 139.762 226.569 139.256C227.076 138.75 227.362 138.063 227.364 137.347V2.828C227.362 2.1117 227.076 1.42542 226.569 0.919384C226.062 0.41335 225.375 0.128793 224.659 0.128002L128.959 0.124001Z" fill="#EBEBEB"/>
          <path d="M106.293 162.277H115.522L115.522 98.9053H106.293V162.277Z" fill="#E6E6E6"/>
          <path d="M109.542 162.278H106.293V150.238H112.948L109.542 162.278Z" fill="#F0F0F0"/>
          <path d="M203.887 162.277H213.116V98.9053H203.887V162.277Z" fill="#E6E6E6"/>
          <path d="M106.293 158.992H206.722V98.9072L106.293 98.9072L106.293 158.992Z" fill="#F0F0F0"/>
          <path d="M203.477 162.278H206.726V150.238H200.067L203.477 162.278Z" fill="#F0F0F0"/>
          <path d="M173.612 103.878V153.582H203.383V103.878H173.612Z" fill="#E6E6E6"/>
          <path d="M185.553 105.628H191.443C193.936 105.653 196.348 104.747 198.207 103.086H178.79C180.649 104.746 183.061 105.653 185.553 105.628V105.628Z" fill="#F0F0F0"/>
          <path d="M141.622 103.878V153.582H171.393V103.878H141.622Z" fill="#E6E6E6"/>
          <path d="M153.262 105.628H159.152C161.644 105.653 164.056 104.746 165.915 103.086H146.498C148.357 104.747 150.769 105.653 153.262 105.628V105.628Z" fill="#F0F0F0"/>
          <path d="M109.632 103.878V153.582H139.403V103.878H109.632Z" fill="#E6E6E6"/>
          <path d="M121.272 105.628H127.162C129.654 105.653 132.067 104.747 133.926 103.086H114.508C116.367 104.747 118.779 105.653 121.272 105.628V105.628Z" fill="#F0F0F0"/>
          <path d="M34.6823 162.277H97.6523L97.6523 20.7253H34.6823L34.6823 162.277Z" fill="#F0F0F0"/>
          <path d="M34.6822 162.277H96.3232L96.3232 20.7253H34.6822L34.6822 162.277Z" fill="#F5F5F5"/>
          <path d="M92.5669 159.066L92.5669 23.9375L38.4379 23.9375L38.4379 159.066H92.5669Z" fill="#F0F0F0"/>
          <path d="M87.2483 49.0695C87.2483 47.1316 86.8666 45.2126 86.125 43.4222C85.3834 41.6318 84.2964 40.005 82.9261 38.6347C81.5558 37.2644 79.929 36.1774 78.1386 35.4358C76.3482 34.6942 74.4292 34.3125 72.4913 34.3125H58.5153C54.6015 34.3125 50.848 35.8673 48.0805 38.6347C45.3131 41.4022 43.7583 45.1557 43.7583 49.0695H87.2483Z" fill="#FAFAFA"/>
          <path d="M87.248 150.669V118.387H43.758V150.669H87.248Z" fill="#F5F5F5"/>
          <path d="M87.2479 58.1719H68.0649V114.221H87.2479V58.1719Z" fill="#F5F5F5"/>
          <path d="M62.9413 58.1719H43.7583V114.221H62.9413V58.1719Z" fill="#F5F5F5"/>
          <path d="M50.7152 85.9419H42.8582C42.5351 85.9411 42.2255 85.8124 41.9971 85.584C41.7686 85.3555 41.64 85.046 41.6392 84.7229V84.7229C41.6402 84.3999 41.769 84.0905 41.9974 83.8621C42.2257 83.6337 42.5352 83.505 42.8582 83.5039H50.7152C51.0381 83.505 51.3476 83.6337 51.576 83.8621C51.8043 84.0905 51.9331 84.3999 51.9342 84.7229V84.7229C51.9334 85.046 51.8047 85.3555 51.5762 85.584C51.3478 85.8124 51.0382 85.9411 50.7152 85.9419Z" fill="#E0E0E0"/>
          <path d="M39.4131 159.066L39.4131 23.9375H38.4391L38.4391 159.066H39.4131Z" fill="#F0F0F0"/>
          <path d="M137.482 83.3711L204.261 83.3711V20.7271L137.482 20.7271V83.3711Z" fill="#E6E6E6"/>
          <path d="M135.799 83.3711L202.578 83.3711V20.7271L135.799 20.7271V83.3711Z" fill="#F5F5F5"/>
          <path d="M131.9 86.9961L204.261 86.9961V83.3701L131.9 83.3701V86.9961Z" fill="#E6E6E6"/>
          <path d="M130.005 86.9961L198.639 86.9961V83.3701L130.005 83.3701V86.9961Z" fill="#F5F5F5"/>
          <path d="M199.556 80.1114V23.9844L138.82 23.9844V80.1114H199.556Z" fill="white"/>
          <path d="M170.871 80.1114V23.9844H167.504V80.1114H170.871Z" fill="#F5F5F5"/>
          <path d="M140.093 80.1114V23.9844H138.82V80.1114H140.093Z" fill="#E6E6E6"/>
          <path d="M172.144 80.1114V23.9844H170.871V80.1114H172.144Z" fill="#E6E6E6"/>
          <path d="M146.46 85.0322C145.232 72.3072 144.377 51.6582 145.041 44.7522C145.052 44.6335 145.089 44.5186 145.148 44.4152C145.207 44.3117 145.288 44.2221 145.385 44.1522C145.513 44.0582 145.665 44.0022 145.824 43.9906C145.983 43.9789 146.141 44.0121 146.282 44.0863C146.423 44.1605 146.54 44.2728 146.62 44.4104C146.7 44.5479 146.74 44.7051 146.735 44.8642C146.618 48.2862 146.483 56.8392 147.212 71.5942C147.575 78.9392 148.098 83.9692 147.739 85.0302L146.46 85.0322Z" fill="#E6E6E6"/>
          <path d="M141.881 55.7004C138.381 51.8174 131.242 49.0564 123.964 49.0704C121.107 49.0764 132.657 53.5874 137.464 58.5704C141.306 62.7083 144.468 67.4284 146.833 72.5554C145.905 64.9504 145.378 59.5814 141.881 55.7004Z" fill="#E6E6E6"/>
          <path d="M148.813 56.8837C151.713 52.8397 158.42 49.7298 165.673 49.3778C168.52 49.2398 157.687 54.3148 153.644 59.5168C150.419 63.8646 147.968 68.7359 146.397 73.9168C146.185 66.3038 145.909 60.9287 148.813 56.8837Z" fill="#E6E6E6"/>
          <path d="M141.725 41.5203C138.764 38.2323 132.716 35.8943 126.553 35.9063C124.134 35.9063 133.914 39.7323 137.981 43.9473C141.234 47.4511 143.912 51.4479 145.915 55.7893C145.133 49.3553 144.684 44.8083 141.725 41.5203Z" fill="#E6E6E6"/>
          <path d="M147.594 46.3818C150.053 42.9568 155.73 40.3238 161.871 40.0258C164.282 39.9088 155.109 44.2058 151.685 48.6108C148.953 52.2943 146.877 56.4215 145.548 60.8108C145.37 54.3588 145.135 49.8048 147.594 46.3818Z" fill="#E6E6E6"/>
          <path d="M146.953 98.9082C145.108 98.9082 143.332 98.2025 141.99 96.9356C140.649 95.6687 139.842 93.9366 139.736 92.0943L139.098 81.0312H154.808L154.17 92.0943C154.064 93.9366 153.258 95.6687 151.916 96.9356C150.574 98.2025 148.799 98.9082 146.953 98.9082Z" fill="#E6E6E6"/>
          <path d="M155.825 83.5453H138.084L137.576 78.9453H156.337L155.825 83.5453Z" fill="#E6E6E6"/>
          <path d="M123.899 184.665C176.969 184.665 219.99 182.153 219.99 179.053C219.99 175.954 176.969 173.441 123.899 173.441C70.8295 173.441 27.8081 175.954 27.8081 179.053C27.8081 182.153 70.8295 184.665 123.899 184.665Z" fill="#F5F5F5"/>
          <path d="M125.838 173.689L121.7 174.155L120.174 164.771L124.312 164.305L125.838 173.689Z" fill="#FFC3BD"/>
          <path d="M144.364 174.592H140.232L139.203 165.023H143.335L144.364 174.592Z" fill="#FFC3BD"/>
          <path d="M139.892 174.109H144.929C145.011 174.109 145.09 174.137 145.154 174.189C145.218 174.24 145.263 174.311 145.281 174.391L146.097 178.064C146.116 178.154 146.114 178.247 146.093 178.335C146.071 178.424 146.03 178.507 145.971 178.578C145.913 178.649 145.84 178.705 145.757 178.744C145.674 178.782 145.583 178.801 145.492 178.799C143.871 178.771 142.692 178.676 140.648 178.676C139.392 178.676 135.604 178.806 133.87 178.806C132.17 178.806 131.909 177.091 132.619 176.936C135.803 176.236 138.202 175.279 139.219 174.363C139.404 174.198 139.644 174.108 139.892 174.109Z" fill="#263238"/>
          <path d="M121.632 173.444L126.116 172.936C126.197 172.927 126.28 172.945 126.349 172.989C126.418 173.032 126.471 173.098 126.498 173.176L127.723 176.733C127.754 176.819 127.764 176.911 127.754 177.002C127.744 177.093 127.714 177.18 127.665 177.257C127.616 177.335 127.55 177.4 127.472 177.447C127.394 177.495 127.306 177.524 127.215 177.533C125.595 177.689 123.247 177.859 121.215 178.09C118.838 178.36 118.442 178.996 115.638 178.931C113.938 178.891 113.512 177.166 114.225 177.031C117.473 176.4 118.101 175.909 120.682 173.883C120.951 173.651 121.28 173.499 121.632 173.444V173.444Z" fill="#263238"/>
          <path d="M125.282 53.1746C123.894 54.3206 122.528 55.3166 121.115 56.3366C119.702 57.3566 118.259 58.3036 116.774 59.2226C115.289 60.1416 113.765 61.0226 112.167 61.8226C110.533 62.6689 108.832 63.3787 107.082 63.9446C106.619 64.0926 106.168 64.2146 105.648 64.3446C105.139 64.4548 104.625 64.5373 104.108 64.5916C103.177 64.6916 102.301 64.7366 101.428 64.7766C99.6876 64.8566 97.9806 64.8546 96.2756 64.8426C92.8656 64.8016 89.4846 64.6376 86.0886 64.3376L86.0376 61.2376C89.3726 60.8086 92.7226 60.4376 96.0376 60.0646C97.6946 59.8846 99.3476 59.6826 100.963 59.4716C101.763 59.3626 102.563 59.2346 103.301 59.0976C103.618 59.0416 103.932 58.9678 104.241 58.8766C104.532 58.7766 104.889 58.6626 105.219 58.5296C106.636 57.9542 108.011 57.2813 109.336 56.5156C110.707 55.7286 112.079 54.8836 113.427 53.9856C114.775 53.0876 116.117 52.1576 117.447 51.2086C118.777 50.2596 120.106 49.2546 121.354 48.3086L125.282 53.1746Z" fill="#FFC3BD"/>
          <path d="M129.321 49.6424C128.727 53.5484 120.503 59.5234 120.503 59.5234L114.603 52.2004C117.188 49.4421 119.928 46.8342 122.811 44.3894C125.466 42.2194 129.967 45.3924 129.321 49.6424Z" fill="#526179"/>
          <path d="M87.1758 61.5645L84.8298 59.8945L84.5728 65.1705C84.9274 65.1715 85.2768 65.0856 85.5906 64.9203C85.9044 64.7551 86.1729 64.5155 86.3728 64.2225L87.1758 61.5645Z" fill="#FFC3BD"/>
          <path d="M82.711 58.3594L82.231 63.5304L84.575 65.1714L84.832 59.8954L82.711 58.3594Z" fill="#FFC3BD"/>
          <path opacity="0.2" d="M120.176 164.775L120.962 169.612L125.101 169.146L124.315 164.309L120.176 164.775Z" fill="black"/>
          <path opacity="0.2" d="M143.337 165.027H139.203L139.735 169.959H143.869L143.337 165.027Z" fill="black"/>
          <path d="M143.396 44.5152C143.703 44.5842 143.994 44.7141 144.251 44.8972C144.508 45.0803 144.726 45.3129 144.891 45.5813C145.057 45.8498 145.167 46.1486 145.215 46.4603C145.264 46.772 145.249 47.0902 145.173 47.3962C144.276 51.1177 143.6 54.8889 143.149 58.6902C142.758 61.8902 142.524 64.9472 142.378 67.7182C142.035 74.2132 142.178 79.1642 141.984 80.8422C138.753 80.6182 126.577 79.7712 119.938 79.3072C117.697 61.7632 120.323 50.5672 121.798 46.0182C122.024 45.3094 122.446 44.6793 123.016 44.2013C123.586 43.7233 124.28 43.417 125.018 43.3182C125.864 43.2072 126.876 43.1002 127.944 43.0372C128.318 43.0132 128.697 43.0002 129.083 42.9922C132.217 43.0293 135.347 43.247 138.456 43.6442C139.046 43.7142 139.642 43.8012 140.222 43.8942C141.381 44.0882 142.483 44.3112 143.396 44.5152Z" fill="#6E7B8F"/>
          <path d="M138.265 33.0312C137.465 36.0052 136.487 41.4892 138.457 43.6492C133.347 47.0862 130.085 51.9253 129.144 50.8743C128.651 50.3243 128.437 43.9593 129.085 42.9953C132.504 42.4273 132.591 39.9262 132.169 37.5422L138.265 33.0312Z" fill="#FFC3BD"/>
          <path d="M138.061 42.418C138.061 42.418 129.734 46.103 128.63 54.343C130.924 51.687 135.991 48.797 135.991 48.797L133.756 48.155C135.177 47.6155 136.649 47.2196 138.149 46.973C137.901 45.835 139.223 43.565 139.223 43.565L138.061 42.418Z" fill="#526179"/>
          <path d="M130.548 42.2852C130.548 42.2852 131.658 46.9912 128.631 54.3432C128.034 51.7771 127.215 49.2677 126.185 46.8432L128.224 47.3982C128.224 47.3982 128.124 46.4492 126.473 45.1872C127.161 44.1879 128.019 43.317 129.008 42.6142L130.548 42.2852Z" fill="#526179"/>
          <path opacity="0.2" d="M135.78 34.875L132.172 37.543C132.279 38.0982 132.337 38.6617 132.345 39.227C133.645 39.127 135.505 37.844 135.736 36.509C135.849 35.9715 135.864 35.4178 135.78 34.875V34.875Z" fill="black"/>
          <path d="M127.684 24.4191C125.669 24.5061 124.557 27.9801 126.602 30.8411C128.647 33.7021 130.399 24.3021 127.684 24.4191Z" fill="#263238"/>
          <path d="M137.621 27.4779C137.86 31.5719 138.208 33.9549 136.406 36.2879C133.696 39.7969 128.456 38.5389 126.999 34.5989C125.688 31.0529 125.517 24.9539 129.299 22.8589C130.131 22.3925 131.068 22.1466 132.022 22.1445C132.976 22.1425 133.914 22.3844 134.748 22.8473C135.582 23.3101 136.284 23.9786 136.786 24.7891C137.289 25.5995 137.576 26.5251 137.621 27.4779V27.4779Z" fill="#FFC3BD"/>
          <path d="M138.222 30.6231C137.127 29.9813 136.171 29.1282 135.409 28.1134C134.647 27.0986 134.095 25.9424 133.785 24.7121C132.585 24.9961 126.604 27.7481 124.765 24.7121C122.926 21.6761 125.24 19.8951 128.681 21.7641C127.159 19.0781 128.543 17.7641 133.325 17.6481C138.107 17.5321 137.464 20.3041 137.464 20.3041C137.464 20.3041 141.084 18.7281 142.364 21.5041C143.847 24.6941 141.111 30.1411 138.222 30.6231Z" fill="#263238"/>
          <path d="M136.646 19.5693C136.69 19.5693 138.457 19.9943 140.493 17.9453C140.128 19.6933 136.966 20.8573 136.966 20.8573L136.646 19.5693Z" fill="#263238"/>
          <path d="M139.022 25.5609C137.115 24.9019 134.806 27.7269 135.659 31.1379C136.512 34.5489 141.591 26.4489 139.022 25.5609Z" fill="#263238"/>
          <path d="M140.101 30.8834C139.926 31.9001 139.356 32.8062 138.515 33.4034C137.394 34.1854 136.384 33.2914 136.315 31.9974C136.257 30.8334 136.769 29.0234 138.076 28.7564C138.363 28.7028 138.658 28.7217 138.936 28.8114C139.214 28.9012 139.465 29.0589 139.666 29.2702C139.867 29.4815 140.012 29.7398 140.088 30.0215C140.164 30.3033 140.169 30.5995 140.101 30.8834V30.8834Z" fill="#FFC3BD"/>
          <path d="M126.152 125.863C127.145 113.777 134.429 80.3196 134.429 80.3196L119.935 79.3086C119.935 79.3086 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z" fill="#526179"/>
          <path opacity="0.5" d="M126.152 125.863C127.145 113.777 134.429 80.3196 134.429 80.3196L119.935 79.3086C119.935 79.3086 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z" fill="white"/>
          <path opacity="0.2" d="M128.969 91.6484C126.047 95.3844 125.995 108.505 127.125 118.199C128.202 111.206 129.899 102.325 131.386 94.9224L128.969 91.6484Z" fill="black"/>
          <path d="M127.756 79.8516C127.756 79.8516 129.32 115.227 131.065 127.207C132.975 140.326 137.826 168.999 137.826 168.999H145.394C145.394 168.999 144.149 141.993 143.15 129.104C142.013 114.423 141.982 80.8436 141.982 80.8436L127.756 79.8516Z" fill="#526179"/>
          <path opacity="0.5" d="M127.756 79.8516C127.756 79.8516 129.32 115.227 131.065 127.207C132.975 140.326 137.826 168.999 137.826 168.999H145.394C145.394 168.999 144.149 141.993 143.15 129.104C142.013 114.423 141.982 80.8436 141.982 80.8436L127.756 79.8516Z" fill="white"/>
          <path d="M136.916 169.159H145.827V166.497L135.866 166.324L136.916 169.159Z" fill="#526179"/>
          <path d="M118.44 168.136L126.724 167.197L126.424 164.52L117.147 165.429L118.44 168.136Z" fill="#526179"/>
          <path d="M131.593 29.3547C131.633 29.6857 131.493 29.9747 131.273 30.0007C131.053 30.0267 130.849 29.7797 130.81 29.4487C130.771 29.1177 130.91 28.8287 131.13 28.8027C131.35 28.7767 131.554 29.0237 131.593 29.3547Z" fill="#263238"/>
          <path d="M127.854 29.8039C127.894 30.1349 127.754 30.4238 127.534 30.4498C127.314 30.4758 127.11 30.2289 127.07 29.8979C127.03 29.5669 127.17 29.2779 127.39 29.2519C127.61 29.2259 127.814 29.4769 127.854 29.8039Z" fill="#263238"/>
          <path d="M129.062 29.8242C128.789 30.8829 128.372 31.8991 127.822 32.8442C128.067 32.9786 128.339 33.0579 128.618 33.0763C128.897 33.0948 129.177 33.0521 129.438 32.9512L129.062 29.8242Z" fill="#ED847E"/>
          <path d="M132.61 28.0068C132.575 28.0173 132.538 28.0181 132.503 28.0091C132.468 28.0001 132.436 27.9817 132.41 27.9558C132.245 27.7739 132.038 27.6338 131.809 27.5469C131.579 27.46 131.331 27.4288 131.087 27.4558C131.036 27.4644 130.984 27.4529 130.941 27.4239C130.898 27.3948 130.869 27.3503 130.858 27.2998C130.853 27.2745 130.853 27.2484 130.858 27.223C130.863 27.1977 130.873 27.1736 130.887 27.1523C130.902 27.1309 130.921 27.1127 130.942 27.0987C130.964 27.0847 130.988 27.0752 131.014 27.0708C131.322 27.0308 131.636 27.0656 131.928 27.1721C132.22 27.2786 132.483 27.4537 132.693 27.6828C132.729 27.72 132.749 27.7697 132.749 27.8213C132.749 27.8729 132.729 27.9225 132.693 27.9598C132.67 27.9821 132.641 27.9983 132.61 28.0068V28.0068Z" fill="#263238"/>
          <path d="M131.173 34.2719C131.645 34.1727 132.086 33.9613 132.459 33.6557C132.832 33.35 133.126 32.9591 133.316 32.5159C133.325 32.4894 133.323 32.4603 133.311 32.435C133.299 32.4097 133.277 32.3902 133.251 32.3809C133.224 32.3717 133.195 32.3733 133.17 32.3855C133.144 32.3977 133.125 32.4194 133.116 32.4459C132.934 32.8536 132.658 33.2128 132.312 33.4945C131.966 33.7762 131.558 33.9727 131.122 34.0679C131.108 34.071 131.096 34.0767 131.084 34.0847C131.073 34.0927 131.063 34.1029 131.056 34.1146C131.049 34.1263 131.044 34.1393 131.042 34.1529C131.039 34.1665 131.04 34.1805 131.043 34.1939C131.046 34.2074 131.052 34.2201 131.06 34.2314C131.068 34.2426 131.078 34.2522 131.089 34.2595C131.101 34.2669 131.114 34.2718 131.128 34.2741C131.141 34.2764 131.155 34.276 131.169 34.2729L131.173 34.2719Z" fill="#263238"/>
          <path d="M126.095 28.7073C126.056 28.7205 126.013 28.7205 125.974 28.7073C125.926 28.6895 125.886 28.6536 125.863 28.6072C125.841 28.5608 125.837 28.5074 125.853 28.4583C125.947 28.1617 126.111 27.8921 126.331 27.6724C126.551 27.4527 126.821 27.2894 127.118 27.1963C127.168 27.185 127.221 27.1934 127.265 27.2198C127.309 27.2463 127.341 27.2887 127.355 27.3383C127.366 27.3884 127.358 27.4409 127.332 27.485C127.306 27.5291 127.263 27.5615 127.214 27.5753V27.5753C126.981 27.6532 126.769 27.7853 126.597 27.9609C126.424 28.1365 126.296 28.3505 126.223 28.5853C126.212 28.6141 126.195 28.6401 126.173 28.6613C126.151 28.6824 126.124 28.6982 126.095 28.7073Z" fill="#263238"/>
          <path d="M138.531 174.833C138.248 174.855 137.966 174.781 137.731 174.622C137.657 174.558 137.601 174.478 137.566 174.388C137.531 174.298 137.519 174.201 137.531 174.105C137.531 174.047 137.546 173.991 137.576 173.943C137.605 173.894 137.648 173.854 137.699 173.828C138.158 173.592 139.489 174.412 139.639 174.506C139.655 174.516 139.668 174.531 139.676 174.549C139.684 174.567 139.687 174.586 139.684 174.606C139.68 174.625 139.672 174.642 139.659 174.657C139.645 174.671 139.628 174.681 139.61 174.686C139.257 174.775 138.895 174.824 138.531 174.833V174.833ZM137.917 173.978C137.873 173.976 137.83 173.984 137.79 174.002C137.769 174.013 137.753 174.03 137.742 174.05C137.731 174.07 137.726 174.094 137.728 174.117C137.719 174.182 137.726 174.249 137.749 174.311C137.773 174.373 137.811 174.428 137.861 174.472C138.322 174.682 138.847 174.709 139.328 174.546C138.897 174.266 138.416 174.073 137.912 173.978H137.917Z" fill="#526179"/>
          <path d="M139.584 174.689C139.567 174.689 139.551 174.685 139.537 174.677C139.11 174.445 138.283 173.539 138.37 173.077C138.384 173.005 138.423 172.94 138.479 172.893C138.536 172.847 138.607 172.821 138.681 172.82C138.755 172.811 138.831 172.818 138.902 172.84C138.974 172.861 139.041 172.898 139.098 172.946C139.578 173.339 139.677 174.531 139.68 174.581C139.681 174.599 139.677 174.616 139.67 174.632C139.662 174.648 139.65 174.662 139.636 174.671C139.62 174.682 139.602 174.688 139.584 174.689ZM138.75 173.014H138.705C138.578 173.03 138.567 173.088 138.562 173.114C138.51 173.386 139.037 174.07 139.462 174.393C139.434 173.921 139.263 173.468 138.974 173.093C138.911 173.041 138.831 173.013 138.75 173.014V173.014Z" fill="#526179"/>
          <path d="M121.296 174.055L121.28 174.062C120.633 174.269 119.475 174.562 118.98 174.216C118.907 174.165 118.847 174.098 118.806 174.019C118.765 173.94 118.744 173.852 118.744 173.764C118.74 173.71 118.75 173.656 118.773 173.608C118.796 173.559 118.831 173.518 118.875 173.487C119.34 173.16 121.087 173.803 121.285 173.878C121.302 173.885 121.317 173.897 121.327 173.912C121.338 173.927 121.344 173.945 121.345 173.963C121.346 173.982 121.342 174 121.334 174.016C121.325 174.033 121.312 174.046 121.296 174.056V174.055ZM119.013 173.631L118.99 173.645C118.972 173.657 118.958 173.673 118.949 173.693C118.94 173.713 118.937 173.734 118.94 173.756C118.939 173.814 118.952 173.872 118.978 173.925C119.005 173.977 119.044 174.022 119.092 174.056C119.7 174.245 120.356 174.21 120.94 173.956C120.335 173.687 119.672 173.575 119.012 173.631H119.013Z" fill="#526179"/>
          <path d="M121.296 174.056C121.272 174.068 121.245 174.07 121.22 174.062C120.7 173.896 119.62 173.119 119.64 172.647C119.645 172.536 119.708 172.391 119.972 172.334C120.067 172.312 120.166 172.311 120.262 172.329C120.358 172.347 120.449 172.385 120.53 172.44C120.954 172.845 121.239 173.373 121.346 173.95C121.349 173.966 121.348 173.984 121.343 174C121.338 174.016 121.328 174.031 121.316 174.043C121.31 174.048 121.303 174.052 121.296 174.056V174.056ZM119.905 172.566C119.838 172.602 119.836 172.644 119.835 172.66C119.821 172.944 120.567 173.566 121.108 173.812C121.008 173.351 120.769 172.932 120.423 172.612C120.364 172.571 120.297 172.543 120.226 172.529C120.156 172.515 120.083 172.517 120.013 172.533C119.976 172.539 119.939 172.55 119.905 172.566Z" fill="#526179"/>
          <path d="M146.882 50.4757C146.777 52.0897 146.587 53.5927 146.39 55.1447C146.193 56.6967 145.928 58.2177 145.637 59.7527C145.061 62.8879 144.211 65.9664 143.096 68.9527L142.605 70.1097L142.481 70.3987L142.45 70.4707L142.399 70.5807L142.255 70.8657C142.064 71.212 141.836 71.536 141.574 71.8317C141.143 72.3168 140.639 72.7318 140.08 73.0617C139.627 73.3311 139.152 73.5616 138.661 73.7507C137.023 74.3415 135.31 74.699 133.573 74.8127C130.418 75.0483 127.247 74.9299 124.119 74.4597L124.232 71.3597L126.418 71.1127C127.151 71.0257 127.88 70.9127 128.605 70.8127C130.052 70.5997 131.484 70.3617 132.849 70.0447C134.111 69.7876 135.339 69.3807 136.505 68.8327C136.819 68.7005 137.094 68.4902 137.305 68.2217C137.318 68.1507 137.16 68.4617 137.346 67.9647L137.673 67.0087C138.474 64.268 139.086 61.4758 139.507 58.6517C139.742 57.2137 139.943 55.7627 140.137 54.3077C140.331 52.8527 140.491 51.3677 140.644 49.9727L146.882 50.4757Z" fill="#FFC3BD"/>
          <path d="M147.242 47.4803C149.265 50.8733 147.542 61.5373 147.542 61.5373L136.942 59.5783C136.942 59.5783 136.368 53.4783 138.386 48.7843C140.577 43.6753 144.791 43.3693 147.242 47.4803Z" fill="#526179"/>
          <path d="M121.264 80.9523L115.639 54.7883C115.369 53.671 114.743 52.6718 113.856 51.9413C112.968 51.2108 111.867 50.7885 110.719 50.7383H89.166C90.3145 50.7883 91.4156 51.2104 92.3033 51.941C93.1909 52.6715 93.817 53.6708 94.087 54.7883L99.712 80.9523C99.982 82.0696 100.608 83.0688 101.495 83.7993C102.383 84.5298 103.484 84.952 104.632 85.0023H126.184C125.036 84.952 123.935 84.5298 123.047 83.7993C122.16 83.0688 121.534 82.0696 121.264 80.9523V80.9523Z" fill="#263238"/>
          <path opacity="0.7" d="M121.264 80.9523L115.639 54.7883C115.369 53.671 114.743 52.6718 113.856 51.9413C112.968 51.2108 111.867 50.7885 110.719 50.7383H89.166C90.3145 50.7883 91.4156 51.2104 92.3033 51.941C93.1909 52.6715 93.817 53.6708 94.087 54.7883L99.712 80.9523C99.982 82.0696 100.608 83.0688 101.495 83.7993C102.383 84.5298 103.484 84.952 104.632 85.0023H126.184C125.036 84.952 123.935 84.5298 123.047 83.7993C122.16 83.0688 121.534 82.0696 121.264 80.9523V80.9523Z" fill="#526179"/>
          <path d="M89.1658 50.7383H110.718C109.614 50.7589 108.556 51.1819 107.743 51.9277C106.929 52.6735 106.416 53.6908 106.3 54.7883L100.709 115.588C100.592 116.686 100.079 117.703 99.2655 118.449C98.452 119.194 97.3942 119.617 96.2908 119.638H82.8388C82.3206 119.648 81.8064 119.547 81.3305 119.341C80.8546 119.136 80.4281 118.832 80.0795 118.448C79.7308 118.065 79.4682 117.611 79.309 117.118C79.1499 116.625 79.098 116.103 79.1568 115.588L84.7488 54.7883C84.8651 53.6909 85.3781 52.6738 86.1914 51.9281C87.0048 51.1823 88.0625 50.7592 89.1658 50.7383Z" fill="#526179"/>
          <path d="M92.6088 115.586C92.55 116.101 92.6019 116.622 92.7611 117.116C92.9202 117.609 93.1829 118.062 93.5315 118.446C93.8801 118.829 94.3066 119.134 94.7825 119.339C95.2584 119.544 95.7726 119.646 96.2908 119.636H74.7388C74.2206 119.646 73.7063 119.545 73.2303 119.339C72.7544 119.134 72.3278 118.83 71.9791 118.446C71.6304 118.063 71.3678 117.609 71.2087 117.116C71.0497 116.622 70.9979 116.101 71.0568 115.586H92.6088Z" fill="#263238"/>
          <path opacity="0.7" d="M92.6088 115.586C92.55 116.101 92.6019 116.622 92.7611 117.116C92.9202 117.609 93.1829 118.062 93.5315 118.446C93.8801 118.829 94.3066 119.134 94.7825 119.339C95.2584 119.544 95.7726 119.646 96.2908 119.636H74.7388C74.2206 119.646 73.7063 119.545 73.2303 119.339C72.7544 119.134 72.3278 118.83 71.9791 118.446C71.6304 118.063 71.3678 117.609 71.2087 117.116C71.0497 116.622 70.9979 116.101 71.0568 115.586H92.6088Z" fill="#526179"/>
          <g opacity="0.5">
          <path opacity="0.5" d="M102.565 57.5492H87.9807C87.8681 57.5508 87.7564 57.5284 87.6531 57.4834C87.5499 57.4385 87.4573 57.3722 87.3817 57.2887C87.306 57.2053 87.2489 57.1068 87.2143 56.9996C87.1796 56.8925 87.1682 56.7792 87.1807 56.6672V56.6672C87.2066 56.4285 87.3185 56.2073 87.4955 56.045C87.6725 55.8827 87.9026 55.7904 88.1427 55.7852H102.725C102.837 55.7837 102.949 55.8061 103.052 55.851C103.155 55.8959 103.248 55.9623 103.324 56.0457C103.399 56.1291 103.456 56.2277 103.491 56.3349C103.526 56.442 103.537 56.5553 103.525 56.6672V56.6672C103.499 56.9057 103.387 57.1266 103.211 57.2888C103.034 57.4511 102.804 57.5436 102.565 57.5492V57.5492Z" fill="white"/>
          <path opacity="0.5" d="M102.131 62.3188H87.5466C87.434 62.3203 87.3224 62.2979 87.2191 62.253C87.1158 62.208 87.0233 62.1417 86.9476 62.0583C86.8719 61.9749 86.8149 61.8763 86.7802 61.7691C86.7456 61.662 86.7341 61.5487 86.7466 61.4368C86.7725 61.198 86.8844 60.9768 87.0615 60.8145C87.2385 60.6522 87.4685 60.5599 87.7086 60.5548H102.293C102.405 60.5532 102.517 60.5756 102.62 60.6205C102.723 60.6655 102.816 60.7318 102.892 60.8152C102.967 60.8987 103.024 60.9972 103.059 61.1044C103.094 61.2115 103.105 61.3248 103.093 61.4368V61.4368C103.067 61.6756 102.955 61.8969 102.778 62.0592C102.601 62.2216 102.371 62.3138 102.131 62.3188V62.3188Z" fill="white"/>
          <path opacity="0.5" d="M98.1215 67.0922H87.1125C86.9999 67.0937 86.8883 67.0713 86.785 67.0264C86.6817 66.9815 86.5892 66.9151 86.5135 66.8317C86.4378 66.7483 86.3808 66.6497 86.3461 66.5426C86.3115 66.4354 86.3 66.3221 86.3125 66.2102V66.2102C86.3384 65.9714 86.4504 65.7503 86.6274 65.588C86.8044 65.4257 87.0344 65.3334 87.2745 65.3282H98.2835C98.3961 65.3267 98.5078 65.3491 98.6111 65.394C98.7143 65.4389 98.8069 65.5053 98.8825 65.5887C98.9582 65.6721 99.0152 65.7707 99.0499 65.8778C99.0846 65.985 99.096 66.0983 99.0835 66.2102V66.2102C99.0576 66.4489 98.9457 66.6701 98.7687 66.8324C98.5916 66.9947 98.3616 67.087 98.1215 67.0922V67.0922Z" fill="white"/>
          <path opacity="0.5" d="M90.4129 71.8617H86.6799C86.5673 71.8633 86.4557 71.8409 86.3524 71.7959C86.2491 71.751 86.1566 71.6847 86.0809 71.6012C86.0052 71.5178 85.9482 71.4193 85.9135 71.3121C85.8789 71.205 85.8674 71.0917 85.8799 70.9797V70.9797C85.9056 70.7411 86.0173 70.5199 86.1942 70.3576C86.371 70.1953 86.6009 70.1029 86.8409 70.0977H90.5749C90.6875 70.0962 90.7992 70.1186 90.9024 70.1635C91.0057 70.2084 91.0983 70.2748 91.1739 70.3582C91.2496 70.4416 91.3066 70.5402 91.3413 70.6474C91.3759 70.7545 91.3874 70.8678 91.3749 70.9797V70.9797C91.349 71.2185 91.2371 71.4396 91.06 71.6019C90.883 71.7642 90.653 71.8566 90.4129 71.8617V71.8617Z" fill="white"/>
          <path opacity="0.5" d="M100.829 76.6313H86.2409C86.1283 76.6328 86.0167 76.6104 85.9134 76.5655C85.8101 76.5206 85.7176 76.4542 85.6419 76.3708C85.5663 76.2874 85.5092 76.1888 85.4746 76.0816C85.4399 75.9745 85.4284 75.8612 85.4409 75.7493V75.7493C85.4669 75.5105 85.5788 75.2893 85.7558 75.127C85.9328 74.9647 86.1628 74.8724 86.4029 74.8673H100.987C101.1 74.8657 101.211 74.8881 101.314 74.9331C101.418 74.978 101.51 75.0443 101.586 75.1277C101.662 75.2112 101.719 75.3097 101.753 75.4169C101.788 75.524 101.799 75.6373 101.787 75.7493V75.7493C101.761 75.9874 101.65 76.208 101.474 76.3702C101.297 76.5324 101.068 76.6251 100.829 76.6313V76.6313Z" fill="white"/>
          <path opacity="0.5" d="M94.6517 81.4047H85.8117C85.6991 81.4063 85.5875 81.3838 85.4842 81.3389C85.3809 81.294 85.2884 81.2276 85.2127 81.1442C85.137 81.0608 85.08 80.9622 85.0454 80.8551C85.0107 80.7479 84.9992 80.6346 85.0117 80.5227V80.5227C85.0374 80.284 85.1491 80.0629 85.326 79.9006C85.5028 79.7383 85.7327 79.6459 85.9727 79.6407H94.8127C94.9253 79.6392 95.037 79.6616 95.1403 79.7065C95.2435 79.7514 95.3361 79.8178 95.4117 79.9012C95.4874 79.9846 95.5445 80.0832 95.5791 80.1903C95.6138 80.2975 95.6252 80.4108 95.6127 80.5227V80.5227C95.5868 80.7613 95.4751 80.9823 95.2983 81.1446C95.1214 81.3069 94.8917 81.3993 94.6517 81.4047V81.4047Z" fill="white"/>
          <path opacity="0.5" d="M99.9617 86.1742H85.3777C85.265 86.1758 85.1534 86.1534 85.0501 86.1084C84.9468 86.0635 84.8543 85.9972 84.7786 85.9137C84.703 85.8303 84.6459 85.7318 84.6113 85.6246C84.5766 85.5175 84.5652 85.4042 84.5777 85.2922V85.2922C84.6036 85.0535 84.7155 84.8323 84.8925 84.67C85.0695 84.5077 85.2996 84.4154 85.5397 84.4102H100.125C100.237 84.4087 100.349 84.4311 100.452 84.476C100.555 84.5209 100.648 84.5873 100.724 84.6707C100.799 84.7541 100.856 84.8527 100.891 84.9599C100.926 85.067 100.937 85.1803 100.925 85.2922V85.2922C100.899 85.5312 100.787 85.7524 100.609 85.9148C100.432 86.0771 100.202 86.1693 99.9617 86.1742V86.1742Z" fill="white"/>
          <path opacity="0.5" d="M95.9526 90.9438H84.9436C84.831 90.9453 84.7193 90.9229 84.616 90.878C84.5128 90.8331 84.4202 90.7667 84.3446 90.6833C84.2689 90.5999 84.2118 90.5013 84.1772 90.3941C84.1425 90.287 84.1311 90.1737 84.1436 90.0618V90.0618C84.1695 89.823 84.2814 89.6019 84.4584 89.4396C84.6355 89.2773 84.8655 89.1849 85.1056 89.1798H96.1146C96.2272 89.1782 96.3388 89.2006 96.4421 89.2456C96.5454 89.2905 96.6379 89.3568 96.7136 89.4403C96.7893 89.5237 96.8463 89.6222 96.881 89.7294C96.9156 89.8365 96.9271 89.9498 96.9146 90.0618V90.0618C96.8886 90.3005 96.7767 90.5217 96.5997 90.684C96.4227 90.8463 96.1927 90.9386 95.9526 90.9438V90.9438Z" fill="white"/>
          <path opacity="0.5" d="M88.2435 95.7133H84.5075C84.3949 95.7148 84.2833 95.6924 84.18 95.6475C84.0767 95.6026 83.9842 95.5362 83.9085 95.4528C83.8329 95.3694 83.7758 95.2708 83.7412 95.1637C83.7065 95.0565 83.695 94.9432 83.7075 94.8313V94.8313C83.7335 94.5925 83.8454 94.3714 84.0224 94.2091C84.1994 94.0468 84.4294 93.9544 84.6695 93.9493H88.4075C88.5202 93.9477 88.6318 93.9702 88.7351 94.0151C88.8384 94.06 88.9309 94.1264 89.0066 94.2098C89.0822 94.2932 89.1393 94.3918 89.1739 94.4989C89.2086 94.6061 89.22 94.7194 89.2075 94.8313C89.1816 95.0704 89.0694 95.2918 88.8919 95.4541C88.7145 95.6165 88.484 95.7086 88.2435 95.7133V95.7133Z" fill="white"/>
          <path opacity="0.5" d="M98.6599 100.487H84.0759C83.9633 100.488 83.8516 100.466 83.7484 100.421C83.6451 100.376 83.5525 100.31 83.4769 100.226C83.4012 100.143 83.3442 100.044 83.3095 99.9371C83.2749 99.83 83.2634 99.7167 83.2759 99.6047V99.6047C83.3018 99.366 83.4137 99.1448 83.5908 98.9825C83.7678 98.8202 83.9978 98.7279 84.2379 98.7227H98.8219C98.9345 98.7212 99.0462 98.7436 99.1494 98.7885C99.2527 98.8334 99.3453 98.8998 99.4209 98.9832C99.4966 99.0666 99.5536 99.1652 99.5883 99.2724C99.6229 99.3795 99.6344 99.4928 99.6219 99.6047V99.6047C99.596 99.8435 99.4841 100.065 99.307 100.227C99.13 100.389 98.9 100.482 98.6599 100.487V100.487Z" fill="white"/>
          <path opacity="0.5" d="M92.4828 105.256H83.6428C83.5302 105.258 83.4185 105.235 83.3153 105.19C83.212 105.146 83.1194 105.079 83.0438 104.996C82.9681 104.912 82.9111 104.814 82.8764 104.707C82.8418 104.599 82.8303 104.486 82.8428 104.374V104.374C82.8685 104.135 82.9804 103.914 83.1574 103.752C83.3345 103.589 83.5646 103.497 83.8048 103.492H92.6438C92.7564 103.491 92.8681 103.513 92.9713 103.558C93.0746 103.603 93.1672 103.669 93.2428 103.753C93.3185 103.836 93.3755 103.935 93.4102 104.042C93.4448 104.149 93.4563 104.262 93.4438 104.374V104.374C93.4179 104.613 93.3061 104.834 93.1293 104.996C92.9525 105.158 92.7227 105.251 92.4828 105.256V105.256Z" fill="white"/>
          <path opacity="0.5" d="M97.7917 110.026H83.2077C83.0951 110.027 82.9835 110.005 82.8802 109.96C82.7769 109.915 82.6844 109.849 82.6087 109.765C82.5331 109.682 82.476 109.583 82.4413 109.476C82.4067 109.369 82.3952 109.256 82.4077 109.144V109.144C82.4337 108.905 82.5456 108.684 82.7226 108.522C82.8996 108.359 83.1296 108.267 83.3697 108.262H97.9517C98.0643 108.26 98.176 108.283 98.2793 108.328C98.3825 108.373 98.4751 108.439 98.5508 108.522C98.6264 108.606 98.6835 108.704 98.7181 108.811C98.7528 108.919 98.7642 109.032 98.7517 109.144V109.144C98.7258 109.382 98.6142 109.603 98.4376 109.765C98.261 109.928 98.0315 110.02 97.7917 110.026Z" fill="white"/>
          </g>
          <path d="M125.503 71.7002L122.337 68.9492L120.853 74.0132C120.853 74.0132 124.069 75.7572 125.183 74.1562L125.503 71.7002Z" fill="#FFC3BD"/>
          <path d="M118.888 68.75L117.858 72.994L120.85 74.014L122.334 68.949L118.888 68.75Z" fill="#FFC3BD"/>
          </svg>
        <span class="no-data">No History Available</span>
      </div>
      <div class="loader-class" *ngIf="isRemainingDataLoading">
        <mat-spinner class="green-spinner loader" diameter="20"></mat-spinner>
      </div>
    </div>
  </sat-popover>

  
  
  <!--------------------------------Balance Pop up-------------------------------------------->
  <mat-menu
  #viewActualMenu="matMenu"
  style="min-width: 106px !important"
>
  <div class="actual-card">
    <div class="header">
      <span class="header-carry-card">MONTH</span>
      <span class="header-carry-card">{{('actual_hours_pop_up' | checkLabel : this.formConfig: 'position-card': 'Actual Hours')}}</span>
    </div>
    <div *ngFor="let month of data.month_wise_billed_hours">
      <div class="data-value">
        <span class="value-carry-card"
          >{{data.month_year}}</span
        >
        <span class="value-carry-card">{{(data.actual_hours ? data.actual_hours : 0) | decimal : 1}}Hrs</span>
      </div>
    </div>
  </div>
</mat-menu>