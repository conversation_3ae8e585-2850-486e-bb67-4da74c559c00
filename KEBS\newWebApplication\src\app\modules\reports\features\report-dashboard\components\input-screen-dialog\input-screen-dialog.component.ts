import { Component, Inject, InjectionToken, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ReportDashboardServiceService } from '../../services/report-dashboard-service.service';
import { SubSink } from 'subsink';
import moment from 'moment';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
export const TOASTER_MESSAGE_SERVICE_TOKEN = new InjectionToken<ToasterMessageService>('TOASTER_MESSAGE_SERVICE_TOKEN');
import * as _ from 'underscore';
@Component({
  selector: 'app-input-screen-dialog',
  templateUrl: './input-screen-dialog.component.html',
  styleUrls: ['./input-screen-dialog.component.scss'],
  providers: [
  { provide: TOASTER_MESSAGE_SERVICE_TOKEN, useClass: ToasterMessageService }
  ]
})
export class InputScreenDialogComponent implements OnInit {

  regionListMaster: any = [];
  divisionListMaster: any = [];
  subdivisionListMaster: any = [];
  inputScreenForm = new FormGroup({
    region: new FormControl('', Validators.required),
    division: new FormControl(''),
    sub_division: new FormControl('')
  });
  subs = new SubSink();
  protected _onDestroy = new Subject<void>();
  savedConfig: any = null;
  reportConfig: any = null;
  constructor(public dialogRef: MatDialogRef<InputScreenDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private dialog: MatDialog,private _reportService: ReportDashboardServiceService,
    @Inject(TOASTER_MESSAGE_SERVICE_TOKEN)  private toasterService: ToasterMessageService
  ) { }

  async ngOnInit(): Promise<void> {
    this.reportConfig = this.dialogData['reportConfig']
    this.updateFormConfig();
    await this.updateSavedConfig();
    this.regionListMaster = await this.getRegionList();
    this.organizationDetailsFormListener();
  }

  organizationDetailsFormListener() {
    this.inputScreenForm
      .get('region')
      .valueChanges.subscribe(async (res: any) => {
        this.inputScreenForm.get('division').reset();
        this.inputScreenForm.get('sub_division').reset();

        this.divisionListMaster = []
        this.subdivisionListMaster = []
        if(res){
          this.divisionListMaster = await this.getDivision(res);
        }
      });
    this.inputScreenForm
      .get('division')
      .valueChanges.subscribe(async (res: any) => {
        this.inputScreenForm.get('sub_division').reset();

        let region_id = this.inputScreenForm.get('region').value;
        let division_id = this.inputScreenForm.get('division').value;
        this.subdivisionListMaster = []
        if(region_id && division_id){
          this.subdivisionListMaster = await this.getSubDivision(region_id,division_id);
        }
      });
  }

  getRegionList() {
    return new Promise((resolve) => {
      this.subs.sink = this._reportService.getRegionList().subscribe(
        (res: any) => {
          if(res['messType'] == 'S')
            resolve(res.data);
          else{
            resolve([])
          }
        },
        (err) => {
          console.log(err);
          resolve([]);
        }
      );
    });
  }

  getDivision(entity) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reportService.getDivision(entity).subscribe(
        (res: any) => {
          if(res['messType'] == 'S')
            resolve(res.data);
          else{
            resolve([])
          }
        },
        (err) => {
          console.log(err);
          resolve([])
        }
      );
    });
  }

  getSubDivision(entity, division) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reportService
        .getSubDivision(entity, division)
        .subscribe(
          (res: any) => {
            if(res['messType'] == 'S')
              resolve(res.data);
            else{
              resolve([])
            }
          },
          (err) => {
            console.log(err);
            resolve([])
          }
        );
    });
  }

  async apply(){
    if (this.inputScreenForm.invalid) {
      if (!this.inputScreenForm.get('region')?.value) {
        this.toasterService.showWarning('Region is Mandatory',10000);
      }
      return;
    }
    await this.saveConfig();
    this.dialogRef.close({
      messType:'S',
      data:[]
    })
  }

  /**
   * @description Updating Based on Saved Config
   */
  async updateSavedConfig(){
    this.savedConfig = await this.getSavedConfig();
    this.savedConfig = (this.savedConfig && this.savedConfig['reportConfig']) ? this.savedConfig['reportConfig'] : null;
    // if(this.savedConfig){
    //   //Updating filter data
    //   if(this.savedConfig['filterData']){
    //     this.savedConfig['filterData'].forEach(item => {
    //       // Check if the FormGroup contains a control with the same name as the type
    //       if (this.inputScreenForm.contains(item.type)) {
    //         // Use patchValue to set the data for the corresponding control
    //         this.inputScreenForm.get(item.type)?.patchValue(item.data);
    //       }
    //     });
    //   }

    // }
  }

  /**
   * @description Getting Saved Config
   * @returns
   */
  getSavedConfig() {
    return new Promise((resolve, reject) => {
      this._reportService
        .getSavedConfig(
          this.dialogData['reportId']
          )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              resolve(res['data']);
            }
            resolve(null);
          },
          error: (err) => {
            console.log(err);
            resolve(null);
          },
        });
    });
  }

  /**
   * @description Save User Config
   * @returns
   */
  saveUserConfig(reportConfig) {
    let currentDate = moment().format();
    return new Promise((resolve, reject) => {
      this._reportService
        .saveUserConfig(
          this.dialogData['reportId'],
          reportConfig,
          currentDate
          )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              resolve(true);
            }
            else
            resolve(false);
          },
          error: (err) => {

            console.log(err);
            resolve(false);
          },
        });
    });
  }


  saveConfig(){
    
    // Usage
    const masterLists = {
      regionListMaster: this.regionListMaster,
      divisionListMaster: this.divisionListMaster,
      sub_divisionListMaster: this.subdivisionListMaster,
    };

    const { filterData, urlLinkData, groupTypeSettings } = this.constructDataFromLinkSettings(
      (this.reportConfig?.['grid_settings']?.['link_settings'] || []), this.inputScreenForm, masterLists
    );

    let groupBySelected = (this.savedConfig && this.savedConfig['groupBySelected']) ? this.savedConfig['groupBySelected'] : this.dialogData['groupBySelected']
    const lastUrlLink = urlLinkData[urlLinkData.length - 1];

    // If last item exists, patch viewTypeData with link_url
    if (lastUrlLink && lastUrlLink.link_url) {
      groupBySelected = lastUrlLink.link_url
    }

    let columnList = this.reportConfig['grid_settings']['column_settings'] ? this.reportConfig['grid_settings']['column_settings'] : [];
    let group_settings = this.reportConfig['grid_settings']['group_settings'] ? this.reportConfig['grid_settings']['group_settings'] : {};
    let groupList = group_settings['groupList'] ? group_settings['groupList'] : [];

    let card_settings = (this.reportConfig?.card_config?.card_items.length > 0 && this.reportConfig?.card_config?.is_active) ? this.reportConfig['card_config']['card_items'] : []; 
    
    let reportConfig = {
      "card_settings": (this.savedConfig && this.savedConfig['card_settings']) ? this.savedConfig['card_settings'] : card_settings,
      "viewTypeSelected": (this.savedConfig && this.savedConfig['viewTypeSelected']) ? this.savedConfig['viewTypeSelected'] : this.dialogData['viewTypeSelected'],
      "groupBySelected": groupBySelected,
      "urlLinkData": urlLinkData,
      "column_settings": (this.savedConfig && this.savedConfig['column_settings']) ? this.savedConfig['column_settings'] : columnList,
      "group_settings" :  (this.savedConfig && this.savedConfig['group_settings']) ? this.savedConfig['group_settings'] : groupList,
      "units": (this.savedConfig && this.savedConfig['units']) ? this.savedConfig['units'] : this.dialogData['units'],
      "filterData": filterData,
      "groupTypeSettings": groupTypeSettings
    }
    this.saveUserConfig(reportConfig)
  }

  constructDataFromLinkSettings(link_settings, formGroup, masterLists) {
    let filterData = [];
    let urlLinkData = [];
    let groupTypeSettings = [];
    let mismatchFound = false;
    let nullDataAdded = false;

    link_settings.forEach((linkItem) => {
        const formValue = formGroup.get(linkItem.link_url)?.value;
        const masterList = masterLists[`${linkItem.link_url}ListMaster`] || [];
        const matchedData = masterList.find(item => item.id === formValue);

        // If a mismatch is found, stop pushing to filterData and urlLinkData
        if (!mismatchFound && matchedData && formValue) {
            // Construct filterData
            filterData.push({
                type: linkItem.link_url,
                data: formValue,
                position: linkItem.position
            });

            // Construct urlLinkData
            urlLinkData.push({
                ...linkItem,
                data: matchedData  // Only update `data` with the matching item from the master list
            });
            groupTypeSettings.push({
              ...linkItem,
              data: matchedData
            });
        } else {
            mismatchFound = true;

            groupTypeSettings.push({
              ...linkItem,
              data: {}  // Keep data empty for non-matching entries
            });
            if (!nullDataAdded) {
              urlLinkData.push({
                  ...linkItem,
                  data: null // Add a new object after mismatch with null data
              });
              nullDataAdded = true; // Set the flag to true after adding the null data entry
          }
        }
    });

    return { filterData, urlLinkData, groupTypeSettings };
  }

  async onCloseClick(){
    this.dialogRef.close(null);
  }

  updateFormConfig(){
    let formConfig = this.dialogData['formConfig'] ? this.dialogData['formConfig'] : null;
    if(formConfig){
      const retrieveStyles = _.where(formConfig, {
        type: 'project-theme',
        field_name: 'styles',
        is_active: true,
      });
      let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    }
  }

}
