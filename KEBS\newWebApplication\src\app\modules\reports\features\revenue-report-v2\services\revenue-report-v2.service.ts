import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ErrorService } from 'src/app/services/error/error.service';
import { catchError } from 'rxjs/operators';
import { EMPTY } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RevenueReportV2Service {

  constructor(private http: HttpClient, private snackBar : MatSnackBar, private errorService: ErrorService) { }

   // To save report 
   saveReportState(state, name, field_conf, application_id) {
    return this.http.post("api/userExperience/saveReportState", { state: state, name: name, field_conf: field_conf, application_id: application_id }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    );
  }

  // To update report state 
  updateReportState(state, customization_id, field_conf, application_id) {
    return this.http.post("api/userExperience/updateReportState", { application_id, state: state, customization_id: customization_id, field_conf: field_conf }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    );
  }

  // TO get user report views
  getReportUserViews(application_id) {
    return this.http.post("api/userExperience/getReportUserViews", { application_id: application_id }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    )
  }

  // TO delete report variant
  deleteVariant(application_id, customization_id) {
    return this.http.post("api/userExperience/deleteVariant", { application_id: application_id, customization_id: customization_id }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    )
  }

  getRevenueData(startDate, endDate, report_id){
    return new Promise((resolve, reject) => {
      this.http.post("api/misFunctions/getRevenueData", {
        start_date: startDate,
        end_date: endDate,
        report_id: report_id
      }).subscribe(res => {
        console.log(res);
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })

  }

  getRevenueReportListData(){
    return new Promise((resolve, reject) => {
      this.http.post('/api/misFunctions/getRevenueReportListData', {}).subscribe(
        res => {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    });
  }

  getDefaultFyData(){
    return new Promise((resolve, reject) => {
      this.http.post('/api/misFunctions/getCurrentFYDetails', {}).subscribe(
        res => {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    });
  }

  revenueForecastAuthorizationValidation() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/misFunctions/revenueForecastAuthorizationValidation", {}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }

}
