import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from "underscore";
@Injectable({
  providedIn: 'root'
})
export class RrProjectionsService {

  constructor(private $http: HttpClient, private rolesService: RolesService) { }

  getColumnConfigForRRReport = (reportId) => {
    return this.$http.post("/api/misFunctions/getColumnConfigForRRReport", {
      reportId: reportId
    });
  };
  getEmpRevenueProjectionLive = (selectedVal) => {
    return this.$http.post("/api/misFunctions/getEmpRevenueProjectionLive", {
      projectId: selectedVal
    });
  };
  getEmpRevenueProjectionInactive = () => {
    return this.$http.post("/api/misFunctions/getEmpRevenueProjectionInactive", {
    });
  };
  getEmpRevenueProjectionActiveVersion = (versionId) => {
    return this.$http.post("/api/misFunctions/getEmpRevenueProjectionActiveVersion", {
      versionId: versionId
    });
  };
  getEmpRevenueProjectionVersionHistory = () => {
    return this.$http.post("/api/misFunctions/getEmpRevenueProjectionVersionHistory", {
    });
  };

  getMasterVersions = (startIndex, offSet) => {
    return this.$http.post("/api/misFunctions/getMasterVersions", {
      startIndex: startIndex,
      offSet: offSet
    });
  };

  getActiveVersion = () => {
    return this.$http.post("/api/misFunctions/getActiveVersion", {
    });
  };

  getTotalRecords = (reportId) => {
    return this.$http.post("/api/misFunctions/getTotalRecords", {
      reportId: reportId
    });
  };

  saveLiveVersion(versionName){
    console.log("save version in service")
    console.log(versionName)
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/saveLiveProjection' ,{
        title: versionName
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    });
  }

  changeSavedVersionToCurrentVersion(versionId){
    console.log("change in service")
    console.log(versionId)
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/markAsCurrentProjection' ,{
        savedVersionId:versionId
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    }); 
  }

  getProjectList(searchText,startIndex,offSet){
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/getProjectMaster' ,{
        searchText:searchText,
        startIndex:startIndex,
        offSet:offSet,
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    }); 
  }

  getItemList(searchText,startIndex,offSet){
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/getItemMaster' ,{
        searchText:searchText,
        startIndex:startIndex,
        offSet:offSet,
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    }); 
  }

  getIsaList(searchText,startIndex,offSet){
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/getIsaMasterData' ,{
        searchText:searchText,
        startIndex:startIndex,
        offSet:offSet,
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    }); 
  }

  getEmpProjectionErrorLogsHistory = () => {
    return this.$http.post("/api/misFunctions/getEmpProjectionErrorLogsHistory", {
    });
  };

  getProjectionRunLogs = (project_id) => {
    return this.$http.post("/api/misFunctions/getRRActionLogs", {
      project_id: project_id
    });
  };

  getEmpProjectionManualRunHistory = () => {
    return this.$http.post("/api/misFunctions/getEmpProjectionManualRunHistory", {
    });
  };

  insertRRProjectionsLive(type,id){
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/insertRRProjectionsLive' ,{
        type: type,
        id: id
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    }); 
  }

  getBillingAdviceMilestoneList(searchText,startIndex,offSet){
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/getBillingAdviceMilestoneMaster' ,{
        searchText:searchText,
        startIndex:startIndex,
        offSet:offSet,
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    }); 
  }
  getBilledInvoiceMilestoneList(searchText,startIndex,offSet){
    return new Promise((resolve, reject) => {
      this.$http.post('/api/misFunctions/getBilledInvoiceMilestoneList' ,{
        searchText:searchText,
        startIndex:startIndex,
        offSet:offSet,
      }).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    }); 
  }

  getPositionBasedProjection = (selectedVal) => {
    return this.$http.post("/api/misFunctions/getPositionBasedProjection", {
      projectId: selectedVal
    });
  };

  rrIsaBasedManualRunCheckAccess() {

    let accessList = _.where(this.rolesService.roles, { application_id: 1001, object_id: 1 });

    if (accessList.length > 0)
      return true;

    else
      return false;
  }
}
