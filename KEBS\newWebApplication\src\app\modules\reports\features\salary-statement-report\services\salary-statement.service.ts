import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})

export class SalaryStatementService {

  constructor(private $http: HttpClient) { }

  
  // Salary Statement Data
  getSalaryStatementData = (salaryStatementTableParams, date) => {
    console.log("getMisTableData")
    return this.$http.post("/api/project/misFunctions/getMisTableData", {
      table_params: salaryStatementTableParams,
      date: date,
      application_id : 319
    });
  };

  // MIS related table Master data
  getMisTableMasterData = () => {
    console.log("misTableMasterData")
    return this.$http.post("/api/project/misFunctions/getMisTableMasterData", {
    });
  };

  
}