import { Component, HostListener, NgModule, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import * as _ from "underscore";

export interface noDataObj {
  noDataCheck: boolean;
  noOpportunity: boolean
}

export interface opportunityList {
  id: number,
  name: string
}

@Component({
  selector: 'quote-crm-quote',
  templateUrl: './crm-quote.component.html',
  styleUrls: ['./crm-quote.component.scss']
})
export class CrmQuoteComponent implements OnInit {

  protected _onDestroy = new Subject<void>();
  protected _onAppApiCalled = new Subject<void>();
  noDataImg: noDataObj = {
    noDataCheck: false,
    noOpportunity: false
  }
  opportunity_id: any;
  itemId: number;
  projectId: number;
  subs = new SubSink();
  opportunityList: Array<opportunityList> = [];
  isQuoteDataLoading: boolean = false;
  isQuoteSelected: boolean = false;
  //UDRF Configurations
  applicationId = 905;
  skip: any = 0;
  limit: any = 15;
  itemDataCurrentIndex: any = 0;
  defaultDataRetrievalCount: any = 15;
  udrfBodyColumns = [
    {
      item: 'quote_position_id',
      header: 'Quote Position Id',
      isVisible: 'true',
      isActive: true,
      type: 'rmg-id',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 140,
      sortOrder: 'N',
      filterId: 3,
    },
    {
      item: 'position_name',
      header: 'Quote Position Name',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 250,
      sortOrder: 'N',
      filterId: 4,
    },
    {
      item: 'quote_id',
      header: 'Quote Id',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 200,
      sortOrder: 'N',
      filterId: 5,
    },
    {
      item: 'display_request_id',
      header: 'Request Id',
      isVisible: 'true',
      isActive: true,
      type: 'rmg-action',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 140,
      sortOrder: 'N',
      filterId: 6,
    },
    {
      item: 'start_date',
      header: 'Request Start Date',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 150,
      sortOrder: 'N',
      filterId: 7,
    },
    {
      item: 'end_date',
      header: 'Request End Date',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 150,
      sortOrder: 'N',
      filterId: 8,
    },
    {
      item: 'status',
      header: 'Status',
      isVisible: 'true',
      isActive: true,
      type: 'statusBadge',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 150,
      sortOrder: 'N',
      filterId: 9,
    },
    {
      item: 'priority',
      header: 'Priority',
      isVisible: 'true',
      isActive: true,
      type: 'rmg-text',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 140,
      sortOrder: 'N',
    },
    {
      item: 'request_count',
      header: 'No of Requests',
      isVisible: 'true',
      isActive: true,
      type: 'rmg-text',
      colSize: '4',
      textClass: 'zf-theme-bold',
      position: 1,
      width: 140,
      sortOrder: 'N',
      filterId: 10,
    },
    {
      item: 'action',
      isActive: true,
      header: 'Action',
      isVisible: 'true',
      type: 'rmg_action',
      position: 14,
      colSize: 1,
      sortOrder: 'N',
      width: 140,

    }

  ];

  formConfig: any;
  inactivePositionStatusForAllocate: any = [];
  udrfItemStatusColor: any;
  createRequestAccess: boolean=false;
  width: number;
  height: number;
  quoteFormGroup = this.formBuilder.group({
    searchOpportunity: [
      null
    ],
  });
  quoteFormPatched: boolean = false;
  constructor(public _udrfService: UdrfService, private _utilityService: UtilityService, private _router: Router,
    private _route: ActivatedRoute, private _tmService: TmService, public dialog: MatDialog, private formBuilder: FormBuilder, private pmAuthService: PmAuthorizationService) { }

  async ngOnInit() {
    this.isQuoteDataLoading = true;
    this.formConfig =  await this.getFormConfig()
    let inactivePositionStatusConfig = _.where(this.formConfig, {type: 'position-card',field_name: 'inactive_position',is_active: true});
    this.inactivePositionStatusForAllocate = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].inactive_status_list_for_allocate ? inactivePositionStatusConfig[0].inactive_status_list_for_allocate  : []) : [])
    
    let statusMasterData = await this.getRequestStatusMasterData();
    this.initStatusData(statusMasterData);
    this.itemId = (this._router.url.split('/')[2] == 'project-management') ? parseInt(this._router.url.split('/')[5]) : parseInt(this._router.url.split('/')[7]);
    this.projectId = (this._router.url.split('/')[2] == 'project-management') ? parseInt(this._router.url.split('/')[3]) : parseInt(this._router.url.split('/')[3]);
    await this.pmAuthService.getReadWriteAccess(this.projectId, this.itemId).then(async(res)=>{
      if(res){
        this.createRequestAccess = await this.pmAuthService.getProjectWiseObjectAccess(this.projectId, this.itemId, 354)
      }
    })

    

    let data = this._route.snapshot.data;
    this.width = data['width'] ? data['width'] : 108
    this.height = data['height'] ? data['height'] : 247
    //For Calculating Dynamic Size
    this.calculateDynamicStyle();
    this.opportunityList = await this.getOpportunityData();
    await this.getQuoteData();
    this.isQuoteDataLoading = false;
  }

  configureUdrf() {
    this._udrfService.udrfData.applicationId = this.applicationId;
    this._udrfService.udrfUiData.showItemDataCount = true;
    this._udrfService.udrfUiData.showSearchBar = true;
    this._udrfService.udrfUiData.showActionButtons = true;
    this._udrfService.udrfUiData.showUdrfModalButton = false;
    this._udrfService.udrfUiData.showColumnConfigButton = true;
    this._udrfService.udrfUiData.showSettingsModalButton = false;
    this._udrfService.udrfUiData.showNewReleasesButton = false;
    this._udrfService.udrfUiData.showReportDownloadButton = false;
    this._udrfService.udrfUiData.isReportDownloading = false;
    this._udrfService.udrfUiData.itemHasOpenInNewTab = false;
    this._udrfService.udrfUiData.horizontalScroll = true;
    this._udrfService.udrfUiData.itemHasQuickCta = false;
    this._udrfService.udrfUiData.collapseAll = false;
    this._udrfService.udrfUiData.showCollapseButton = true;
    this._udrfService.udrfUiData.countForOnlyThisReport = false;
    this._udrfService.udrfUiData.toggleChecked = false;
    this._udrfService.udrfUiData.countFlag = false;
    this._udrfService.udrfUiData.isMultipleView = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.emailPluginVisible = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.itemHasAttachFileButton = false;
    this._udrfService.udrfUiData.itemHasComments = false;
    this._udrfService.udrfUiData.isMoreOptionsNeeded = false;
    this._udrfService.udrfUiData.showAddMember = false
    this._udrfService.udrfUiData.isModalSortVisible = false;
    // this._udrfService.udrfUiData.completeProfileBtn = true;
    this._udrfService.udrfUiData.variant = 1;
    this._udrfService.udrfUiData.itemDataType = '';
    this._udrfService.udrfUiData.searchPlaceholder = 'by Quote Position';
    this._udrfService.udrfUiData.totalItemDataCount = 0;
    this._udrfService.udrfUiData.itemCardSelecteditem = {};
    this._udrfService.udrfUiData.iteml2CardSelectedItem = {};
    this._udrfService.udrfUiData.inlineEditData = {};
    this._udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns;
    this._udrfService.udrfUiData.udrfVisibleBodyColumns =
      this._udrfService.udrfUiData.udrfVisibleBodyColumns;
    this._udrfService.udrfUiData.udrfInvisibleBodyColumns =
      this._udrfService.udrfUiData.udrfInvisibleBodyColumns;
    this._udrfService.udrfUiData.quickCTAInput = {};
    this._udrfService.udrfUiData.commentsInput = {};
    this._udrfService.udrfUiData.commentsContext = {};
    this._udrfService.udrfUiData.updateItemCard = () => { };
    this._udrfService.udrfUiData.attachFile = () => { };
    this._udrfService.udrfUiData.deleteFile = () => { };
    this._udrfService.udrfUiData.downloadFile = () => { };
    this._udrfService.udrfUiData.closeCTA = () => { };
    this._udrfService.udrfUiData.updateItemCardData = {};
    this._udrfService.udrfUiData.attachFileData = {};
    this._udrfService.udrfUiData.deleteFileData = {};
    this._udrfService.udrfUiData.downloadFileData = {};
    this._udrfService.udrfUiData.closeCTAData = {};
    this._udrfService.udrfUiData.onProgressItem = () => { };
    this._udrfService.udrfUiData.onCompleteItem = () => { };
    this._udrfService.udrfUiData.onCompleteItemResponse = '';
    this._udrfService.udrfUiData.onColumnClickItem = '';
    this._udrfService.udrfUiData.collapseAllCard = () => { };
    this._udrfService.udrfUiData.countResponseData = [];
    this._udrfService.udrfUiData.emailPluginData = {};
    this._udrfService.udrfUiData.summaryCards = [];
    this._udrfService.udrfUiData.itemHasMoreActions = false;
    this._udrfService.udrfUiData.moreActionItemClickedData = {};
    this._udrfService.udrfUiData.openAllocationRequest = this.openAllocationRequest.bind(this);
    this._udrfService.udrfUiData.itemDataScrollDown =
      this.onUdrfItemDataScrollDown.bind(this);
    this._udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor;
    this._udrfService.getAppUdrfConfig(
      this.applicationId,
      this.initReport.bind(this)
    );

    this._udrfService.getNotifyReleasesUDRF();
    this._udrfService.udrfFunctions.resolveVisibleColumnConfigItems();
    this._udrfService.udrfUiData.resolveColumnConfig();

    this._udrfService.udrfUiData.openRequestDetail =
      this.openRequestDetail.bind(this);

    this.quoteFormGroup.patchValue({ searchOpportunity: this.opportunity_id });
  };

  initReport = () => {
    this._onAppApiCalled.next();
    this.itemDataCurrentIndex = 0;
    this._udrfService.udrfBodyData = [];
    this._udrfService.udrfUiData.resolveColumnConfig();
    this._udrfService.udrfData.isItemDataLoading = true;
    this.skip = 0;
    this.limit = this.defaultDataRetrievalCount;
    this.getPositionData(false);
  };

  openAllocationRequest() {
    let requestData = this._udrfService.udrfUiData.itemCardSelecteditem;
    requestData['opportunity_id'] = this.opportunity_id;
    // console.log("Request Data: ",requestData);
    this._tmService.setQuoteData(requestData);
    this._router.navigate(['request-form'], { relativeTo: this._route });
  }

  /**
   * @description get Position List data
   */

  getPositionData = (infiniteScrollFlag) => {
    this._udrfService.udrfData.isItemDataLoading = true;
    let mainFilterArray = JSON.parse(
      JSON.stringify(this._udrfService.udrfData.mainFilterArray)
    );

    let filterConfig = {
      item_id: this.itemId,
      opportunity_id: this.opportunity_id,
      startIndex: this.skip,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
    };

    this._tmService
      .getCRMQuotePositionList(filterConfig)
      .pipe(takeUntil(this._onDestroy))
      .pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {
          if (!res['err'] && res['data'].length > 0) {
            // res['data'] = this.utcDateFormat(res['data'])

            for(let data of res['data'])
            {
              if((!(this.inactivePositionStatusForAllocate.length > 0 && _.includes(this.inactivePositionStatusForAllocate,data.position_status))) && this.createRequestAccess)
              {
                data['isVisible']=true
              }
            }
            this._udrfService.udrfBodyData =
              this._udrfService.udrfBodyData.concat(res['data']);
          } else {
            this._udrfService.udrfBodyData =
              this._udrfService.udrfBodyData.concat([]);
          }
          if (this.skip == 0) await this.getTotalCount(filterConfig);
          if (this._udrfService.udrfBodyData.length == 0) {
            this.noDataImg.noDataCheck = true;
          }
          else {
            this.noDataImg.noDataCheck = false;
          }
          this._udrfService.udrfData.isItemDataLoading = false;
        },
        (err) => {
          this._udrfService.udrfBodyData = [];
          this._udrfService.udrfData.isItemDataLoading = false;
          console.log(err);
        }
      );
  };

  getOpportunityData = async (): Promise<Array<opportunityList> | null> => {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getOpportunityData(this.itemId).subscribe(
        (res: any) => {
          if (!res.err && res.data.length > 0) {
            this.noDataImg.noOpportunity = false;
            resolve(res.data);
          }
          else {
            this.noDataImg.noOpportunity = true;
            resolve(null);
          }
        },
        (err) => {
          console.log(err);
          this.isQuoteDataLoading = false;
          this.noDataImg.noOpportunity = true;
          reject(err);
        }
      );
    });
  }


  /**
   * @description on udrf item scroll down
   */
  onUdrfItemDataScrollDown() {
    // console.log("Scroll is called!!!");
    if (!this._udrfService.udrfData.noItemDataFound) {
      if (!this._udrfService.udrfData.isItemDataLoading) {
        this.skip += this.defaultDataRetrievalCount;

        this.getPositionData(true);
      }
    }
  }

  async getTotalCount(filterConfig) {
    await this._tmService
      .getCRMQuotePositionCount(filterConfig)
      .pipe(takeUntil(this._onDestroy))
      .pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res: any) => {
          if (!res.err)
            this._udrfService.udrfUiData.totalItemDataCount = res['total_count'];
        },
        (err) => {
          console.log(err);
        }
      );
  }

  initStatusData = (data) => {
    let udrfItemStatusColor = [];
    data.forEach((e, i) => {
      udrfItemStatusColor.push({
        status: e.status,
        color: e.color,
      });
    });

    this.udrfItemStatusColor = udrfItemStatusColor;
  };

  /**
   * @description get status master
   */
  getRequestStatusMasterData() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getRequestStatusList().subscribe(
        (res: any) => {
          if (!res.err) {
            resolve(res.data);
          } else {
            reject(res);
          }
        },
        (err) => {
          console.log(err);
        }
      );
    });
  }

  async openRequestDetail() {
    let requestData = this._udrfService.udrfUiData.iteml2CardSelectedItem;
    const { RequestedResourceDetailComponent } = await import(
      '../../../resource-request/components/requested-resource-detail/requested-resource-detail.component'
    );
    const requestDetail = this.dialog.open(
      RequestedResourceDetailComponent,
      {
        disableClose: true,
        width: "90%",
        height: "100%",
        position: { top: '0px', right: '0%' },
        data: {
          modalParams: requestData,
          status: this.udrfItemStatusColor
        }
      }
    );
    requestDetail.afterClosed().subscribe((result) => {
      const udrfData = this._udrfService.udrfBodyData;

      // Creating a dictionary to map quote_position_id to their corresponding data
      const quotePositionData = {};

      for (const data of udrfData) {
        quotePositionData[data['quote_position_id']] = data;
      }

      // Iterate through requestData and result objects
      for (const data of udrfData) {
        if (requestData['quote_position_id'] == data['quote_position_id']) {
          for (const l2Data of data['l2']) {
            if (l2Data['request_id'] == result['request_id']) {
              l2Data['status'] = result['status'];
              break;
            }
          }
        }
      }

    });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle() {
    let headerWidth = window.innerWidth - this.width + 92 + 'px'
    let tableWidth = window.innerWidth - this.width + 'px'
    document.documentElement.style.setProperty(
      '--dynamicHeaderWidth',
      headerWidth
    )
    document.documentElement.style.setProperty(
      '--dynamicTableWidth',
      tableWidth
    )
    let tableHeight = window.innerHeight - this.height - 44 + 'px'
    let tableHeightShorted = window.innerHeight - this.height - 52 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicTableHeight',
      tableHeight
    )
    document.documentElement.style.setProperty(
      '--dynamicTableHeightShortened',
      tableHeightShorted
    )
  }

  handleInputChange(event) {
    console.log('QuoteForm Value:', this.quoteFormGroup.value);
    this.isQuoteSelected = true;
    console.log(event);
    this.opportunity_id = event.id
    this._udrfService.resetUdrfData();
    this.configureUdrf();
  }

  async getQuoteData() {
    let quoteData;
    this.subs.sink = this._tmService.selectedQuote$.subscribe(async (value) => {
      quoteData = value;
      console.log(quoteData);
      if (quoteData) {
        this.opportunity_id = quoteData.opportunity_id;
      } else {
        this.opportunity_id = this.opportunityList.length > 0 ? this.opportunityList[0].id : null;
      }

      await this.patchSearchValue();
    });
  }

  async patchSearchValue() {
    console.log('Opportunity Id:', this.opportunity_id);
    console.log('opportunityList', this.opportunityList);
    this.quoteFormGroup.patchValue({ searchOpportunity: this.opportunity_id });
    if (this.opportunity_id) {
      this.quoteFormGroup.patchValue({ searchOpportunity: this.opportunity_id });
      console.log('QuoteForm Value:', this.quoteFormGroup.value);
      this.quoteFormPatched = true;
      this.isQuoteSelected = true;
      this._udrfService.resetUdrfData();
      this.configureUdrf();
    } else {
      this.quoteFormGroup.get('searchOpportunity').patchValue(null);
      console.log(this.quoteFormGroup.value);
      this.quoteFormPatched = true;
    }
  }

  ngOnDestroy() {
    // this._onDestroy.next();
    // this._onDestroy.complete();

    this.subs.unsubscribe();
    this._udrfService.resetUdrfData();
  }


  getFormConfig(){

    return new Promise((resolve, reject) => {

      this.subs.sink = this._tmService.getPMFormCustomizeConfigV().subscribe((res: any) => {
        if (res && res['messType']=="S") {
          resolve(res['data'])
        }
        else{
          resolve([])
        }
      },(err)=>{
        reject(err)
      });
      
    });
    
  }

}

import { CommonModule } from '@angular/common';
import { UtilityService } from 'src/app/services/utility/utility.service';
//import { _udrfService } from 'src/app/services/udrf/udrf.service';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { Subject } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TmService } from '../../../../services/tm.service';
import { takeUntil } from 'rxjs/operators';
import { SubSink } from 'subsink';
import { MatDialog } from '@angular/material/dialog';
import { MatDialogModule } from '@angular/material/dialog';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';

@NgModule({
  declarations: [CrmQuoteComponent],
  imports: [CommonModule, SharedComponentsModule, MatCheckboxModule, MatIconModule],
  exports: []
})
export class CRMQuoteModule { }
