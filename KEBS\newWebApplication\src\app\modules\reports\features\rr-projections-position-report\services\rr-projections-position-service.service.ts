import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Subject,Observable } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
@Injectable({
  providedIn: 'root'
})
export class RrProjectionsPositionServiceService {

  constructor(private http: HttpClient,private snackBar : MatSnackBar) { }
  getRRProjectData(startIndex, offset, filterConfig, currency){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/misFunctions/getRRProjectionProjectLevelDetails',{
          noOfRecords: offset,
          startIndex: startIndex,
          filterConfig: filterConfig,
          currencyCode :currency
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open(res['messText'], 'Dismiss', { duration: 2000 })
                return resolve(res)
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getRRPositionData(id,currency,filterConfig){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/misFunctions/getRRProjectionQuoteLevelDetails',{
          project_item_id : id,
          currencyCode :currency,
          filterConfig: filterConfig
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing project data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getRREmployeeData(position_id,project_id,currency,filterConfig){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/misFunctions/getRRProjectionEmployeeLevelDetails',{
          project_item_id : project_id,
          quote_position_id: position_id,
          currencyCode :currency,
          filterConfig: filterConfig
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing project data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getTenantInfo = () => {
    return this.http.post('/api/misFunctions/getTenantFY', {
    });
  }

  getRRBilledData(){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/invoice/getRRProjectWiseBilledValue',{
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing billed data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getRRARData(){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/invoice/getRRProjectWiseARValue',{
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing AR data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getRRSummaryData(filterConfig){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/misFunctions/getRRProjectionSummaryDetails',{
          filterConfig
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open(res["messText"], 'Dismiss', { duration: 2000 })
                return resolve(res)
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getRRSummaryBilledValue(filterConfig){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/invoice/getRRSummaryBilledValue',{
          filterConfig
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing Projection report summary data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getRRSummaryARValue(filterConfig){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/invoice/getRRSummaryARValue',{
          filterConfig
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing Projection report summary data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getRRActualRevenueValue(filterConfig){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/misFunctions/getRRActualRevenue',{
          filterConfig
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing Projection report summary data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }

  getFinancialYearDetails(){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/misFunctions/getFinancialYearDetails',{})
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.spinnerService.hide();
              this.snackBar.open(res["messText"], "Dismiss", {
                duration: 5000
              });
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.spinnerService.hide();
              this.snackBar.open(err["messText"], "Dismiss", {
                duration: 5000
              });
            return reject(err)}
        }
        )
      })
    }
    catch(err){
      return Promise.reject();
    }
  }

  getProjectTotalCount(filterConfig){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/misFunctions/getProjectTotalCount',{
          filterConfig: filterConfig
        })
        .subscribe({
          next: (res)=> {
            if(res['messType']== 'E'){
              // this.spinnerService.hide();
              this.snackBar.open(res["messText"], "Dismiss", {
                duration: 5000
              });
            }
            return resolve(res)
          },
          error: (err)=>{
            // this.spinnerService.hide();
              this.snackBar.open(err["messText"], "Dismiss", {
                duration: 5000
              });
            return reject(err)}
        }
        )
      })
    }
    catch(err){
      return Promise.reject();
    }
  }

  getBilledDataWithTax(){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('/api/invoice/getRRProjectWiseTotalBilledValue',{
        })
          .subscribe({
            next: (res) => {
              if (res['messType'] == 'E') {
                this.snackBar.open('Error in retrieveing billed data', 'Dismiss', { duration: 2000 })
              }
              return resolve(res)
            },
            error: (err) => {
              console.log(err);
              return reject(err)
            }
          }
          )
      })
    }
    catch (err) {
      return Promise.reject();
    }
  }
}
