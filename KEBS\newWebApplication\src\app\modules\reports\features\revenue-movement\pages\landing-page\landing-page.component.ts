import { Component, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { DxDataGridComponent } from 'devextreme-angular';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from "@angular/material/snack-bar";
import { RevenueMovementService } from '../../services/revenue-movement.service';
import { Subject } from 'rxjs';
import { MatSelect } from '@angular/material/select';
import { debounceTime } from 'rxjs/operators';
import { takeUntil } from "rxjs/operators";
import * as moment from 'moment';
import { RevenueLogComponent } from '../../components/revenue-log/revenue-log.component';
import { NgxSpinnerService } from 'ngx-spinner';
import { Router } from '@angular/router';
@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss']
})
export class LandingPageComponent implements OnInit {
  @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
  @ViewChild('onScroll', { static: false }) onScroll: MatSelect;
  milestoneId = new FormControl();
  milestoneDetails: any;
  originalMilestoneDetails: any;
  data: any;
  milestoneOptions: any = [];
  startIndex: any = 0;
  offSet: any = 50;
  searchCtrl = new FormControl();
  protected _onDestroy = new Subject<void>();
  minDate: any;
  maxDate: any;
  debugDate: Date = new Date();
  isAuthorized: boolean = true;
  constructor(private fb: FormBuilder, private revenueMovementService: RevenueMovementService, private openMatDialog: MatDialog, private snackBar: MatSnackBar, 
  private spinnerService: NgxSpinnerService, private _router: Router) { }

  async ngOnInit() {
    await this.revenueMovementAuthorizationValidation()
    let searchValue = await this.getMilestoneMaster();
    this.searchCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy), debounceTime(700))
      .subscribe(async val => {
        if (val != null && val != '') {
          this.milestoneOptions = [];
          this.startIndex = 0;
          this.offSet = 50;
          let searchValue = await this.getMilestoneMaster();
        }
      })
  }

  getMilestoneDetails() {
    this.milestoneDetails = [];
    this.originalMilestoneDetails = [];
    this.spinnerService.show();
    return new Promise((resolve, reject) => {
      this.revenueMovementService.getMilestoneData(this.milestoneId.value).subscribe((res: any) => {
        console.log("res")
        console.log(res)
        this.data = res.data
        this.milestoneDetails = res['data']
        this.originalMilestoneDetails = JSON.parse(JSON.stringify(res['data']));
        // let misDate = res ? res['misDate'] : moment().startOf('month').toDate();
        this.minDate = res['minDate']  // Converts moment object to Date
        this.maxDate = res['maxDate']
        console.log("inside function")
        resolve(res.data)
        this.spinnerService.hide();
      }, err => {
        reject(err);
        this.spinnerService.hide();
      })
    })
    
  }

  getMilestoneMaster() {
    let searchValue = this.searchCtrl.value;
    return new Promise((resolve, reject) => {
      this.revenueMovementService.getMilestoneMaster(this.startIndex, this.offSet, searchValue).subscribe((res: any) => {
        this.data = res.data
        this.milestoneOptions = this.milestoneOptions.concat(res.data)
        resolve(res.data)
      }, err => {
        reject(err);
      })
    })
  }

  // Infinite scroll in mat select
  onOpenedChange(event: any, select: string) {
    if (event) {
      this[select].panel.nativeElement.addEventListener(
        'scroll',
        (event: any) => {
          let scrollTop = Math.ceil(this[select].panel.nativeElement.scrollTop);
          let scrollHeight = this[select].panel.nativeElement.scrollHeight - this[select].panel.nativeElement.offsetHeight;
          if (
            scrollTop - 1 == scrollHeight || scrollTop == scrollHeight || scrollTop + 1 == scrollHeight
          ) {
            this.startIndex = this.startIndex + this.offSet;
            // this.offSet+=15;
            let searchResults = this.getMilestoneMaster();
          }
        }
      );
    }
  }

  async cancelSearchValue() {
    this.searchCtrl.patchValue('');
    this.startIndex = 0;
    this.offSet = 50;
    this.milestoneOptions = [];
    let res = await this.getMilestoneMaster()
    // this.milestoneOptions = res['data'];

  }

  async onSaved(e) {
    console.log(e)
    let updatedData = e.changes[0].data;

    console.log(this.originalMilestoneDetails)
    let originalRow = this.originalMilestoneDetails?.find(row =>
      row["revenue_type"] == updatedData.revenue_type &&
      row["milestone_id"] == updatedData.milestone_id &&
      row["revenue"] == updatedData.revenue
    );

    this.updateRevenueDetails(updatedData, originalRow)

  }

  async updateRevenueDetails(updatedData, originalRow) {
    let result = await this.revenueMovementService.updateRevenueDetails(updatedData, originalRow).toPromise();
    if (result["messType"] == "S") {
      this.snackBar.open(result['messText'] ? result['messText'] : 'Revenue Data Updated Successfullly', 'dismiss', { duration: 5000 });
      this.getMilestoneDetails()
      this.spinnerService.hide();
    }
    else {
      this.spinnerService.hide();
      this.snackBar.open('Error in updating revenue data', 'dismiss', { duration: 5000 });
    }
  }

  // To open a dialog 
  rowInstance = Event;
  rowInstanceData: any;
  clicked: boolean = false;
  id: any;
  getChangeLog: any;
  async onColumnNameCellClick(event: any) {

    this.rowInstance = event;
    console.log("event")
    console.log(event)
    console.log(event?.column?.dataField)
    if (event.data && event?.column?.dataField == 'change_log') {
      this.rowInstanceData = event.data;
      this.id = this.rowInstanceData.milestone_id;
      if (this.id) {
        this.getChangeLog = await this.revenueMovementService.getRevenueChangeLog(this.id)
        if (this.getChangeLog["messType"] == "E") {
          return this.snackBar.open(this.getChangeLog['message'], "Dismiss");
        }
        if (!this.clicked) {
          this.clicked = true;
          let dialogRef = this.openMatDialog.open(RevenueLogComponent,
            {
              height: '60%',
              width: '40%',
              data: { logData: this.getChangeLog["data"] }
            })

          dialogRef.afterClosed().subscribe(() => {
            this.clicked = false;
          });
        }
      }



    }
  }

  statusCellTemplate = (container, options) => {
    const iconClass = "dx-icon-info"
    const icon = document.createElement("i");
    icon.className = iconClass;
    container.appendChild(icon);
    if (options.data.dataField == "changed_log") {
    }
  }

  async revenueMovementAuthorizationValidation(){
   let roleAccessFilters = await this.revenueMovementService.revenueMovementAuthorizationValidation();
    this.isAuthorized = true;
    if (roleAccessFilters['messType'] != "S"){
      this.isAuthorized = false;
      this._router.navigateByUrl("/main/reports")
      return this.snackBar.open(roleAccessFilters['messText'], 'Dismiss', { duration: 2000 })
    }
  }

  getSelectedTooltip(): string {
    const selectedOption = this.milestoneOptions?.find(option => option?.id == this.milestoneId?.value);
    return selectedOption ? `${selectedOption?.id} - ${selectedOption?.name}` : '';
}
}
