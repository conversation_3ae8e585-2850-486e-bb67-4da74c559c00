import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PmInternalStakeholderService } from '../../services/pm-internal-stakeholder.service';
import * as _ from 'underscore';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import moment from 'moment';
import { takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { SubSink } from 'subsink';
@Component({
  selector: 'app-position-card',
  templateUrl: './position-card.component.html',
  styleUrls: ['./position-card.component.scss']
})
export class PositionCardComponent implements OnInit {
  @Input() position:any
  @Input() noQuote:boolean = false;
  @Input() noQuoteList:any = []
  @Output() triggerPositionEvent: EventEmitter<any> = new EventEmitter<any>();
  data:any;
  allExpand:boolean = false;
  formConfig:any;
  itemId:any;
  projectId:any;
  protected _onDestroy = new Subject<void>();
  enable_external_employee_id:boolean=false
  parentData:any = [];
  subs = new SubSink();
  isRemainingDataLoading: boolean = false;
  popUpLogData : any;
  popUpPlannedLogData: any;
  isPlannedDataLoading: boolean = false;
  planned_allocated_hours: any = 0;
  triggerOrigin: any;
  showIdentity: boolean=false;
  start = 0;
  actionList: any = [];
  inactivePositionStatusForAllocate: any = [];
  inactivePositionStatusForEdit: any = [];
  inactivePositionStatus: any =[];
  inactivePositionAddWarningMsg: string = '';
  inactivePositionEditWarningMsg: string = '';
  logPopUp = false;
  milestoneConfigDetails: any;
  isMilestoneVisible: boolean = false;
  constructor(private masterService: PmMasterService,public _isService: PmInternalStakeholderService,private toasterService: ToasterMessageService,private router: Router, private PmInternalStakeholderService: PmInternalStakeholderService) { }

  async ngOnInit(): Promise<void> {
    this.itemId = parseInt(this.router.url.split('/')[5]);
    this.projectId = parseInt(this.router.url.split('/')[3]);
    await this.getSubscribedData();
    await this.masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });

    let show_identity = _.findWhere(this.formConfig, {type:"project-team", field_name:"show-identity", is_active: true})

    this.showIdentity = show_identity ? true: false;

    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });
    let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    this.allExpand = this.position['expandAll'] ? this.position['expandAll'] : false
    let enable_external_employee_id_data = _.where(this.formConfig, {
      type: 'project-team',
      field_name: 'enable_external_employee_id',
      is_active: true,
    });
    let button =
          retrieveStyles.length > 0
            ? retrieveStyles[0].data.button_color
              ? retrieveStyles[0].data.button_color
              : '#90ee90'
            : '#90ee90';
    document.documentElement.style.setProperty('--teamButton', button);
    this.enable_external_employee_id=enable_external_employee_id_data.length>0 ? true :false

    let inactivePositionStatusConfig = _.where(this.formConfig, {
      type: 'position-card',
      field_name: 'inactive_position',
      is_active: true,
    });

    this.inactivePositionStatus = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].inactive_position_status ? inactivePositionStatusConfig[0].inactive_position_status  : []) : [])
    this.inactivePositionStatusForAllocate = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].inactive_status_list_for_allocate ? inactivePositionStatusConfig[0].inactive_status_list_for_allocate  : []) : [])
    this.inactivePositionStatusForEdit = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].inactive_status_list_for_edit ? inactivePositionStatusConfig[0].inactive_status_list_for_edit  : []) : [])
    this.inactivePositionAddWarningMsg = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].add_warning_msg ? inactivePositionStatusConfig[0].add_warning_msg  : 'Allocation is restricted for inactive quote position') : 'Allocation is restricted for inactive quote position');
    this.inactivePositionEditWarningMsg = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].edit_warning_msg ? inactivePositionStatusConfig[0].edit_warning_msg  : 'Edit allocation details is restricted for inactive quote position') : 'Edit allocation details is restricted for inactive quote position')

    let milestoneConfig = _.where(this.formConfig,{
      type: 'position-card',
      field_name: 'milestone_name',
      is_active: true,
    });

    this.milestoneConfigDetails = (milestoneConfig && milestoneConfig.length > 0 ? (milestoneConfig[0].applicable_service_type_id ? milestoneConfig[0].applicable_service_type_id  : []) : []);
    this.isMilestoneVisible = this.milestoneConfigDetails.includes(this.PmInternalStakeholderService.projectServiceType)

    

    this.data = await this.getPositionData(this.position)
    await this.getParentData();
    
    if(this.allExpand){
      for (let items of this.parentData) {
        items['expand'] = 1;
      }
    }

    let remainingInfo = _.where(this.formConfig, {
      type: 'position-card',
      field_name: 'remaining_info',
      is_active: true,
    });
    this.actionList = (remainingInfo && remainingInfo.length > 0 ? (remainingInfo[0].action_list ? remainingInfo[0].action_list  : []) : [])
  }

  async getPositionData(position){
    const consumed = await this._isService.getPositionAvailableHours(position.position_id);
    let positionData = {
      position_id: position.position_id,
      position_name: position.position_name,
      rate:15000,
      currency:'INR',
      position_quantity:position.quantity ? position.quantity : 0,
      // consumed: position.consumed ? position.consumed : 0,
      remaining: position.remaining ? position.remaining : 0,
      consumed: consumed,
      // remaining: this.getAvailable(position.quantity ? position.quantity : 0),
      expand:position.expand ? position.expand : false,
      total_billed_hours: position.total_billed_hours ? position.total_billed_hours : 0,
      month_wise_billed_hours: position.month_wise_billed_hours ? position.month_wise_billed_hours : [],
      work_location: position.work_location ? position.work_location : '',
      per_hour_rate: position.per_hour_rate ? position.per_hour_rate : 0,
      quote_currency: position.currency ? position.currency : '',
      service_line:position.service_line ? position.service_line : '',
      position_status: position.position_status ? position.position_status : 1,
      status_name: position.status_name ? position.status_name : 'Open',
      primary_color: position.primary_color ? position.primary_color : '#FFFFFF',
      secondary_color: position.secondary_color ? position.secondary_color : '#8B95A5',
      milestone_name: position.milestone_name ?  position.milestone_name  : '',
      isVisible: _.includes(this.inactivePositionStatus,position.position_status) && await this.getChildData(position.position_id).length == 0 ? false: true
    }
    return positionData
  }

  async onExpandClick(){
    this.data.expand = !this.data.expand
    await this.saveQTCConfig()
  }

  getParentData(){
    let childData = this.getChildData(this.data?.position_id);
    const uniqueIds = this.getUniqueIds(childData);
    this.parentData = [];

    const createParentData = (item: any) => ({
      associate_id: item['associate_id'],
      name: item['name'],
      split_percentage: item['split_percentage'],
      commercial: item['commercial']+" "+(this.showIdentity ? item['identity_name'] : "") ,
      identity_name: item['identity_name'],
      role: item['role'],
      start_date: moment(item['start_date']).utc().format('DD-MMM-YYYY'),
      end_date: moment(item['end_date']).utc().format('DD-MMM-YYYY'),
      status: item['status'],
      expand: 0,
      status_color: item['status_color'],
      oid: item['oid'],
      stakeholder_reference_id: item['stakeholder_reference_id'],
      rate_card_id: item['rate_card_id'],
      external_employee_id: item['external_employee_id'],
      head:item['head'],
      reports_to_warning:item['reports_to_warning']
    });

    uniqueIds.forEach(id => {
      const items = childData.filter(item => item['associate_id'] === id);
      const assignedItem = items.find(item => item['status'] === 'Assigned');
      const allocationCompletedItem = items.find(item => item['status'] === 'Allocation Completed');
      const allocationReservedItem = items.find(item => item['status'] === 'Allocation Reserved');
      
      if (assignedItem) {
        this.parentData.push(createParentData(assignedItem));
      } else if (allocationCompletedItem && allocationReservedItem) {
        this.parentData.push(createParentData(allocationReservedItem));
      } else if (allocationCompletedItem) {
        this.parentData.push(createParentData(allocationCompletedItem));
      } else if (allocationReservedItem) {
        this.parentData.push(createParentData(allocationReservedItem));
      }
    });

    return this.parentData
    // if (!this._isService.parentData || !Array.isArray(this.parentData)) {
    //   return [];
    // }
    //   let filteredData
    //   if(this.noQuote){
    //     filteredData = _.filter(this.parentData, item => {
    //       return _.some(this.noQuoteList, noQuoteItem => noQuoteItem.rate_card_id === item.rate_card_id);
    //   });
    //   return filteredData;
    //   }
    //   else{
    //     filteredData = _.filter(this.parentData, item => {
    //       return (item.rate_card_id === id)
    //     })
    //     return filteredData;
    //   }
  }

  getUniqueIds(data: any[]): number[] {
    const uniqueIds = new Set<number>();
    data.forEach((item) => {
      uniqueIds.add(item.associate_id);
    });
    return Array.from(uniqueIds);
  }

  getChildData(id){
    if (!this._isService.childData || !Array.isArray(this._isService.childData)) {
      return [];
    }
    let filteredData 
    if(this.noQuote){
      filteredData = _.filter(this._isService.childData, item => {
        return _.some(this.noQuoteList, noQuoteItem => noQuoteItem.rate_card_id === item.rate_card_id);
    });
      return filteredData;
    }
    else{
      filteredData = _.filter(this._isService.childData, item => {
        return (item.rate_card_id === id)
      })
      return filteredData;
    }
  }

  editMember(items,type){
    if(type === 'E' && this.inactivePositionStatusForEdit.length > 0 && _.includes(this.inactivePositionStatusForEdit,this.data.position_status)){
      this.toasterService.showWarning(this.inactivePositionEditWarningMsg, 10000);
      return;
    }
    this.triggerPositionEvent.emit({items,type})
  }

  addMember(){
    // if(this.data.remaining && this.data.remaining > 0){
    //   this.triggerPositionEvent.emit({item:this.data.position_id,type:'C'})
    // }
    // else{
    //   this.toasterService.showWarning('No More Allocation is Allowed !', 10000);
    // }
    if(this.inactivePositionStatusForAllocate.length > 0 && _.includes(this.inactivePositionStatusForAllocate,this.data.position_status)){
      this.toasterService.showWarning(this.inactivePositionAddWarningMsg, 10000);
      return;
    }
    this.triggerPositionEvent.emit({item:this.data.position_id,type:'C'})
  } 

  collapse(associate_id: any) {
    for (let items of this.parentData) {
      if (items['associate_id'] == associate_id) {
        items['expand'] = 0;
      }
    }
  }
  expand(associate_id: any) {
    for (let items of this.parentData) {
      if (items['associate_id'] == associate_id) {
        items['expand'] = 1;
      }
    }
  }

  async collapseAll() {
    this.allExpand = false;
    for (let items of this.parentData) {
        items['expand'] = 0;
    }
    await this.saveQTCConfig()
  }
  async expandAll() {
    this.allExpand = true;
    for (let items of this.parentData) {
        items['expand'] = 1;
    }
    await this.saveQTCConfig()
  }

  async getAvailable(quantity){
    if (isNaN(quantity) || quantity === null || quantity === undefined) {
        return 0;
    }

    let available = quantity - await this._isService.getPositionAvailableHours(this.position.position_id);

    if (available < 0) {
        available = 0;
    }

    return available;
  }

  selectChildItem(data): void {
    if (!data || !this._isService.demobilizedData) {
        return;
    }

    const index = this._isService.demobilizedData.findIndex(item =>
        item &&
        item['associate_id'] === data['associate_id'] &&
        item['start_date'] === data['start_date'] &&
        item['end_date'] === data['end_date']
    );

    if (index !== -1) {
        this._isService.demobilizedData.splice(index, 1); 
    } else {
        this._isService.demobilizedData.push(data);
    }
  }

    /**
   * @description saving the User QTC Config
   */
    saveQTCConfig(){
      let config = {
        "type":"position",
        "expand":this.data.expand,
        "id":this.data.position_id,
        "expandAll":this.allExpand,
        "project_id":this.projectId,
        "item_id":this.itemId,
        "date": moment()
      }
      return new Promise((resolve,reject) => {
        this._isService.saveMemberQTCConfig(config).pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if(res['messType'] == 'S') {
              resolve(true)
            }
            else{
              resolve(false)
            }
          },
          error:(err) => {
            resolve(false);
          }
        });
      });
    }

    async getSubscribedData() {
      this.subs.sink = this._isService.parentData$.subscribe(async (value) => {
        await this.getParentData()        
      });
    }

    /**
   * @description Getting the Max Width
   * @param fieldName 
   * @param type 
   * @returns 
   */
  getMaxWidth(fieldName: string, type: string): string {
    const item = this.formConfig.find(d => d.field_name === fieldName && d.type === type);
    if (item && item.max_width) {
      return `max-width: ${item.max_width}%`;
    }
    return ''; // No max-width applied if no data
  }

  /**
   * @description For Fetching Remaining Hours Log
   */
  getRemainingHoursLog(start,limit){
    let quotePositionId = this.data?.position_id;
    return new Promise(async (resolve, reject) => {
      this.subs.sink = (await this.PmInternalStakeholderService
        .getPositionRemainingLogData(this.itemId, quotePositionId,start,limit))
        .subscribe(
          (res: any) => {
            if (res['messType'] == 'S') 
              resolve(res.data);
            else 
            resolve([]);
          },
          (err) => {
            console.log(err);
            this.toasterService.showError('Error in Fetching Remaining Hours Log')
            resolve([]);
          }
        );
    });
  }

  async getRemainingLog(){
    this.isRemainingDataLoading = true;
    this.start = 0;
    let limit = 15;
    this.popUpLogData = await this.getRemainingHoursLog(this.start,limit)
    this.isRemainingDataLoading = false;
  }

  async getPlannedLog(isa_id,allocated_hours){
    this.isPlannedDataLoading = true;
    this.planned_allocated_hours = allocated_hours ? allocated_hours : 0;
    this.popUpPlannedLogData = await this.getPlannedHoursLog(isa_id)
    this.isPlannedDataLoading = false;
  }

  async onAPIDataScroll(){
    // this.isRemainingDataLoading = true;
    let limit = 15;
    this.start += limit;
    let logData = await this.getRemainingHoursLog(this.start,limit);
    this.popUpLogData = this.popUpLogData.concat(logData);
    // this.isRemainingDataLoading = false;
  }

  getActionItem(actionName: string,type:string): string | '' {
    let action = this.actionList.find(item => item.action_name === actionName);
    if(type == 'name'){
      return action ? action.action_label : '';
    }
    else if(type == 'svg'){
      return action ? action.svg : '';
    }
    else 
      return '';
  }

  /**
   * @description For Fetching Planned Hours Log
   */
  getPlannedHoursLog(isa_id){
    
    return new Promise(async (resolve, reject) => {
      this.subs.sink = (await this.PmInternalStakeholderService
        .getISAPlannedHoursLog(isa_id))
        .subscribe(
          (res: any) => {
            if (res['messType'] == 'S') 
              resolve(res);
            else 
            resolve(null);
          },
          (err) => {
            console.log(err);
            this.toasterService.showError('Error in Fetching Planned Hours Information')
            resolve(null);
          }
        );
    });
  }

  toggleOverlay(trigger: any, data: any) {
    this.triggerOrigin = trigger;
    this.logPopUp = !this.logPopUp;
  }

    ngOnDestroy(): void {
      if(this.subs){
        this.subs.unsubscribe();
      }
    }
}
