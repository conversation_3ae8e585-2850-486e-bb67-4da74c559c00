import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router} from '@angular/router';
import { Observable } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { RmService } from '../services/rm.service';
import { LoginService } from "src/app/services/login/login.service"

@Injectable({
  providedIn: 'root'
})
export class RouteGuard implements CanActivate {

  constructor(
    private loginService: LoginService,
    private toaster: ToasterService,
    private router:Router,
    private rmService: RmService
  ) {}

  async canActivate(route: ActivatedRouteSnapshot){

        let hasAccess = <boolean>await this.canAccess()

        if(hasAccess == false)
        {
          console.log("People Allocation Access")
          this.toaster.showError("You not having access to People Allocation Application!","Access Restricted",10000)
          this.router.navigateByUrl("/main");
        };
  
     
        return hasAccess
  }


  canAccess(){
    return new Promise(async(resolve, reject)=>{
      await this.rmService.checkPeopleAllocationAccess().then(async(res: any)=>{
        resolve(res);
      },(err)=>{
        reject(false)
      });
    })
   
  }

}
