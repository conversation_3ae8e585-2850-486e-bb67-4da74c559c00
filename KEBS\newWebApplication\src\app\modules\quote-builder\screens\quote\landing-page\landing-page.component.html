<div class="quote-header">
    <div class="total-count">
        <p>{{(_landingPageService.quotesList | async)?.length || 0}}</p>
        <p>Total Quotations</p>
    </div>
    <div class="toolbar">
        <div class="search-container">
            <mat-icon style="font-size: 20px;">search</mat-icon>
            <input [formControl]="searchFormControl" placeholder="Search" class="input-search">
        </div>
        <button mat-icon-button (click)="openApproverDialog(ifUserHasApprovals)" class="shine-icon-container d-flex align-items-center" *ngIf="ifUserHasApprovals">
            <mat-icon fontSet="material-symbols-outlined" [matTooltip]="ifUserHasApprovals ? 'You have Quote Activation Approvals' : ''" [ngClass]="{'shine-icon': ifUserHasApprovals}" fontSet="material-symbols-outlined">
                gavel
            </mat-icon>
        </button>
        <div (click)="openHelpDialog()" class="d-flex" style="color: #515965;cursor: pointer;" matTooltip="Help">
            <mat-icon class="d-flex align-items-center" fontSet="material-symbols-outlined">info</mat-icon> 
            <span style="font-size: 13px;" class="d-flex align-item-center pl-1"> Help </span>
        </div>
        <!-- <div class="d-flex align-items-center" style="color: #515965;cursor: pointer;padding-top: 7px;">
            <div class="shine-icon-container d-flex align-items-center">
                <mat-icon fontSet="material-symbols-outlined" [matTooltip]="ifUserHasApprovals ? 'You have Quote Activation Approvals' : ''" [ngClass]="{'shine-icon': ifUserHasApprovals}" fontSet="material-symbols-outlined">info</mat-icon> 
            </div>
            <span (click)="openHelpDialog()" style="font-size: 13px; margin-top: -10%;" class="pl-2" matTooltip="Help"> Help </span>
        </div> -->
        <!-- <mat-icon style="color: #45546E;" matTooltip="Compare Quotes">sync_alt</mat-icon> -->
        <button class="d-flex align-items-center justify-content-center" [matTooltip]="showDeleted ? 'Active Quotes' : 'Show Deleted Quotes'" (click)="showDeletedQuotes()" mat-icon-button>
            <mat-icon [ngClass]="showDeleted ? 'del-icon': 'no-del-icon'">delete</mat-icon>
        </button>
        <div *ngIf="isCUDEnabled" class="create-quote">

            <button mat-button (click)="openCreateDialog(createNewQuoteDialog, 'TRADITIONAL')">
                Create New Quote
            </button>
            <button mat-button class="dropdown"
                [matMenuTriggerFor]="createMoreOptions"><mat-icon>keyboard_arrow_down</mat-icon></button>
        </div>
        <div *ngIf="enableChangeRequest" class="create-action">
            <button mat-button class="create-quote" (click)="openCreateDialog(createNewQuoteDialog, 'CHANGE_REQUEST')">
                Change Request
            </button>
        </div>
    </div>
</div>

<ng-container *ngIf='(_landingPageService.quotesList | async | sortQuotes) as quotes;'>
    <ng-container *ngIf="!_landingPageService.isQuoteLoading">
        <ng-container *ngIf="quotes.length;else createNewTemplate">
            <div class="row p-2">
                <quote-card class="col-3 p-2" *ngFor="let quote of quotes" [quote]="quote" 
                    [isCUDEnabled]="quote.quote_type === 'CHANGE_REQUEST' ? enableChangeRequest : isCUDEnabled"
                    [isCRApplicable]="enableChangeRequest"
                    (delete)="deleteQuote(quote)" 
                    (edit)="editQuote(quote)" 
                    (copy)="copyQuote(quote)" 
                    (copyAsCR)="copyQuote(quote, false, 'CHANGE_REQUEST')" 
                    [showDeleted]="showDeleted"
                    [showApprovalButtons]="ifUserHasApprovals"
                    (flagged)="updateActiveQuote(quote)"
                    (changeRequestFlagged)="updateActiveChangeRequestQuote(quote)"
                    (activityLog)="openActivityLog(quote)"
                    (openApproverPopUp)="openApproverDialog(quote)">
                </quote-card>
            </div>
        </ng-container>
    </ng-container>
</ng-container>
<ng-template #createNewTemplate>
    <div *ngIf="!isQuoteBeingImported" class="body-card">
        <img src="https://assets.kebs.app/images/no_data_found.png" height="200" width="270" class="mt-5 mb-3">
        <div class="text-wrapper">
            <p class="text-main">No {{ showDeleted ? 'Deleted' : '' }} Quotes Here</p>
            <p *ngIf="!showDeleted && isCUDEnabled" class="text-content">Start creating quote now.</p>
        </div>
        <div *ngIf="!showDeleted && isCUDEnabled" class="create-action">
            <button mat-button class="create-quote" (click)="openCreateDialog(createNewQuoteDialog, 'TRADITIONAL')">
                Create New Quote
            </button>
            <button mat-stroked-button (click)="openImportQuoteDialog()">
                Import A Quote
            </button>
        </div>
    </div>
</ng-template>

<ng-container *ngIf="_landingPageService.isQuoteLoading">
    <div class="body-card">
        <span>Loading....</span>
    </div>
</ng-container>


<ng-template #createNewQuoteDialog>
    <div class="p-3 create-quote-dialog">
        <p class="dialog-title">New Quote!</p>
        <label>Quote Name</label><br>
        <mat-form-field appearance="outline">
            <input matInput placeholder="Quote Name" required="true" [(ngModel)]="initialQuote.quoteName">
        </mat-form-field>
        <br><label>Currency</label><br>
        <app-input-search [list]="masterDataService.currency | async" placeholder="Currency" required="true"
            [hideMatLabel]="true" [disabled]="true" [(ngModel)]="initialQuote.currency"></app-input-search>
        <mat-dialog-actions align="start">
            <button mat-stroked-button mat-dialog-close>Cancel</button>
            <button mat-button (click)="routeToCreatePage()" class="create-quote">Create Quote</button>
        </mat-dialog-actions>
    </div>
</ng-template>

<ng-template #copyQuoteDialog let-data>
    <div class="p-3 create-quote-dialog">
        <p class="dialog-title" *ngIf="data.quoteType != 'CHANGE_REQUEST'">{{ data.isFromImport ? 'Import' : 'Copy' }} Quote!</p>
        <p class="dialog-title" *ngIf="data.quoteType == 'CHANGE_REQUEST'">Copy as a Change Request Quote!</p>
        <label>Quote Name</label><br>
        <mat-form-field appearance="outline">
            <input matInput placeholder="Quote Name" required="true" [(ngModel)]="initialQuote.quoteName">
        </mat-form-field>
        <br><label>Currency</label><br>
        <app-input-search [list]="masterDataService.currency | async" placeholder="Currency" required="true"
            [hideMatLabel]="true" [disabled]="true" [(ngModel)]="initialQuote.currency"></app-input-search>
        <div *ngIf="data.showImportOption" class="p-1">
            <mat-radio-group [formControl]="importFormControl" class="radio-btn-class">
                <mat-radio-button [ngClass]="importFormControl.value == 1 ? 'radio-label-color' : ''" value="1">Copy all Data</mat-radio-button>
                <mat-radio-button [ngClass]="importFormControl.value == 2 ? 'radio-label-color' : ''" class="pl-3" value="2">Copy Referencing Rate Card</mat-radio-button>
            </mat-radio-group>
        </div>
        <mat-dialog-actions align="start">
            <button mat-stroked-button mat-dialog-close>Cancel</button>
            <button mat-button (click)="copyData()" class="create-quote">{{ data.isFromImport ? 'Import' : 'Copy' }} Quote</button>
        </mat-dialog-actions>
    </div>
</ng-template>

<ng-template #importQuoteDialog>
    <div class="p-3 create-quote-dialog">
        <p class="dialog-title">Import Quote From Opportunities</p>

        <div class="search-bar">
            <mat-form-field appearance="outline" class="w-100">
                <mat-label>Search Opportunities or Quotes</mat-label>
                <input matInput [(ngModel)]="searchQuery" (ngModelChange)="onSearch()" [formControl]="importSearchFormControl" placeholder="Search by Opportunity Name, Opportunity ID, Quote Name or Quote ID ">
                    <button mat-button *ngIf="searchQuery" matSuffix mat-icon-button aria-label="Clear" (click)="clearSearch()">
                        <mat-icon [matTooltip]="'Clear Search'" style="color:#a3a7ad;font-size: 19px">close</mat-icon>
                    </button>
            </mat-form-field>
        </div>

        <div *ngIf="oppQuoteList.length == 0" class="d-flex justify-content-center align-items-center"
            style="flex-direction: column;">
            <img src="https://assets.kebs.app/images/no_data_found.png" height="180" width="250" class="mt-5 mb-3">
            <div style="color: #45546e;font-weight: 700;">No Quotes Found to Import !</div>
        </div>

        <div *ngIf="searchingFlag && !limitLoader" class="d-flex justify-content-center align-items-center"
            style="flex-direction: column;">
            <mat-spinner diameter="25"></mat-spinner>
            <div style="color: #45546e;font-weight: 600;font-size: 13px;">Searching...</div>
        </div>

        <ng-container *ngIf="oppQuoteList.length">
            <div class="pt-2 pb-2" style="max-height: 400px; overflow-y: auto;" infinite-scroll [infiniteScrollDistance]="3" [infiniteScrollThrottle]="100" (scrolled)="onScroll()" [scrollWindow]="false" >
                <div *ngFor="let oppItem of oppQuoteList">
                    <mat-accordion>
                        <mat-expansion-panel hideToggle [expanded]="true">
                            <mat-expansion-panel-header>
                                <mat-panel-title>
                                    <div class="row quote-title" style="color: #ef4a61;">
                                      {{oppItem?.opportunity_code ? oppItem.opportunity_code : "OPP/" + oppItem.opportunity_id }} - {{ oppItem.opportunity_name }}  
                                    </div>
                                </mat-panel-title>
                            </mat-expansion-panel-header>
                            <ng-container *ngFor="let quoteItem of oppItem['quote_list']">
                                <div class="row pt-2 pl-2 quote-title">
                                    <span style="cursor: pointer;" (click)="importQuote(oppItem, quoteItem)">
                                        QT/{{ quoteItem.quote_header_id }} - {{ quoteItem.quote_name }}
                                    </span>
                                </div>
                            </ng-container>
                        </mat-expansion-panel>
                    </mat-accordion>
                </div>
            </div>
            <div class="d-flex justify-content-center align-items-center pt-1" *ngIf="limitLoader">
                <mat-spinner diameter="25"></mat-spinner>
            </div>
        </ng-container>
    </div>
</ng-template>
  

<!-- Submit for approval Dialog -->
<ng-template #submitForApproval let-data>
    <div class="submit-for-approval-dialog" style="min-height: 40vh" *ngIf="!miniLoader">
        <div>
            <div class="row header-submit-pop" mat-dialog-title>
                <div class="heading">
                    <mat-icon> send </mat-icon> &nbsp; Send Quote For Approval
                </div>
                <button class="d-flex justify-content-center align-items-center" mat-icon-button
                    (click)="closeSubmitApprovalPopUp(data, 'SUBMIT_CANCEL')">
                    <mat-icon>close</mat-icon>
                </button>
            </div>
            <div class="qb-details">
                <div class="row">
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-title">Quote Name:</div>
                    </div>
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-value">
                            QT/{{ data?.quoteDetails.quote_header_id }} - {{ data?.quoteDetails.quote_name }}
                        </div>
                    </div>
                </div>
            
                <div class="row">
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-title">Quote Value:</div>
                    </div>
                    <div class="col-5 d-flex align-items-center">
                        <app-currency [currencyList]="data?.quoteDetails?.quote_revenue_value" type="small"
                            [showActualAmount]="true"></app-currency>
                    </div>
                </div>
            </div>
            <div class="approvers-profiles ml-3 mt-3">
                <div class="top-header mb-2">
                    <span>
                        Approvers &nbsp;
                    </span>
                </div>
                <div class="d-flex align-items-center justify-content-start approvers-container">
                    <div *ngIf="reviewersList.length == 0" [matTooltip]="'-'">
                        -
                    </div>
                    <div *ngFor="let approver of reviewersList" class="approver-item">
                        <app-user-image [id]="approver.oid" [matTooltip]="approver.employee_name"
                            imgWidth="33px" imgHeight="33px">
                        </app-user-image>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-center align-items-center mt-3 pt-3">
            <button class="dialog-cancel-btn" mat-button
                (click)="closeSubmitApprovalPopUp(data.quoteDetails, 'SUBMIT_CANCEL')">
                Cancel
            </button>
            <button [disabled]="reviewersList.length == 0"
                [matTooltip]="reviewersList.length == 0 ? 'No Approvers Found!' : ''" mat-raised-button
                class="dialog-submit-btn" mat-button (click)="closeSubmitApprovalPopUp(data?.quoteDetails, 'SUBMIT')">
                Send
            </button>
        </div>
    </div>
    <div class="mini-loader" *ngIf="miniLoader">
        <mat-spinner class="spinner d-flex justify-content-center" diameter="26"></mat-spinner>
    </div>
</ng-template>

<ng-template #approveOrReject let-data>
    <div class="submit-for-approval-dialog" style="min-height: 40vh" *ngIf="!miniLoader">
        <div>
            <div class="row header-submit-pop" mat-dialog-title>
                <div class="heading"> 
                    <mat-icon> send </mat-icon> &nbsp; {{"Quote Approval"}}</div>
                    <button class="d-flex justify-content-center align-items-center" matTooltip="Close" mat-icon-button (click)="closeApproverPopUp(data, 'CANCEL')">
                        <mat-icon>close</mat-icon>
                    </button>
            </div>
                <div class="qb-details">
                    <div class="top-header mb-2" *ngIf="data.quoteDetails.isApprover">
                        <span class="top-header mb-2">
                            Submitted By &nbsp;
                        </span>
                    </div>
                    <div class="d-flex align-items-center mb-3 submitter-details" *ngIf="data.quoteDetails.isApprover">
                        <app-user-image [id]="data.submitterDetails?.oid" imgWidth="28px" imgHeight="28px">
                        </app-user-image>&nbsp;&nbsp;&nbsp;
                        <div>
                            <div style="font-size: 12px;">A.ID {{ data?.submitterDetails?.associate_id || submitterDetails?.associate_id}}</div>
                            <div class="reviwer-name"><app-user-profile type="name" [oid]="data.submitterDetails?.oid">
                        </app-user-profile></div>
                        </div>
                    </div>
                    <div class="approvers-profiles mb-3 mt-3">
                        <div class="top-header mb-2" style="font-weight: 600 !important;">
                            {{data.quoteDetails.isApprover ? 'Approvers for Quote Activation Request' : 'Approvers '}}
                        </div>
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center justify-content-start approvers-container">
                                <div *ngIf="reviewersList.length == 0" [matTooltip]="'-'">
                                    -
                                </div>
                                <!-- <div class="d-flex align-items-center justify-content-between"> -->
                                    <div *ngFor="let approver of reviewersList" class="approver-item">
                                        <app-user-image [id]="approver.oid" [matTooltip]="getApproverTooltip(approver)" imgWidth="33px"
                                            imgHeight="33px">
                                        </app-user-image>
                                    <!-- </div> -->
                                </div>
                                
                            </div>
                            <div class="uploader-details-desc d-flex align-items-center         justify-content-center"
                                [ngStyle]="getStatusCss(data.quoteDetails?.quote_status)">
                                {{ data.quoteDetails.status_name || "-" }}
                                </div>
                        </div>
                        
                    </div>
                    <div class="row">
                        <div class="col-5 d-flex align-items-center">
                            <div class="qb-title">Quote Name:</div>
                        </div>
                        <div class="col-5 d-flex align-items-center">
                            <div class="qb-value">
                                QT/{{ data?.quoteDetails.quote_header_id }} - {{ data?.quoteDetails.quote_name }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-5 d-flex align-items-center">
                            <div class="qb-title">Quote Value:</div>
                        </div>
                        <div class="col-5 d-flex align-items-center">
                            <app-currency [currencyList]="data?.quoteDetails?.quote_revenue_value" type="small" [showActualAmount]="true"></app-currency>
                        </div>
                    </div>
                </div>
                <div class="approvers-profiles ml-3 mt-3">
                    <div class="d-flex align-items-center justify-content-start">
                        <span class="top-header" style="font-weight: 600 !important;"> Comments </span>
                        <mat-icon *ngIf="data?.quoteDetails?.isApprover && this.allow_approval"
                            [matTooltip]="'Comments are required for rejecting document'"> info </mat-icon>
                    </div>
                    <textarea [(ngModel)]="commentToSubmitter" [readonly]=" !data.quoteDetails?.isApprover || data.quoteDetails.quote_status == 3 || data.quoteDetails.quote_status == 2" rows="5" cols="40"></textarea>
                </div>
            
        </div>
        <div class="d-flex justify-content-center align-items-center mt-3 pt-3" *ngIf="data.quoteDetails.isApprover && data.quoteDetails.quote_status === 1">
            <button class="dialog-cancel-btn" mat-button (click)="closeApproverPopUp(data.quoteDetails, 'REJECT')">
                Reject
            </button>
            <button mat-raised-button class="dialog-submit-btn" mat-button (click)="closeApproverPopUp(data?.quoteDetails, 'APPROVE')">
                Approve
            </button>
        </div>
    </div>
    <div class="mini-loader" *ngIf="miniLoader">
        <mat-spinner class="spinner d-flex justify-content-center" diameter="26"></mat-spinner>
    </div>
</ng-template>

<mat-menu #createMoreOptions="matMenu">
    <button (click)="openImportQuoteDialog()" mat-menu-item matTooltip="Import a Existing Quote">Import A Quote</button>
</mat-menu>

<ngx-spinner bdColor="rgba(0, 0, 0, 0)" size="small" color="#cf0001" type="ball-clip-rotate" [fullScreen]="false">
    <p style="color: #cf0001; margin-top: 10vh !important; font-weight: 400">
        {{ loadingContent }}
    </p>
</ngx-spinner>