import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { MatDialogRef, MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import * as _ from 'underscore';
import { pluck, first, takeUntil, debounceTime } from 'rxjs/operators';
import { OpportunityStakeholdersService } from 'src/app/modules/opportunities/features/opportunities-detail/pages/internal-stakeholder/services/opportunity-stakeholders.service';
import { OpportunityService } from 'src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService';
import { RolesService } from 'src/app/services/acl/roles.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { SharedLazyLoadedComponentsService } from '../../../services/shared-lazy-loaded-components.service';
import { DocumentUploadComponent } from '../document-upload/document-upload.component';
import { DocumentViewerComponent } from '../document-viewer/document-viewer.component';
import { DocumentManagerService } from '../../service/document-manager.service';
import { LoginService } from 'src/app/services/login/login.service';
import { FilterServiceService } from 'src/app/modules/project-management/shared-lazy-loaded/components/filters-common-dialog/services/filter-service.service';
import { Subject, Subscription } from 'rxjs';
import { formatDate } from '@angular/common';
import moment from 'moment';


@Component({
  selector: 'app-document-landing-page',
  templateUrl: './document-landing-page.component.html',
  styleUrls: ['./document-landing-page.component.scss']
})
export class DocumentLandingPageComponent implements OnInit {
  protected _onDestroy = new Subject<void>();
  filterSubscription$: Subscription;
  @ViewChild('confirmationDialog', { read: TemplateRef }) confirmationDialog: TemplateRef<any>;
  @ViewChild('rejectDialog', { read: TemplateRef }) rejectDialog: TemplateRef<any>;
  @ViewChild('filterDialog', { read: TemplateRef }) filterDialog: TemplateRef<any>;
  configDialogRef: MatDialogRef<any>;
  rejectRef: MatDialogRef<any>;
  filterDialogRef: MatDialogRef<any>;
  rejectForm: FormGroup;
  fileArray: any = [];
  applicationReferenceId: any;
  urls = {
    uploadUrl: '/api/opportunity/uploadOpportunityAttachment',
    downloadUrl: 'api/opportunity/downloadOpportunityAttachment',
  };
  crmAttachmentConfig: any;
  contextId = '';
  appId: any;
  attachmentCount = 0;
  filesUploaded: any = [];
  isLoading: boolean = true;
  editAccess: boolean = false;
  superDeleteAccess: boolean = false;
  documentType: string = 'GENERAL';
  fileTypeDropDown: any = [];
  showGeneralAttachments = false;
  showFinancialDocuments = false;
  selectedToggle: string = 'general_attachments';
  showAddApproval: boolean = false;
  commentText: string;
  originalFileUpload: any[];
  miniLoader: boolean = false;
  headerColumns: any = [];
  attachmentStatus: any = [];
  attachmentslist: any = [];
  moreOptionsConfig: any = {};
  user_aid: any;
  user_oid: any;
  FDAccess: boolean = false;
  filterConfig: any = {
    search_params: "",
    query_params: []
  }

  dialog_animation = {
    entryAnimation: {
      keyframes: [
        { transform: 'translateX(100%)' },
        { transform: 'translateX(0)' },
      ],
      keyframeAnimationOptions: {
        duration: 250,
        easing: 'ease-in-out',
      },
    },
    exitAnimation: {
      keyframes: [
        { transform: 'translateX(0)' },
        { transform: 'translateX(100%)' },
      ],
      keyframeAnimationOptions: {
        duration: 250,
        easing: 'ease-in-out',
      },
    },
  }
  FD_count: number;
  menuList: any = [];
  filterData: any;
  internal_application_id: any;
  s3_config: any;
  docParams: any;
  searchFormControl = new FormControl("");
  dynamicSearchParameter: any;
  valueChangeSubscription = new Subscription();
  originalFilesData: any = [];
  appliedFilter: any = [];
  isGoogleDoxRestricted: boolean;
  isDeletedFilterActive: boolean = false
  showDeletedButton: boolean = false
  attachmentConfig: any;
  approverEnabled: boolean = true;
  additionalDetails: any;

  constructor(
    private route: ActivatedRoute,
    private opportunityService: OpportunityService,
    private dialog: MatDialog,
    private _sharedService: SharedLazyLoadedComponentsService,
    private roleService: RolesService,
    private toaster: ToasterService,
    private _stakeholderService: OpportunityStakeholdersService,
    private fb: FormBuilder,
    private router: Router,
    private documentService: DocumentManagerService,
    private _loginService: LoginService,
    private _filterService: FilterServiceService
    
  ) {
    this.rejectForm = this.fb.group({
      commentText: ['', Validators.required]
    });
  }

  async ngOnInit() {

    const generalConfig = await this._stakeholderService.getTheme();
       document.documentElement.style.setProperty('--intButton', generalConfig['tabColor']);


    this.documentService.applicationIdSubject$.subscribe(id => {
      this.appId = id;
      this.contextId = id;
    });

    this.documentService.applicationReferenceId$.subscribe(id => {
      this.applicationReferenceId = id;
      this.contextId = this.contextId + this.applicationReferenceId;
    });

    this.documentService.isGoogleViewerBlocked().subscribe((blocked) => {
      console.log('Google Viewer Blocked: ', blocked);
      this.isGoogleDoxRestricted = blocked
    });

    this.documentService.docManagerParams$.subscribe(val => {
      console.log(val, 'Doc Params')
      this.docParams = val
      this.internal_application_id = val.internal_application_id || 'opportunity_document_manager'
      this.s3_config = val.s3_config
      this.additionalDetails = val?.additionalDetails || {}
      this.contextId = (typeof this.contextId === 'string' && this.contextId.length < 3)
        ? this.contextId + (this.additionalDetails?.opportunityId || '')
        : this.contextId;
    });


    this.editAccess = await this.checkEditAcess()

    this.superDeleteAccess = await this.checkDeleteAccess()

    this.user_aid = this._loginService.getProfile().profile.aid,
      this.user_oid = this._loginService.getProfile().profile.oid,

      console.log('Logged In User', this.user_aid, this.user_oid);

    await this.fetchAttachmentStatus();
    await this.opportunityService.getCRMAttachmentsConfigs().then((res) => {
      console.log(res);
      this.attachmentConfig = res
      let data = res['data']['aws']['attachment_plugin_config'];
      this.crmAttachmentConfig = data;
      this.headerColumns = res['data']['document_manager_config'];
      this.moreOptionsConfig = res['data']['more_info_config'];
      this.menuList = this.moreOptionsConfig.menuItems.filter(m => m.is_active) || [];
      this.showDeletedButton = res['data']['show_deleted_docs_option'] && _.where(this.roleService.roles, { application_id: Number(this.appId), object_id: 29418, operation: "*" }).length != 0;
      this.approverEnabled = res['data']['approvers_enabled'] || false;
    });


    await this.opportunityService.getOpportunityDocumentTypes()
      .then((res) => {
        console.log(res);
        if (res && res['data']) {
          this.fileTypeDropDown = res['data'];
        } else {
          console.warn("No document types received.");
          this.fileTypeDropDown = [];
        }
      })
      .catch((error) => {
        console.error("Failed to retrieve document types:", error);
      });
    this.filterData = {
      "document_type": this.fileTypeDropDown || [],
      "document_status": this.attachmentStatus || []
    }
    await this.retrieveUploadedObjects();



    this.filterSubscription$ = await this._filterService.getFilterConfig(Number(this.appId), 'opportunity_document_manager').subscribe(filterList => {
      this.retrieveUploadedObjects();
    });

    this.valueChangeSubscription = this.searchFormControl.valueChanges.pipe(debounceTime(500), takeUntil(this._onDestroy)).subscribe(searchParameter => {

      //Original 
      // if (searchParameter) {

      //   const reg = new RegExp(searchParameter, "i");
  
      //   this.filesUploaded = this.originalFilesData.filter(val => reg.test(val['document_name']) || reg.test(val['updated_on']));

      // }

      if (searchParameter) {
        this.dynamicSearchParameter = searchParameter;
        const escapedSearchParameter = searchParameter.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
        const reg = new RegExp(escapedSearchParameter, "i");
  
        this.filesUploaded = this.originalFilesData.filter(val => {
          const updatedOnFormatted = formatDate(val['updated_on'], 'dd-MMM-yy', 'en-GB');
          const validFormats = ['DD MMM YYYY', 'DD-MMM-YYYY', 'DD/MM/YYYY', 'dd-MMM-yy', 'dd MMM yy'];
          let matched = false;

          let fileType = this.fileTypeDropDown.find(type => type.document_type === val?.document_type);
          const status = this.attachmentStatus.find(status => status.id === val?.document_status);
  
          validFormats.forEach(format => {
            if (moment(val['updated_on'], format, true).isValid()) {
              const formattedDate = moment(val['updated_on'], format).format('DD/MM/YYYY');
              if (formattedDate.includes(searchParameter)) {
                matched = true;
              }
            }
          });
  
          return reg.test(val['file_name']) || 
                reg.test(val['document_id']) || 
                // reg.test(updatedOnFormatted) ||
                reg.test(fileType?.document_label) ||
                reg.test(val['uploaded_by_name']) ||
                reg.test(status?.status_name) ||
                matched;
        });
      } 

      else
        this.filesUploaded = this.originalFilesData;

    });
  }

  async fetchAttachmentStatus() {
    try {
      const res: any = await this.documentService.attachmentStatus().pipe(first()).toPromise();
      // console.log('Status:', res);
      if (res && res.data) {
        this.attachmentStatus = res.data;
      }
    } catch (error) {
      console.error('Error fetching approvers:', error);
    }
  }

  toggleDeletedFilter() {
    this.isDeletedFilterActive = !this.isDeletedFilterActive;
    this.retrieveUploadedObjects(); 
    this.searchFormControl.reset();
  }

  async retrieveUploadedObjects() {
    this.isLoading = true;
    await this.applyFilter()
    this.filesUploaded = [];
    this.documentService
      .retrievecrmUploadedObjects(
        this.s3_config['destination_bucket'] || this.crmAttachmentConfig['destination_bucket'],
        this.contextId, this.applicationReferenceId, this.appId, this.filterConfig, this.isDeletedFilterActive
      )
      .pipe(pluck('data'))
      .subscribe(
        (res: any) => {
          this.attachmentCount = res['length'];
          this.filesUploaded = res;
          this.filesUploaded = this.filesUploaded
            .filter(file => 'document_id' in file && 'document_status' in file && 'document_type' in file)
            .map(file => {
              const approvalStatus = this.attachmentStatus.find(status => status.id === file.document_status);
              const fileType = this.fileTypeDropDown.find(type => type.document_type === file.document_type);

              return {
                ...file,
                document_approval_status: approvalStatus ? approvalStatus.status_name : '-',
                document_label: fileType ? fileType.document_label : '-'
              };
            });

          this.FD_count = this.filesUploaded.length
          this.originalFilesData = this.filesUploaded;



          // this.filesUploaded = this.filesUploaded.map(file => {
          //   const status = this.attachmentStatus.find(status => status.id === file.document_status);
          //   return {
          //     ...file,
          //     document_approval_status: status ? status.status_name : '-'
          //   };
          // });
          // this.filesUploaded = this.filesUploaded.map(file => {
          //   const status = this.fileTypeDropDown.find(status => status.document_type === file.document_type);
          //   return {
          //     ...file,
          //     document_label: status ? status.document_label : '-'
          //   };
          // });
          // this.filesUploaded = this.filesUploaded.filter(file =>
          //   'document_id' in file && 'document_status' in file && 'document_type' in file
          // );

          this.originalFileUpload = [...this.filesUploaded]

          for (let h of this.headerColumns) {
            if (h.sortOrder && (h.sortOrder == 'A' || h.sortOrder == 'D')) {
              h.sortOrder = 'N';
            }
          }
          this.isLoading = false;
        },
        (err) => {
          console.error(err);
          this.isLoading = false;
        }
      );
  };

  viewFile = (file, type?) => {
    let cdn_link = file.cdn_link;

    if (type == 'INLINE' && file.file_format !== 'pdf') 
      this.downloadFile(file)

    this._sharedService.getcrmDownloadUrl(cdn_link, this.applicationReferenceId, this.appId).subscribe((res: any) => {
      if (this.attachmentConfig?.data?.viewer_option?.openInNewTabMozillaViewer && file.file_format == 'pdf') {
        this.toaster.showInfo('Opening in New tab!', '', 2500)
        window.open(res.mozUrl, '_blank')
        return
      }

      else if (this.attachmentConfig?.data?.viewer_option?.showBase64 && file.file_format == 'pdf') {
        this.toaster.showInfo('Opening in New tab!', '', 2500);
      
        if (res.base64Url.startsWith('JVBER')) {
          const base64Data = res.base64Url;
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
      
          const byteArray = new Uint8Array(byteNumbers);
          const pdfFile = new Blob([byteArray], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(pdfFile);
          window.open(fileURL);
        } else {
          window.open(res.base64Url, '_blank');
        }
      
        return;
      }
      
      else if (this.attachmentConfig?.data?.viewer_option?.showBase64iFrame && file.file_format == 'pdf') {
        this.toaster.showInfo('Opening in New tab!', '', 2500);
      
        let base64Data = res.base64Url;
      
        let pdfWindow = window.open('');
        pdfWindow.document.write(`
          <iframe width="100%" height="100%" style="border:none;" src="data:application/pdf;base64,${base64Data}"></iframe>
        `);
      
        return;
      }

      else if (this.attachmentConfig?.data?.viewer_option?.justIFrame && file.file_format == 'pdf') {
        this.toaster.showInfo('Opening in New tab!', '', 2500);
      
        let pdfWindow = window.open('');
        pdfWindow.document.write(`
          <iframe width="100%" height="100%" style="border:none;" src="${res.data}"></iframe>
        `);
      
        return;
      }      

      else if (this.attachmentConfig?.data?.viewer_option?.openInNewTabGoogleViewer && file.file_format == 'pdf') {
        this.toaster.showInfo('Opening in New tab!', '', 2500)
        window.open(res.googleOpenInNewTab, '_blank')
        return
      }

      else if (this.attachmentConfig?.data?.viewer_option?.openInNewTabMozillaViewer && this.documentService.isImageFormat(file.file_format) || this.attachmentConfig?.data?.viewer_option?.showInGoogleViewer || this.attachmentConfig?.data?.viewer_option?.openInNewTabGoogleViewer || file.file_format != 'pdf')
        this.dialog.open(DocumentViewerComponent, {
          width: '75%',
          height: '95%',
          data: {
            selectedFileUrl: res.data,
            fileFormat: file.file_format,
          },
        });

      else
        this.downloadFile(file);
        // this.toaster.showWarning('Cannot show preview for the uploaded file type', '', 2000);

    });
  };

  openToaster() {
    if (!this.editAccess) {
      return;
    }
    this.toaster.showSuccess('File deleted Successfully', '', this.documentService.mediumInterval);
  }

  deleteFile = (file) => {
    if (!this.editAccess)
      return this.toaster.showWarning("Delete is restricted", '', this.documentService.mediumInterval);

    if (this.additionalDetails && !this.additionalDetails?.atRisk && !this.superDeleteAccess)
      return this.toaster.showWarning("Cannot Delete Document", "Opportunity is in secured state", this.documentService.mediumInterval)

    // if (file.isApprover)
    //   return this.toaster.showWarning("Approvers cannot delete file!", '', this.documentService.mediumInterval);

    if (file.document_status === 2)
      return this.toaster.showWarning("Cannot delete document under review", '', this.documentService.longInterval);

    let index = file.index;
    this._sharedService.deletecrmObj(file, 't_app_attachments_meta', this.applicationReferenceId, this.appId).subscribe(
      (res: any) => {
        this.filesUploaded.splice(0);
        this.openToaster();
        this.retrieveUploadedObjects();
      },
      (err) => {
        console.error(err);
      }
    );
  };

  async openPopUp(existingDocument?) {
    console.log(this.crmAttachmentConfig);

    let data = {
      destinationBucket: this.s3_config['destination_bucket'] || this.crmAttachmentConfig['destination_bucket'],
      routingKey: this.s3_config['routing_key'] || this.crmAttachmentConfig['routing_key'],
      contextId: this.contextId,
      allowEdit: true,
      allowDeleteRename: this.editAccess,
      myFilesDefaultFolder: [],
      documentType: this.documentType,
      fileTypeDropDown: this.fileTypeDropDown,
      mode: 'create',
      color: "#79BA44",
      applicationReferenceId: this.applicationReferenceId,
      appId: this.appId,
      existingDocument: existingDocument ? existingDocument[0] : null,
      viewerOption: this.attachmentConfig?.data?.viewer_option || null,
      approversEnabled: this.attachmentConfig?.data?.approvers_enabled || false
    };

    let dialogConfig = {
      // width: '70%',
      minHeight: '50vh',
      data: { data },
      disableClose: true,
      panelClass: 'custom-mat-dialog-panel',
      // animation: this.dialog_animation 
    };

    const uploadBtnDialogRef = this.dialog.open(DocumentUploadComponent, dialogConfig);

    try {
      const res = await uploadBtnDialogRef.afterClosed().toPromise();

      if (res?.refreshLanding) {
        this.attachmentCount = res.fileCount;
        this.filesUploaded.splice(0);
        await this.addAttachmentType(res);
      }
    } catch (error) {
      console.error('Error handling dialog result:', error);
    }
  }

  async addAttachmentType(res) {
    let documentInfo = res?.filesUploaded.map(file => ({
      _id: file._id,
      document_type_id: file.document_type_id,
      document_type: file.document_type,
      reviewersList: res.reviewersList,
      isApprovalRequired: res?.isApprovalRequired || false,
      applicationReferenceId: this.applicationReferenceId,
      appId: this.appId,
      documentId: file?.document_id
    }));

    console.log(documentInfo)

    this.opportunityService.updateDocumentTypes(documentInfo).subscribe(
      response => {
        this.retrieveUploadedObjects();

        console.log('API Response:', response);
      },
      error => {
        console.error('API Error:', error);
      }
    );
  }

  async checkEditAcess() {
    let adminAccess = _.where(this.roleService.roles, { application_id: Number(this.appId), role_id: 1 });
    let editAccess = _.where(this.roleService.roles, { application_id: Number(this.appId), object_id: 6, operation: "*" });
    console.log("accessList", editAccess)
    if (editAccess.length > 0) {
      return true;
    }
    else {
      return false;
    }
  }

  async checkDeleteAccess() {
    let deleteAccess = _.where(this.roleService.roles, { application_id: Number(this.appId), object_id: 29420, operation: "*" });
    if (deleteAccess.length > 0) return true;
    else return false;
  }

  async handleAction(action: string, document: any) {
    switch (action) {
      case 'download':
        this.downloadFile(document)
        break;

      case 'view':
        this.viewFile(document);
        break;

      case 'delete':
        this.deleteFile(document);
        break;

      default:
        console.warn(`Unknown action: ${action}`);
        break;
    }
  }

  getStatusCss(status: number): any {
    switch (status) {
      case 1:
        return { 'background': '#E8E9EE', 'color': '#45546E' };
      case 2:
        return { 'background': '#FFF3E8', 'color': '#FA8C16' };
      case 3:
        return { 'background': '#EEF9E8', 'color': '#52C41A' };
      case 4:
        return { 'background': '#FFEBEC', 'color': '#FF3A46' };
      default:
        return {};
    }
  }

  sortThisThing(header) {
    // console.log('Header:', header);

    if (!header.sortOrder) {
      header.sortOrder = 'A';
    } else {
      header.sortOrder = header.sortOrder === 'A' ? 'D' : header.sortOrder === 'D' ? 'N' : 'A';
    }
    // console.log('Sort Order:', header.sortOrder);

    if (header.sortOrder === 'A') {
      this.filesUploaded = this.filesUploaded.sort((a, b) => {
        const aKeyName = this.getKeyName(a, header.key_name);
        const bKeyName = this.getKeyName(b, header.key_name);
        return this.compare(aKeyName, bKeyName);
      });
    } else if (header.sortOrder === 'D') {
      this.filesUploaded = this.filesUploaded.sort((a, b) => {
        const aKeyName = this.getKeyName(a, header.key_name);
        const bKeyName = this.getKeyName(b, header.key_name);
        return this.compare(bKeyName, aKeyName);
      });
    } else if (header.sortOrder === 'N') {
      this.filesUploaded = this.originalFileUpload
    } else {
      this.filesUploaded.sort((a, b) => {
        const aDueDate = new Date(a.task_due_date).getTime();
        const bDueDate = new Date(b.task_due_date).getTime();
        return bDueDate - aDueDate;
      });
    }
    // console.log('Sorted Data:', this.filesUploaded);
  }

  getKeyName(item, keyName) {
    const value = item[keyName];
    if (typeof value === 'number') {
      return value;
    } else if (typeof value === 'string') {
      return value.toLowerCase();
    } else {
      return (value || item.opportunity_name || item.contact_name || '');
    }
  }

  compare(a: any, b: any) {
    if (a < b) return -1;
    if (a > b) return 1;
    return 0;
  }

  getSortTooltip(sortOrder: string): string {
    switch (sortOrder) {
      case 'A':
        return 'Sort Descending';
      case 'D':
        return 'Remove Sort';
      case 'N':
        return 'Sort Ascending';
      default:
        return 'Sort';
    }
  }

  openDetailedApprovalPage(document) {
    // document.showView = this.attachmentConfig?.data?.viewer_option?.openInNewTabMozillaViewer && !this.attachmentConfig?.data?.viewer_option?.openInNewTabGoogleViewer && !this.attachmentConfig?.data?.viewer_option?.showInGoogleViewer && document.file_format == "pdf" ;
    document.showInGoogleViewer = this.attachmentConfig?.data?.viewer_option?.showInGoogleViewer;
    document.openInNewTabGoogleViewer = this.attachmentConfig?.data?.viewer_option?.openInNewTabGoogleViewer;
    document.openInNewTabMozillaViewer = this.attachmentConfig?.data?.viewer_option?.openInNewTabMozillaViewer;
    document.columnConfig = this.headerColumns;
    this.documentService.setDocument(document);
    this.router.navigate(['../approver-page'], { relativeTo: this.route });
  }

  closeDialog(type: string): void {
    switch (type) {
      case 'REJECT_CANCEL':
        this.rejectRef.close('REJECT_CANCEL');
        break;

      case 'APPROVE':
        this.configDialogRef.close();
        break;

      case 'REJECT':
        if (this.rejectForm.valid) {
          console.log('Comment:', this.rejectForm.get('commentText')?.value);
          this.rejectRef.close('CAN_REJECT');
        } else {
          this.toaster.showWarning('Comments required to reject!', '', this.documentService.mediumInterval);
          this.rejectForm.markAllAsTouched();
        }
        break;

      case 'APPLY_FILTER':
        this.filterDialogRef.close();
        break;

      default:
        break;
    }
  }

  approve(document: any) {
    document.document_triggered_disable = true;

    let details = {
      workflowId: document.workflow_header_id,
      approval_status: 'A',
      document_id: document.document_id,
      app_reference_id: this.applicationReferenceId,
      app_id: this.appId,
    };

    this.documentService.approveOrReject(details).subscribe(
      (res: any) => {
        console.log('Approvers:', res);
        if (res) {
          this.miniLoader = false;
          document.document_triggered = true;
          this.configDialogRef = this.dialog.open(this.confirmationDialog, {
            width: '25rem',
            minHeight: '25vh',
            disableClose: true,
          });

          this.configDialogRef.afterClosed().subscribe(result => {
            document.document_status = 3;
            document.document_approval_status = 'Approved';
            document.document_triggered = true;
          });
          this.toaster.showSuccess('Document Approved!', '', this.documentService.mediumInterval)
        }

      },
      (error) => {
        console.error('Error fetching approvers:', error);
        let err = error?.error?.err
        if (err?.code == "WORKFLOW_TRIGGERED" && err?.err) {
          this.toaster.showWarning('Cannot Approve Document!', 'Approval process already completed', this.documentService.mediumInterval);
        } else if (err?.code != "WORKFLOW_TRIGGERED" && err?.err) {
          this.toaster.showWarning('Error approving document!', err?.msg || 'Kindly try again after sometime', this.documentService.mediumInterval);
        } else {
          this.toaster.showError('Error approving document!', 'Kindly try again after sometime', this.documentService.mediumInterval);
        }
        this.miniLoader = false;
        document.document_triggered_disable = false;

      }
    );
  }

  reject(document: any): void {
    document.document_triggered_disable = true;

    let dialogConfig = {
      width: '30rem',
      minHeight: '40vh',
      data: { type: 'REJECT' },
      disableClose: true,
      panelClass: 'custom-mat-dialog-panel',
    };
    this.rejectRef = this.dialog.open(this.rejectDialog, dialogConfig);

    this.rejectRef.afterClosed().subscribe(result => {
      if (result && result == 'REJECT_CANCEL') {
        document.document_triggered_disable = false;
      }
      if (result && result == 'CAN_REJECT') {
        console.log('Comment:', this.commentText);

        let details = {
          workflowId: document.workflow_header_id,
          approval_status: 'R',
          document_id: document.document_id,
          comments: this.rejectForm.get('commentText')?.value,
          app_reference_id: this.applicationReferenceId,
          app_id: this.appId,
        };

        this.documentService.approveOrReject(details).subscribe(
          (res: any) => {
            console.log('Approvers:', res);
            if (res) {
              this.miniLoader = false;
              document.document_status = 4;
              document.document_approval_status = 'Rejected';
              document.document_triggered = true;
              document.document_triggered_disable = false;

              this.toaster.showSuccess('Document Rejected!', '', this.documentService.mediumInterval);
            }
          },
          (error) => {
            console.error('Error fetching approvers:', error);
            let err = error?.error?.err
            if (err?.code == "WORKFLOW_TRIGGERED" && err?.err) {
              this.toaster.showWarning('Cannot Reject Document!', 'Approval process already completed', this.documentService.mediumInterval);
            } else if (err?.code != "WORKFLOW_TRIGGERED" && err?.err) {
              this.toaster.showWarning('Error rejecting document!', err?.msg || 'Kindly try again after sometime', this.documentService.mediumInterval);
            } else {
              this.toaster.showError('Error rejecting document!', 'Kindly try again after sometime', this.documentService.mediumInterval);
            }
            this.miniLoader = false;
            document.document_triggered_disable = false;
            document.document_triggered = false;
          }
        );
      }
    });
  }

  async submit(document: any) {
    // document.document_triggered_send = true;
    document.isApprovalRequired = true;
    let documentInfo = [{
      _id: document._id,
      document_type_id: document.document_type_id,
      document_type: document.document_type,
      reviewersList: document.reviewer_list,
      isApprovalRequired: true,
      file_format: document.file_format,
      cdn_link: document.cdn_link,
      file_name: document.file_name,
      is_existing: true
    }]

    this.openPopUp(documentInfo);

    // this.opportunityService.updateDocumentTypes(documentInfo).subscribe(
    //   response => {
    //     const isApprover = document.reviewer_list.some(({ associate_id, oid }) =>
    //       associate_id === this.user_aid && oid === this.user_oid
    //     );

    //     Object.assign(document, {
    //       document_status: 2,
    //       document_approval_status: 'Under Review',
    //       isApprover: isApprover,
    //       document_triggered_send: true
    //     });
    //     this.toaster.showSuccess('Document Submitted!', '', this.documentService.mediumInterval);
    //   },

    //   error => {
    //     console.error('API Error:', error);
    //     document.document_triggered_send = false;
    //     this.toaster.showError('Error submitting document!', 'Kindly try again after sometime', this.documentService.mediumInterval);
    //   }
    // );
  }

  downloadFile(row) {
    this._sharedService.getDownloadUrl(row.cdn_link).subscribe((res: any) => {
      if (!res['err'])
        window.open(res['data']);
      else {
        this.toaster.showError("Unable to download file", '', this.documentService.mediumInterval);
      }
    })
  }

  isSubmitterButtonVisible(header: any, document: any): boolean {
    return header.key_name === 'submitter_buttons' &&
      header.isActive &&
      !document.isApprover &&
      document.document_status === 1 &&
      document.reviewer_list.length > 0 &&
      document.uploaded_by === this.user_oid &&
      !this.isDeletedFilterActive && this.approverEnabled;
  }

  /**
   * Filters the menu list based on user permissions, file format, and viewer settings.
   *
   * Conditions:
   * 1. **Only Active Items**: Exclude menu items where `is_active` is `false`.
   * 2. **Restrict Delete Option**: If the current user (`user_oid`) is not the uploader (`uploaded_by`),
   *    the "Delete" action is removed.
   * 3. **Restrict View Option**:
   *    - If `openInNewTabMozillaViewer` is `true`, allow "View" only for PDFs and images (`png`, `jpg`, `jpeg`).
   *    - Otherwise, allow "View" for PDFs and images by default.
   *
   * @param document - The document object containing file details.
   * @returns Filtered menu list based on conditions.
   */
  getFilteredMenuList(document) {
    return this.menuList.filter(item =>
      item.is_active &&

      !(this.user_oid !== document.uploaded_by && item.action === 'delete') &&

      !(
        item.action === 'view' &&
        this.attachmentConfig?.data?.viewer_option?.openInNewTabMozillaViewer &&
        !(this.documentService.isImageFormat(document.file_format) || document.file_format === "pdf")
      )
    );
  }

  filter() {
    this._filterService.openFilterLandingPage(Number(this.appId), this.internal_application_id, this.filterData);
  }

  async applyFilter() {
    let filter = await this.getUserFilterConfig();

    if (filter && filter !== '' && filter != null) {
      let filterVal = filter && filter['filterConfig'] && filter['filterConfig']['filterData'] ? filter['filterConfig']['filterData'] : [];
      this.appliedFilter = filterVal.length;
      let query = this._filterService.generateConditionBasedQuery(filterVal);

      this.filterConfig['filter'] = filterVal;
      this.filterConfig['condition_query'] = query ? query : ''
    }
  }

  /**
 * @description Fetching the User Filter Config
 */
  getUserFilterConfig() {
    return new Promise((resolve, reject) => {
      this._filterService.getFilterUserConfig(Number(this.appId),
        'opportunity_document_manager').pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {

            if (res['messType'] == 'S') {
              resolve(res['data'])
            }
            else {
              resolve([])
            }
          },
          error: (err) => {
            this.toaster.showError('Error in Fetching User Filter Config', '', 3000);
            resolve([]);
          }
        });
    });
  }

  handleClick(key: string, doc: any): void {
    if (this.isDeletedFilterActive) return;
    if (key === 'file_name') this.viewFile(doc, 'INLINE');
    else this.openDetailedApprovalPage(doc);
  }

  ngOnDestroy() {
    if (this.filterSubscription$) {
      this.filterSubscription$.unsubscribe();
    }
  }

}
