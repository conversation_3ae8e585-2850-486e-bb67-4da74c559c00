.request-status{
    font-family: 'Plus Jakarta Sans' !important;
    .timeline{
        padding-left: 43px;
        height: 84%;
        width: 93%;
        position: absolute;
        overflow: scroll;
    }
    .content-item{
        display: block;
        width: auto;
        height: 16px;
       
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #526179;
        margin-bottom:-9px;
    }
    activityButton{
        margin: 4px;
        padding: 4px;
        border-radius: 4px;
        display: inline;
        background-color: white;
        color: #526179;
        border-color: #526179;
        border-width: 1.5px;
        font-style: normal;
        font-weight: 700;
        font-size: 10px;
        line-height: 16px;
        /* padding-top: 13px; */
        margin-top: 18px;
        margin-left: 20px;
      }
    .content-subItem{
        width: auto;
        height: 8px;
       
        font-style: normal;
        font-weight: 400;
        font-size: 8px;
        /* line-height: 8px; */
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #8B95A5;
    }
    .Ocircle{
        left: 30px;
        margin-top: -2px;
        height: 29px;
        width: 27px;
        /* background: white; */
        border-radius: 50%;
        border-width: thick;
        display: flex;
        align-items: center;
        border: 10px solid white;
        position: absolute;
    }
    .circle{
        position: absolute;
        left: -4px;
        margin-top: 0px;
        height: 15px;
        width: 15px;
        background: white;
        border-radius: 50%;
        border-width: thick;
        display: inline-block;
    }
    .line{  
        margin-left: 7px;
        position: absolute;
        width: 12px;
        left: 13px;
        height: 27px;
        display: inline-block;
        margin-top: -25px;
        border-left: 1.9px solid #DADCE2;
    }
    .title{
        font-family: "Plus Jakarta Sans";// Zifo theme
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        text-transform: capitalize;
        color: #45546E;
        margin-bottom: -5px;
        padding-top: 0px;
        width: 116px;
    }
    .sub-title{
        font-family: "Plus Jakarta Sans";// Zifo theme
        font-style: normal;
        font-weight: 500;
        font-size: 10px;
        line-height: 16px;
        text-transform: capitalize;
        padding-bottom: 15px;
        margin-bottom: 0px;
    }
    .circle-item{
        font-size: 11px;
        position: absolute;
        top: 1.9px;
        left: 2px;
        color: #fff;
        font-weight: bold;
    }
    .arrowIcon{
        color: #111434;
        font-size: 19px;
        padding-top: 4px;
        padding-left: 34px;
        cursor: pointer;
    }
    .status_badge{
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 4px 8px;
        gap: 8px;
        height: 22px;
        width: auto;
        color: white;
        border-radius: 4px;
        /* width: 51px; */
        /* height: 16px; */
       
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #FFFFFF;
    }
    .res-img{
        width: 45px;
        height: 45px;
        border-radius: 24px;
        margin-left: -6px;
    }
    .res-item{
        display: block;
        width: auto;
        height: 24px;
        font-family: "Plus Jakarta Sans";// Zifo theme
        font-style: normal;
        font-weight: 700;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #45546E;
    }
    .res-subItem{
        width: 71px;
        height: 16px;
        font-family: "Plus Jakarta Sans";// Zifo theme
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        color: #526179;
    }
    .colName{
        width: auto;
        height: 16px;
        font-family: "Plus Jakarta Sans";// Zifo theme
        font-style: normal;
        font-weight: 400;
        font-size: 10px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: uppercase;
        color: #B9C0CA;
        margin-bottom: 0px;
    }
    .colValue{
        width: auto;
        height: 16px;
        font-family: "Plus Jakarta Sans";// Zifo theme
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #526179;
        margin-bottom: 0px;
    }
    .OuterCircle{
        padding-left: 18px;
        margin-bottom: 6px;
    }
    .confirmButton{
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 4px 8px;
        gap: 8px;
        border-radius: 4px;
        width: auto;
        height: 25px;
        margin-right: 8px;
       
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        line-height: 16px;
        align-items: center;
        letter-spacing: -0.02em;
        text-transform: capitalize;
        margin-top: 9px;
        margin-bottom: 3px;
        margin-left: -12px;
        color: #FFFFFF; 
        border: none;
        //background: #EE4961;
        background:#79BA44; // Zifo theme
        font-family: 'Plus Jakarta Sans';// Zifo theme

    }
    .circle-item-content{
        font-size: 11px;
        position: absolute;
        top: 2.9px;
        left: 2px;
        color: #fff;
        font-weight: bold;
    }
    .circle-content{
        position: absolute;
        left: 77px;
        margin-top: 1px;
        height: 15px;
        width: 15px;
        background: white;
        border-radius: 50%;
        border-width: thick;
        display: inline-block;
    }
    .colCriteria{
        margin-left: 40px;
       
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        /* identical to box height, or 133% */

        letter-spacing: 0.02em;
        text-transform: capitalize;

        /* Blue Grey/80 */

        color: #5F6C81;

    }
    .rejection{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 8px;
        gap: 8px;
        width: 789px;
        height: 80px;
        /* Palette/10/Red */
        background: #FFEBEC;
        /* Palette/100/Red */
        border: 1px solid #FF3A46;
        border-radius: 4px;
       
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        /* or 133% */


        /* Black/90 */

        color: #272A47;
        /* Inside auto layout */

        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        margin-top: 8px
    }

    .itemHead {
        font-style: normal;
        font-weight: 400;
        font-size: 10px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: uppercase;
        color: #b9c0ca;
        display: block;
      }
      .itemSub {
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #526179;
      }
      .infoOverlay {
        font-size: small;
        color: #b9c0ca;
        padding-top: 1px;
        margin-left: 3px;
        margin-top: 0px;
      }
      .material-symbols-outlined {
        font-variation-settings: "FILL" 0, "wght" 400, "GRAD" 0, "opsz" 48;
      }
      .notes {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 8px;
        gap: 8px;
        width: 536px;
        min-height: 80px;
        /* Palette/10/Red */
        // background: #FFEBEC;
        background: #f7f9fb;
        /* Palette/100/Red */
        border: 1px solid #ff3a46;
        border-radius: 4px;
    
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        /* or 133% */
    
        /* Black/90 */
    
        color: #272a47;
        /* Inside auto layout */
    
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        margin-top: 8px;
        border: 1px solid #e8e9ee;
        border-radius: 4px;
        margin-bottom: 12px;
      }
      .noteHead {
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        /* identical to box height, or 133% */
    
        letter-spacing: 0.02em;
        text-transform: capitalize;
    
        /* Black/100 */
    
        color: #111434;
      }
      .noteContent {
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        max-width: 500px;
        /* or 133% */
    
        /* Black/90 */
    
        color: #272a47;
      }
}