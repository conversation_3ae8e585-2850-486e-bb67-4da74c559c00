import { Component, EventEmitter, NgModule, OnInit, Output } from '@angular/core';
import { VoluntarySummaryComponent } from 'src/app/modules/exit-dashboard/components/voluntary-summary/voluntary-summary.component';
@Component({
  selector: 'app-request-status',
  templateUrl: './request-status.component.html',
  styleUrls: ['./request-status.component.scss']
})
export class RequestStatusComponent implements OnInit {
  // data: any = [
  //   {
  //       item_id: 0,
  //       item_title: "Resource Allocated",
  //       sub_title: "Awaiting Confirmation",
	//   sub_title_color: "#FFBD3D",
  //       status_badge: "Available",
  //       allocated_count:1,
  //       request_status:"Awaiting",
  //       sub_content: {
  //           resource_name: "Devon Lane",
  //           oid: "",
  //           associate_id: 1234,
  //           designation: "dev",
  //           status:"Available",
  //           current_project: "KTERN",
  //           years_of_exp: 12,
  //           start_date: '12-11-2022',
  //           end_date:'12-12-2022',
  //           utilization_capacity:'100%',
  //           booking_type:'Hard Booking'
  //       },
  //       is_collapsed: true
  //   },
  //   {
  //       item_id: 1,
  //       item_title: "Resource Rejected",
  //       sub_title: "09 Oct 2022",
  //       sub_title_color: "#FF3A46",
  //       status_badge: "Rejected",
  //       allocated_count:1,
  //       request_status:"Rejected",
  //       sub_content: {
  //         resource_name: "Devon Lane",
  //         oid: "",
  //         associate_id: 1234,
  //         status:"Available",
  //         designation: "dev",
  //         current_project: "KTERN",
  //         years_of_exp: 12,
  //         start_date: '12-11-2022',
  //         end_date:'12-12-2022',
  //         utilization_capacity:'100%',
  //         booking_type:'Hard Booking'
  //     },
  //       is_collapsed: true
  //   },
  //   {
  //     item_id: 2,
  //     item_title: "Resource Allocated",
  //     sub_title: "09 Oct 2022",
  //     sub_title_color: "#FFBD3D",
  //     status_badge: null,
  //     allocated_count:2,
  //     request_status:"Awaiting",
  //     sub_content: null,
  //     is_collapsed: false
  //   },
  //   {
  //     item_id: 3,
  //     item_title: "Finalise Resource",
  //     sub_title: "09 Oct 2022",
  //     sub_title_color: "#45546E",
  //     status_badge: "Accepted",
  //     allocated_count:1,
  //     request_status:"Accepted",
  //     sub_content: null,
  //     is_collapsed: false
  //   },
  //   ];
  data:any;
  reqId : number;
  graph:any;
  summaryData:any;
  filterValue:any;
  timeLineLoader:boolean = false;
  len:number;
  is_final_resource:boolean=false;
  subs = new SubSink();
  requestData:any;
  constructor(
    private _router:Router,
    private _rmService:RmService,
    private _toaster: ToasterService,
    private _rmReqDetailService: RmRequestDetailService,
    public dialog: MatDialog,
  ) { }

  async ngOnInit() {
    this.reqId = parseInt(this._router.url.split('/')[5]);
    this.data = await this.getAllocatedResourceActionListStatus();
    await this.patchStatus();
    this.len = this.data.action_items.length;
    this.timeLineLoader = true;
    // console.log("criteria:",this.data.sub_content.criteria)
    // console.log("criteria length:",this.data.sub_content.criteria.length)
  }
  Collapse=(index:number)=>{
    console.log("Collapse Called!!!", index);
    for(var i = 0; i < this.data.action_items.length; i++){
      if(i == index){
        this.data.action_items[i].is_collapsed = true;
      }
    }
  }
  Expand=(index:number)=>{
    console.log("Expand Called!!!",index);
    for(var i = 0; i < this.data.action_items.length; i++){
      if(i == index){
        this.data.action_items[i].is_collapsed = false;
      }
    }
  }
  Pending = ()=>{
    this.is_final_resource = true;
  }
  getAllocatedResourceActionListStatus=()=>{
    let request_id = this.reqId
    // let request_id = 3;
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService
        .allocatedResourceActionListStatus(request_id)
        .subscribe(
          (res: any) => {
            if (res.err == false) {
              res['data'] = this.utcDateFormat(res['data'])
              resolve(res.data);
            }
            else reject(res);
          },
          (err) => {
            if(err.error.user_msg)
              this._toaster.showWarning("Error",err.error.msg)
            console.log(err);
            reject(err);
          }
        );
    });
  }

  utcDateFormat(data) {
    for(let item of data['action_items']) {
      if(item['sub_content'] && item['sub_content']['allocated_start_date']) {
        item['sub_content']['allocated_start_date'] = 
        moment(item['sub_content']['allocated_start_date']).utc().format('DD-MMM-YYYY')
      }
      if(item['sub_content'] && item['sub_content']['allocated_end_date']) {
        item['sub_content']['allocated_end_date'] = 
          moment(item['sub_content']['allocated_end_date']).utc().format('DD-MMM-YYYY')
      } 
    }
    return data
  }

  allocateNewResource(){
    // this._router.navigateByUrl('main/resource-management/home/<USER>/'+this.reqId+'/resources');
    this._router.navigateByUrl(this._rmService.getRouterLink("resourceAllocation",this.reqId))
    this._rmService.activeLink = 'resources';
    console.log(this._rmService.activeLink);
  }
  routeProjectDetails(associateId) {
    if (associateId) {
      return new Promise((resolve, reject) => {
        this.subs.sink = this._rmReqDetailService
          .getEmployeeProjectDetails(associateId)
          .subscribe(
            (res: any) => {
              this.summaryData = res.data; // or whatever relevant data you want to pass
              this.filterValue = 'RMG'; // set your caption accordingly
              this.graph = 'external'; // set your graph data accordingly
              this.openSummary();
  
              resolve(res.data);
            },
            (err) => {
              this._toaster.showError(
                'Error',
                'Failed to retrieve employee!',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      
      });
      
    }
    else{
      this._toaster.showWarning("Error","Associate Id Not Found")
    }
    
  }
  
  openSummary() {
    let data = this.summaryData; // Data to be passed to the dialog
    let caption = this.filterValue; // Caption for the dialog
    let graph = this.graph; // Graph data for the dialog
  
    console.log(data); // Log the data for debugging
  
    this.dialog.open(VoluntarySummaryComponent, {
      data: {
        columnData: data,
        caption: caption,
        graph: graph
      },
      width: '75%', // Dialog width
      height: '75%' // Dialog height
    });
  }

  patchStatus() {
    this.subs.sink = this._rmReqDetailService.getRequestData().subscribe((res: any) => {
      // console.log('LUFFYYYYYYYY',res)
      if(res) {
        // console.log('Status sub',res)
        this.requestData = res
      }
    },(err) => {
      console.log(err)
    })
  }

  skillPopup(data:any){
    let params = {
      associate_id: data.associate_id
    }
    // console.log("Data in Request Status While Calling!!:",data.associate_id,this.reqId);
    let dialogRef = this.dialog.open(SkillScoreComponent, {
      width: '846px',
      height: '400px',
      data:{modalParams:params,request_id:this.reqId}
    });

    dialogRef.afterClosed().subscribe((res) => {
      console.log("Skill Score Closed!!!!");
    })
  }

}



import { CommonModule } from '@angular/common';
import {MatIconModule} from '@angular/material/icon';
import { Router } from '@angular/router';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { SubSink } from 'subsink';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import moment from 'moment';
import { RmRequestDetailService } from '../../../services/rm-request-detail.service';
import { BehaviorSubject } from 'rxjs';
import { SkillScoreComponent } from '../../resources/components/skill-score/skill-score.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
@NgModule({
 declarations: [RequestStatusComponent],
  imports: [
    CommonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    SharedComponentsModule
  ],
  exports: [],
})
export class RequestStatusModule {}
